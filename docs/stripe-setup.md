# Stripe Setup Guide

This guide explains how to set up Stripe for your application.

## 1. Create a Stripe Account

If you don't already have a Stripe account, sign up at [stripe.com](https://stripe.com).

## 2. Get API Keys

1. Go to the [Stripe Dashboard](https://dashboard.stripe.com/apikeys)
2. Copy your **Publishable Key** and **Secret Key**
3. Add these to your `.env.local` file:

```
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_SECRET_KEY=sk_test_your_secret_key
```

## 3. Create Products and Prices

You need to create products and prices in the Stripe Dashboard. Each price will have a unique ID that you'll use in your application.

1. Go to [Products](https://dashboard.stripe.com/products) in your Stripe Dashboard
2. Click **Add Product**
3. Fill in the product details:
   - Name: "Basic Plan"
   - Description: "Perfect for small businesses just getting started"
4. Under Pricing, add a recurring price:
   - Price: $49
   - Billing period: Monthly
5. Click **Save Product**
6. Repeat for each plan (Professional, Business)

## 4. Get Price IDs

After creating your products and prices, you need to get the price IDs:

1. Go to [Products](https://dashboard.stripe.com/products) in your Stripe Dashboard
2. Click on a product to view its details
3. Find the price in the Prices section
4. Copy the price ID (it starts with `price_`)
5. Add these to your `.env.local` file:

```
NEXT_PUBLIC_STRIPE_PRICE_BASIC=price_1234567890abcdef
NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL=price_2345678901abcdef
NEXT_PUBLIC_STRIPE_PRICE_BUSINESS=price_3456789012abcdef
```

## 5. Update Your Code

Make sure your code is using these price IDs:

1. In `src/lib/stripe/plans.ts`, update the `STRIPE_PRICE_IDS` object to use your actual price IDs
2. Make sure you're using the price IDs, not the actual price amounts, when creating checkout sessions

## 6. Test the Integration

1. Use Stripe's test cards to test your integration:
   - Test card number: 4242 4242 4242 4242
   - Expiration date: Any future date
   - CVC: Any 3 digits
   - ZIP: Any 5 digits

## 7. Set Up Webhooks (Optional)

For production, you should set up webhooks to handle events like successful payments:

1. Go to [Webhooks](https://dashboard.stripe.com/webhooks) in your Stripe Dashboard
2. Click **Add Endpoint**
3. Enter your webhook URL (e.g., `https://yourdomain.com/api/stripe/webhook`)
4. Select the events you want to listen for (e.g., `checkout.session.completed`)
5. Click **Add Endpoint**
6. Copy the signing secret and add it to your `.env.local` file:

```
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

## Important Notes

- Always use price IDs (e.g., `price_1234567890abcdef`), not the actual price amounts, when creating checkout sessions
- Use test mode for development and switch to live mode for production
- Keep your secret key secure and never expose it in client-side code
- Use environment variables for all sensitive information
