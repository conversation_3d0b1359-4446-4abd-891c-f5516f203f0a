{"name": "visitor-management-saas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@stripe/stripe-js": "^6.0.0", "@supabase/ssr": "^0.6.0", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-table": "^8.21.2", "@types/uuid": "^10.0.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.482.0", "next": "15.2.2", "qrcode": "^1.5.4", "react": "^19.0.0", "react-datepicker": "^8.2.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "stripe": "^17.7.0", "tailwind-merge": "^3.0.2", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.2", "tailwindcss": "^4", "typescript": "^5"}}