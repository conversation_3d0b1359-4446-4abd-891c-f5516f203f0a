import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createMiddlewareClient } from '@/lib/supabase/middleware'
import { ensureUserExistsMiddleware } from '@/lib/auth/middleware-helpers'

export async function middleware(request: NextRequest) {
  // Get the path from the URL
  const url = new URL(request.url)
  const path = url.pathname

  // Check for redirect loop
  const redirectCount = parseInt(url.searchParams.get('_rc') || '0', 10)
  if (redirectCount > 1) {
    console.log('Possible redirect loop detected, allowing access to break the loop')
    return NextResponse.next()
  }

  // If the URL has a redirect_count parameter, increment it
  if (url.searchParams.has('_rc')) {
    const newCount = redirectCount + 1
    url.searchParams.set('_rc', newCount.toString())
    console.log(`Incrementing redirect count to ${newCount}`)
  }

  // Always allow access to the debug page
  if (path === '/auth-debug') {
    return NextResponse.next()
  }

  // If the URL has the bypass parameter, skip middleware completely
  if (url.searchParams.has('bypass_auth')) {
    console.log('Skipping middleware - bypass parameter detected')
    return NextResponse.next()
  }

  // ONLY check dashboard paths - let the login page handle its own logic
  if (!path.startsWith('/dashboard')) {
    return NextResponse.next()
  }

  try {
    console.log(`Middleware protecting dashboard path: ${path}`)

    // Create a Supabase client for the middleware
    const { supabase, supabaseResponse } = createMiddlewareClient(request)

    // Check session status with Supabase
    const { data: { session } } = await supabase.auth.getSession()

    console.log(`Middleware dashboard check: session exists = ${!!session}`)

    // Redirect to login if no valid session found
    if (!session) {
      console.log('Redirecting to login - no valid session for dashboard')

      // Clear auth redirect attempt to prevent loops
      const redirectUrl = new URL('/login', request.url)
      redirectUrl.searchParams.append('clear_redirect', 'true')

      // Add redirect count to detect loops
      redirectUrl.searchParams.append('_rc', '1')

      return NextResponse.redirect(redirectUrl)
    }

    // Check if the user exists in the public.users table
    try {
      const userExists = await ensureUserExistsMiddleware(supabase, session.user.id)

      if (!userExists) {
        console.log('User authenticated but not in public.users table, redirecting to complete registration')

        // Redirect to signup page
        const redirectUrl = new URL('/signup', request.url)
        redirectUrl.searchParams.append('_rc', '1')
        redirectUrl.searchParams.append('complete_registration', 'true')

        return NextResponse.redirect(redirectUrl)
      }
    } catch (error) {
      console.error('Error checking if user exists:', error)
      // Continue anyway - we don't want to block access
    }

    // User is authenticated and exists in public.users table, allow access to dashboard
    return supabaseResponse

  } catch (err) {
    console.error('Middleware exception:', err)
    // On error, allow access to break potential loops
    return NextResponse.next()
  }
}

// Simplify matcher to only dashboard pages
export const config = {
  matcher: [
    '/dashboard/:path*',
    '/auth-debug'
  ]
}
