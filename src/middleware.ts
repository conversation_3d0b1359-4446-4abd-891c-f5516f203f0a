import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  // Get the path from the URL for debugging
  const url = new URL(request.url)
  const path = url.pathname

  console.log(`[Middleware] Processing request for path: ${path}`)

  // Log cookies for debugging
  const cookies = request.cookies.getAll()
  const supabaseCookies = cookies.filter(cookie => cookie.name.startsWith('sb-'))
  console.log(`[Middleware] Found ${supabaseCookies.length} Supabase cookies:`, supabaseCookies.map(c => c.name))

  // Check for stale cookies and clear them if needed
  let hasStaleSupabaseCookies = false
  for (const cookie of supabaseCookies) {
    if (cookie.value && cookie.value.includes('%')) {
      console.log(`[Middleware] Detected URL-encoded cookie: ${cookie.name}`)
      hasStaleSupabaseCookies = true
      break
    }

    // Try to decode the cookie value to check if it's valid
    try {
      if (cookie.value) {
        atob(cookie.value)
      }
    } catch {
      console.log(`[Middleware] Detected invalid base64 cookie: ${cookie.name}`)
      hasStaleSupabaseCookies = true
      break
    }
  }

  // If we found stale cookies, clear them and redirect to login with a clean state
  if (hasStaleSupabaseCookies && path.startsWith('/dashboard')) {
    console.log(`[Middleware] Clearing stale cookies and redirecting to login`)
    const redirectUrl = request.nextUrl.clone()
    redirectUrl.pathname = '/login'
    redirectUrl.searchParams.set('cookies_cleared', 'true')

    const response = NextResponse.redirect(redirectUrl)

    // Clear all Supabase cookies
    supabaseCookies.forEach(cookie => {
      response.cookies.delete(cookie.name)
    })

    return response
  }

  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  try {
    const {
      data: { user },
      error
    } = await supabase.auth.getUser()

    console.log(`[Middleware] Auth check result:`, {
      hasUser: !!user,
      userId: user?.id,
      userEmail: user?.email,
      error: error?.message
    })

    if (error) {
      console.error(`[Middleware] Error getting user:`, error)
    }

    if (
      !user &&
      !path.startsWith('/login') &&
      !path.startsWith('/auth') &&
      !path.startsWith('/signup') &&
      !path.startsWith('/clear-cookies') &&
      path.startsWith('/dashboard')
    ) {
      // no user, potentially respond by redirecting the user to the login page
      console.log(`[Middleware] No user found, redirecting ${path} to /login`)
      const redirectUrl = request.nextUrl.clone()
      redirectUrl.pathname = '/login'
      return NextResponse.redirect(redirectUrl)
    }

    console.log(`[Middleware] Access granted for ${path}`)

  } catch (authError) {
    console.error(`[Middleware] Exception during auth check:`, authError)

    // If there's an error getting the user and they're trying to access dashboard, redirect to login
    if (path.startsWith('/dashboard')) {
      console.log(`[Middleware] Auth error on dashboard access, redirecting to /login`)
      const redirectUrl = request.nextUrl.clone()
      redirectUrl.pathname = '/login'
      return NextResponse.redirect(redirectUrl)
    }
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
  // creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
