export enum NotificationType {
  VISITOR_PREREGISTERED = 'visitor_preregistered',
  VISITOR_CHECKED_IN = 'visitor_checked_in',
  VISITOR_CHECKED_OUT = 'visitor_checked_out',
  VISIT_CANCELLED = 'visit_cancelled',
  VISIT_RESCHEDULED = 'visit_rescheduled',
  VISIT_REMINDER = 'visit_reminder'
}

export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  IN_APP = 'in_app'
}

export interface NotificationPreference {
  id: string
  user_id: string
  org_id: string
  notification_type: NotificationType
  email_enabled: boolean
  sms_enabled: boolean
  in_app_enabled: boolean
  created_at: string
  updated_at: string
}

export interface Notification {
  id: string
  user_id: string
  org_id: string
  notification_type: NotificationType
  channel: NotificationChannel
  title: string
  message: string
  data: Record<string, unknown>
  is_read: boolean
  created_at: string
  sent_at: string | null
  delivered_at: string | null
  error: string | null
}

export interface NotificationTemplate {
  id: string
  org_id: string
  notification_type: NotificationType
  channel: NotificationChannel
  subject: string
  body: string
  is_default: boolean
  created_at: string
  updated_at: string
}
