import { Database } from './supabase'

// Organization Settings Types
export interface OrganizationSettings {
  id: string
  name: string
  logo_url?: string
  primary_color?: string
  secondary_color?: string
  data_retention_days?: number
  default_language?: string
  timezone?: string
  security_settings?: SecuritySettings
  created_at: string
  updated_at: string
}

export interface SecuritySettings {
  require_2fa: boolean
  password_expiry_days?: number
  session_timeout_minutes?: number
  allowed_ip_ranges?: string[]
  visitor_data_access_level?: 'strict' | 'standard' | 'minimal'
}

// User Management Types
export interface UserRole {
  id: string
  name: string
  description?: string
  permissions: Permission[]
  created_at: string
  updated_at: string
}

export interface Permission {
  resource: string
  action: 'create' | 'read' | 'update' | 'delete' | 'manage'
}

export type UserWithRole = Database['public']['Tables']['users']['Row'] & {
  role: UserRole
  locations?: LocationAssignment[]
}

export interface LocationAssignment {
  location_id: string
  location_name: string
  is_primary: boolean
}

// Location Management Types
export interface Location {
  id: string
  org_id: string
  name: string
  address?: string
  city?: string
  state?: string
  country?: string
  postal_code?: string
  phone?: string
  email?: string
  timezone?: string
  working_hours?: WorkingHours
  settings?: LocationSettings
  parent_id?: string
  location_type: 'headquarters' | 'branch' | 'office' | 'facility' | 'other'
  group_id?: string
  is_active: boolean
  capacity?: number
  created_at: string
  updated_at: string
}

export interface LocationGroup {
  id: string
  org_id: string
  name: string
  description?: string
  color?: string
  created_at: string
  updated_at: string
}

export interface LocationHierarchy extends Location {
  children?: LocationHierarchy[]
  parent?: Location
  group?: LocationGroup
}

export interface WorkingHours {
  monday?: DayHours
  tuesday?: DayHours
  wednesday?: DayHours
  thursday?: DayHours
  friday?: DayHours
  saturday?: DayHours
  sunday?: DayHours
}

export interface DayHours {
  is_open: boolean
  open_time?: string
  close_time?: string
  breaks?: TimeRange[]
}

export interface TimeRange {
  start_time: string
  end_time: string
}

export interface LocationSettings {
  require_host_approval: boolean
  allow_walk_ins: boolean
  require_photo: boolean
  require_id: boolean
  default_agreement_ids?: string[]
  notification_settings?: LocationNotificationSettings
  visitor_policies?: VisitorPolicy[]
  access_restrictions?: AccessRestriction[]
  check_in_methods?: ('self' | 'receptionist' | 'kiosk')[]
  default_check_in_method?: 'self' | 'receptionist' | 'kiosk'
}

export interface VisitorPolicy {
  id: string
  name: string
  description?: string
  rules: PolicyRule[]
  is_active: boolean
}

export interface PolicyRule {
  condition: string
  action: string
  parameters?: Record<string, unknown>
}

export interface AccessRestriction {
  type: 'time' | 'date' | 'visitor_type' | 'custom'
  rule: string
  message: string
  is_active: boolean
}

export interface LocationNotificationSettings {
  notify_on_check_in: boolean
  notify_on_check_out: boolean
  notify_on_pre_registration: boolean
  default_notification_channels?: ('email' | 'sms' | 'in_app')[]
}

// Custom Fields Types
export interface CustomField {
  id: string
  org_id: string
  name: string
  label: string
  type: 'text' | 'number' | 'date' | 'time' | 'datetime' | 'select' | 'multiselect' | 'checkbox' | 'radio' | 'textarea'
  entity_type: 'visitor' | 'visit' | 'host' | 'location'
  options?: string[]
  is_required: boolean
  is_visible: boolean
  validation_rules?: ValidationRule[]
  default_value?: string
  placeholder?: string
  help_text?: string
  order: number
  created_at: string
  updated_at: string
}

export interface ValidationRule {
  type: 'min' | 'max' | 'regex' | 'email' | 'url' | 'custom'
  value?: string | number
  message: string
}

// Workflow Types
export interface Workflow {
  id: string
  org_id: string
  name: string
  description?: string
  trigger: WorkflowTrigger
  conditions?: WorkflowCondition[]
  actions: WorkflowAction[]
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface WorkflowTrigger {
  event: 'pre_registration' | 'check_in' | 'check_out' | 'visit_approved' | 'visit_rejected' | 'visit_cancelled' | 'scheduled'
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly'
    time?: string
    day_of_week?: number
    day_of_month?: number
  }
}

export interface WorkflowCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'in' | 'not_in' | 'is_empty' | 'is_not_empty'
  value?: string | number | boolean | string[] | number[] | null
}

export interface WorkflowAction {
  type: 'notification' | 'email' | 'sms' | 'webhook' | 'update_field' | 'create_record' | 'custom'
  config: Record<string, unknown>
}

// Branding Types
export interface BrandingSettings {
  id: string
  org_id: string
  logo_url?: string
  favicon_url?: string
  primary_color?: string
  secondary_color?: string
  accent_color?: string
  font_family?: string
  email_templates?: EmailTemplates
  landing_page_settings?: LandingPageSettings
  created_at: string
  updated_at: string
}

export interface EmailTemplates {
  invitation?: EmailTemplate
  pre_registration_confirmation?: EmailTemplate
  visitor_check_in_notification?: EmailTemplate
  visitor_check_out_notification?: EmailTemplate
}

export interface EmailTemplate {
  subject: string
  body: string
  is_html: boolean
}

export interface LandingPageSettings {
  welcome_message?: string
  background_image_url?: string
  show_logo: boolean
  custom_css?: string
}
