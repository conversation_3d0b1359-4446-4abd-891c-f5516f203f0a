// This is a placeholder for the Supabase database types
// In a real project, you would generate these types using the Supabase CLI
// Example: npx supabase gen types typescript --project-id your-project-id > src/types/supabase.ts

export interface Database {
  public: {
    Tables: {
      host_statuses: {
        Row: {
          id: string
          user_id: string
          status: string
          message: string | null
          duration_type: string
          end_time: string | null
          end_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          status: string
          message?: string | null
          duration_type: string
          end_time?: string | null
          end_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          status?: string
          message?: string | null
          duration_type?: string
          end_time?: string | null
          end_date?: string | null
          updated_at?: string
        }
      }
      host_status_history: {
        Row: {
          id: string
          user_id: string
          status: string
          message: string | null
          duration_type: string
          end_time: string | null
          end_date: string | null
          created_at: string
          ended_at: string
        }
        Insert: {
          id?: string
          user_id: string
          status: string
          message?: string | null
          duration_type: string
          end_time?: string | null
          end_date?: string | null
          created_at?: string
          ended_at: string
        }
        Update: {
          id?: string
          user_id?: string
          status?: string
          message?: string | null
          duration_type?: string
          end_time?: string | null
          end_date?: string | null
          ended_at?: string
        }
      }
      calendar_integrations: {
        Row: {
          id: string
          user_id: string
          provider: string
          is_active: boolean
          sync_frequency: string
          last_synced_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          provider: string
          is_active?: boolean
          sync_frequency: string
          last_synced_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          provider?: string
          is_active?: boolean
          sync_frequency?: string
          last_synced_at?: string | null
          updated_at?: string
        }
      }
      calendar_events: {
        Row: {
          id: string
          user_id: string
          calendar_integration_id: string
          external_event_id: string
          title: string
          description: string | null
          location: string | null
          start_time: string
          end_time: string
          is_all_day: boolean
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          calendar_integration_id: string
          external_event_id: string
          title: string
          description?: string | null
          location?: string | null
          start_time: string
          end_time: string
          is_all_day?: boolean
          status: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          calendar_integration_id?: string
          external_event_id?: string
          title?: string
          description?: string | null
          location?: string | null
          start_time?: string
          end_time?: string
          is_all_day?: boolean
          status?: string
          updated_at?: string
        }
      }
      agreements: {
        Row: {
          id: string
          org_id: string
          name: string
          content: string
          is_required: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          org_id: string
          name: string
          content: string
          is_required: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          org_id?: string
          name?: string
          content?: string
          is_required?: boolean
          updated_at?: string
        }
      }
      badges: {
        Row: {
          id: string
          org_id: string
          name: string
          template: Record<string, unknown>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          org_id: string
          name: string
          template: Record<string, unknown>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          org_id?: string
          name?: string
          template?: Record<string, unknown>
          updated_at?: string
        }
      }
      organizations: {
        Row: {
          id: string
          name: string
          logo_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          logo_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          logo_url?: string | null
          updated_at?: string
        }
      }
      users: {
        Row: {
          id: string
          org_id: string
          email: string
          full_name: string
          role: string
          is_admin: boolean
          created_at: string
        }
        Insert: {
          id?: string
          org_id: string
          email: string
          full_name: string
          role: string
          is_admin?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          org_id?: string
          email?: string
          full_name?: string
          role?: string
          is_admin?: boolean
        }
      }
      locations: {
        Row: {
          id: string
          org_id: string
          name: string
          address: string
          created_at: string
        }
        Insert: {
          id?: string
          org_id: string
          name: string
          address: string
          created_at?: string
        }
        Update: {
          id?: string
          org_id?: string
          name?: string
          address?: string
        }
      }
      visitors: {
        Row: {
          id: string
          org_id: string
          full_name: string
          email: string
          phone: string
          company: string
          photo_url: string | null
          custom_fields: Record<string, unknown> | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          org_id: string
          full_name: string
          email: string
          phone: string
          company: string
          photo_url?: string | null
          custom_fields?: Record<string, unknown> | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          org_id?: string
          full_name?: string
          email?: string
          phone?: string
          company?: string
          photo_url?: string | null
          custom_fields?: Record<string, unknown> | null
          updated_at?: string
        }
      }
      visits: {
        Row: {
          id: string
          org_id: string
          location_id: string
          host_id: string
          visitor_id: string
          purpose: string
          scheduled_time: string | null
          check_in_time: string | null
          check_out_time: string | null
          status: string
          badge_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          org_id: string
          location_id: string
          host_id: string
          visitor_id: string
          purpose: string
          scheduled_time?: string | null
          check_in_time?: string | null
          check_out_time?: string | null
          status: string
          badge_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          org_id?: string
          location_id?: string
          host_id?: string
          visitor_id?: string
          purpose?: string
          scheduled_time?: string | null
          check_in_time?: string | null
          check_out_time?: string | null
          status?: string
          badge_id?: string | null
          updated_at?: string
        }
      }
      visit_agreements: {
        Row: {
          id: string
          visit_id: string
          agreement_id: string
          signed_at: string | null
          signature: string | null
          created_at: string
        }
        Insert: {
          id?: string
          visit_id: string
          agreement_id: string
          signed_at?: string | null
          signature?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          visit_id?: string
          agreement_id?: string
          signed_at?: string | null
          signature?: string | null
        }
      }
      subscriptions: {
        Row: {
          id: string
          org_id: string
          stripe_subscription_id: string
          stripe_customer_id: string
          plan_id: string
          status: string
          current_period_end: string
          created_at: string
        }
        Insert: {
          id?: string
          org_id: string
          stripe_subscription_id: string
          stripe_customer_id: string
          plan_id: string
          status: string
          current_period_end: string
          created_at?: string
        }
        Update: {
          id?: string
          org_id?: string
          stripe_subscription_id?: string
          stripe_customer_id?: string
          plan_id?: string
          status?: string
          current_period_end?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
