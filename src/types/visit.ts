import { Database } from './supabase';

export type VisitRow = Database['public']['Tables']['visits']['Row'];
export type VisitInsert = Database['public']['Tables']['visits']['Insert'];
export type VisitUpdate = Database['public']['Tables']['visits']['Update'];

export type VisitorRow = Database['public']['Tables']['visitors']['Row'];
export type VisitorInsert = Database['public']['Tables']['visitors']['Insert'];
export type VisitorUpdate = Database['public']['Tables']['visitors']['Update'];

export type VisitStatus = 'pending' | 'approved' | 'rejected' | 'checked_in' | 'checked_out' | 'cancelled' | 'no_show';

export interface VisitRequest {
  id: string;
  visitor_id: string;
  host_id: string;
  location_id: string;
  purpose: string;
  start_time: string;
  end_time: string;
  status: VisitStatus;
  notes?: string;
  created_at: string;
  updated_at: string;
  visitor_name: string;
  visitor_email: string;
  visitor_phone?: string;
  visitor_company?: string;
  host_name: string;
  host_email: string;
  location_name: string;
}

export interface VisitApproval {
  id: string;
  visit_id: string;
  approver_id: string;
  status: 'pending' | 'approved' | 'rejected';
  notes?: string;
  created_at: string;
  updated_at: string;
  approver_name: string;
  approver_email: string;
}

export interface VisitApprovalRequest {
  visit_id: string;
  approver_id: string;
  notes?: string;
}

export interface VisitApprovalResponse {
  visit_id: string;
  approver_id: string;
  status: 'approved' | 'rejected';
  notes?: string;
}

export interface VisitRequestFilter {
  host_id?: string;
  visitor_id?: string;
  location_id?: string;
  status?: VisitStatus | VisitStatus[];
  start_date?: string;
  end_date?: string;
  search?: string;
}

export interface VisitStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  checked_in: number;
  checked_out: number;
  cancelled: number;
  no_show: number;
}
