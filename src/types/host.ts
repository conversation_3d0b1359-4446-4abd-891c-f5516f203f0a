import { Database } from './supabase';

export type HostStatus = 'available' | 'busy' | 'out_of_office' | 'in_meeting' | 'do_not_disturb';

export interface HostStatusDuration {
  type: 'indefinite' | 'until_time' | 'all_day';
  endTime?: string; // ISO string for until_time
  endDate?: string; // ISO string for all_day
}

export interface HostStatusData {
  id: string;
  user_id: string;
  status: HostStatus;
  message?: string;
  duration: HostStatusDuration;
  created_at: string;
  updated_at: string;
}

export interface HostStatusHistory {
  id: string;
  user_id: string;
  status: HostStatus;
  message?: string;
  duration: HostStatusDuration;
  created_at: string;
  ended_at: string;
}

export interface CalendarIntegration {
  id: string;
  user_id: string;
  provider: 'google' | 'outlook' | 'ical';
  is_active: boolean;
  sync_frequency: 'realtime' | 'hourly' | 'daily';
  last_synced_at?: string;
  created_at: string;
  updated_at: string;
}

export interface CalendarEvent {
  id: string;
  user_id: string;
  calendar_integration_id: string;
  external_event_id: string;
  title: string;
  description?: string;
  location?: string;
  start_time: string;
  end_time: string;
  is_all_day: boolean;
  status: 'confirmed' | 'tentative' | 'cancelled';
  created_at: string;
  updated_at: string;
}

// Database types
export type HostStatusRow = Database['public']['Tables']['host_statuses']['Row'];
export type HostStatusInsert = Database['public']['Tables']['host_statuses']['Insert'];
export type HostStatusUpdate = Database['public']['Tables']['host_statuses']['Update'];

export type HostStatusHistoryRow = Database['public']['Tables']['host_status_history']['Row'];
export type HostStatusHistoryInsert = Database['public']['Tables']['host_status_history']['Insert'];

export type CalendarIntegrationRow = Database['public']['Tables']['calendar_integrations']['Row'];
export type CalendarIntegrationInsert = Database['public']['Tables']['calendar_integrations']['Insert'];
export type CalendarIntegrationUpdate = Database['public']['Tables']['calendar_integrations']['Update'];

export type CalendarEventRow = Database['public']['Tables']['calendar_events']['Row'];
export type CalendarEventInsert = Database['public']['Tables']['calendar_events']['Insert'];
export type CalendarEventUpdate = Database['public']['Tables']['calendar_events']['Update'];
