import { Database } from './supabase'

// Supabase types
export type Tables = Database['public']['Tables']
export type TablesInsert = {
  [K in keyof Tables]: Tables[K]['Insert']
}
export type TablesUpdate = {
  [K in keyof Tables]: Tables[K]['Update']
}
export type TablesRow = {
  [K in keyof Tables]: Tables[K]['Row']
}

// Organization types
export type Organization = TablesRow['organizations']
export type OrganizationInsert = TablesInsert['organizations']
export type OrganizationUpdate = TablesUpdate['organizations']

// User types
export type User = TablesRow['users']
export type UserInsert = TablesInsert['users']
export type UserUpdate = TablesUpdate['users']

// Location types
export type Location = TablesRow['locations']
export type LocationInsert = TablesInsert['locations']
export type LocationUpdate = TablesUpdate['locations']

// Visit types
export type Visit = TablesRow['visits']
export type VisitInsert = TablesInsert['visits']
export type VisitUpdate = TablesUpdate['visits']

// Visitor types
export type Visitor = TablesRow['visitors']
export type VisitorInsert = TablesInsert['visitors']
export type VisitorUpdate = TablesUpdate['visitors']

// Agreement types
export type Agreement = TablesRow['agreements']
export type AgreementInsert = TablesInsert['agreements']
export type AgreementUpdate = TablesUpdate['agreements']

// Visit agreement types
export type VisitAgreement = TablesRow['visit_agreements']
export type VisitAgreementInsert = TablesInsert['visit_agreements']
export type VisitAgreementUpdate = TablesUpdate['visit_agreements']

// Badge template types
export type BadgeTemplate = TablesRow['badges']
export type BadgeTemplateInsert = TablesInsert['badges']
export type BadgeTemplateUpdate = TablesUpdate['badges']

// Badge template field types
export enum BadgeFieldType {
  TEXT = 'text',
  IMAGE = 'image',
  VISITOR_NAME = 'visitor_name',
  VISITOR_COMPANY = 'visitor_company',
  VISITOR_EMAIL = 'visitor_email',
  VISITOR_PHOTO = 'visitor_photo',
  HOST_NAME = 'host_name',
  VISIT_DATE = 'visit_date',
  VISIT_PURPOSE = 'visit_purpose',
  QR_CODE = 'qr_code',
  BARCODE = 'barcode',
  ORGANIZATION_LOGO = 'organization_logo',
  ORGANIZATION_NAME = 'organization_name',
  LOCATION_NAME = 'location_name',
}

export interface BadgeField {
  id: string;
  type: BadgeFieldType;
  x: number; // Position X (percentage of badge width)
  y: number; // Position Y (percentage of badge height)
  width: number; // Width (percentage of badge width)
  height: number; // Height (percentage of badge height)
  value?: string; // For text fields
  fontSize?: number; // For text fields
  fontColor?: string; // For text fields
  fontWeight?: 'normal' | 'bold'; // For text fields
  alignment?: 'left' | 'center' | 'right'; // For text fields
  rotation?: number; // Rotation in degrees
}

export interface BadgeTemplateData {
  name: string;
  width: number; // Width in mm
  height: number; // Height in mm
  backgroundColor: string;
  borderColor?: string;
  borderWidth?: number;
  fields: BadgeField[];
  isDefault?: boolean;
}

// Subscription types
export type Subscription = TablesRow['subscriptions']
export type SubscriptionInsert = TablesInsert['subscriptions']
export type SubscriptionUpdate = TablesUpdate['subscriptions']

// Subscription plan types
export interface SubscriptionPlan {
  id: string
  name: string
  description: string
  price: number
  features: string[]
  visitorLimit: number
  locationLimit: number
  userLimit: number
  customFields: boolean
  analytics: boolean
  apiAccess: boolean
}

// Visit status enum
export enum VisitStatus {
  SCHEDULED = 'scheduled',
  CHECKED_IN = 'checked_in',
  CHECKED_OUT = 'checked_out',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show',
}

// User role enum
export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  HOST = 'host',
  RECEPTIONIST = 'receptionist',
}

// Export notification types
export * from './notification'
