import { NotificationType } from '@/types'
import { createServerNotificationService } from './server'
import { Visit, Visitor, User } from '@/types'

/**
 * Send a notification when a visitor is pre-registered
 */
export async function notifyVisitorPreRegistered(
  visit: Visit,
  visitor: Visitor,
  hostId: string
): Promise<void> {
  const notificationService = await createServerNotificationService()

  await notificationService.sendNotification({
    userId: hostId,
    orgId: visit.org_id,
    notificationType: NotificationType.VISITOR_PREREGISTERED,
    title: 'New Visitor Pre-registered',
    message: `${visitor.full_name} from ${visitor.company || 'N/A'} has been pre-registered for a visit on ${formatDate(visit.scheduled_time)}.`,
    data: {
      visitId: visit.id,
      visitorId: visitor.id,
      visitorName: visitor.full_name,
      visitorCompany: visitor.company,
      scheduledTime: visit.scheduled_time,
      purpose: visit.purpose
    }
  })
}

/**
 * Send a notification when a visitor checks in
 */
export async function notifyVisitorCheckedIn(
  visit: Visit,
  visitor: Visitor,
  hostId: string
): Promise<void> {
  const notificationService = await createServerNotificationService()

  await notificationService.sendNotification({
    userId: hostId,
    orgId: visit.org_id,
    notificationType: NotificationType.VISITOR_CHECKED_IN,
    title: 'Visitor Checked In',
    message: `${visitor.full_name} from ${visitor.company || 'N/A'} has checked in for your meeting. Purpose: ${visit.purpose || 'N/A'}.`,
    data: {
      visitId: visit.id,
      visitorId: visitor.id,
      visitorName: visitor.full_name,
      visitorCompany: visitor.company,
      checkInTime: visit.check_in_time,
      purpose: visit.purpose
    }
  })
}

/**
 * Send a notification when a visitor checks out
 */
export async function notifyVisitorCheckedOut(
  visit: Visit,
  visitor: Visitor,
  hostId: string
): Promise<void> {
  const notificationService = await createServerNotificationService()

  await notificationService.sendNotification({
    userId: hostId,
    orgId: visit.org_id,
    notificationType: NotificationType.VISITOR_CHECKED_OUT,
    title: 'Visitor Checked Out',
    message: `${visitor.full_name} from ${visitor.company || 'N/A'} has checked out.`,
    data: {
      visitId: visit.id,
      visitorId: visitor.id,
      visitorName: visitor.full_name,
      visitorCompany: visitor.company,
      checkOutTime: visit.check_out_time,
      duration: calculateDuration(visit.check_in_time, visit.check_out_time)
    }
  })
}

/**
 * Send a notification when a visit is cancelled
 */
export async function notifyVisitCancelled(
  visit: Visit,
  visitor: Visitor,
  hostId: string,
  cancelledBy?: User
): Promise<void> {
  const notificationService = await createServerNotificationService()
  
  const cancelledByText = cancelledBy 
    ? `by ${cancelledBy.id === hostId ? 'you' : cancelledBy.full_name}`
    : ''
  
  await notificationService.sendNotification({
    userId: hostId,
    orgId: visit.org_id,
    notificationType: NotificationType.VISIT_CANCELLED,
    title: 'Visit Cancelled',
    message: `Visit with ${visitor.full_name} from ${visitor.company || 'N/A'} scheduled for ${formatDate(visit.scheduled_time)} has been cancelled ${cancelledByText}.`,
    data: {
      visitId: visit.id,
      visitorId: visitor.id,
      visitorName: visitor.full_name,
      visitorCompany: visitor.company,
      scheduledTime: visit.scheduled_time,
      cancelledAt: new Date().toISOString(),
      cancelledById: cancelledBy?.id
    }
  })
}

/**
 * Send a notification when a visit is rescheduled
 */
export async function notifyVisitRescheduled(
  visit: Visit,
  visitor: Visitor,
  hostId: string,
  previousStart: string
): Promise<void> {
  const notificationService = await createServerNotificationService()

  await notificationService.sendNotification({
    userId: hostId,
    orgId: visit.org_id,
    notificationType: NotificationType.VISIT_RESCHEDULED,
    title: 'Visit Rescheduled',
    message: `Visit with ${visitor.full_name} from ${visitor.company || 'N/A'} has been rescheduled from ${formatDate(previousStart)} to ${formatDate(visit.scheduled_time)}.`,
    data: {
      visitId: visit.id,
      visitorId: visitor.id,
      visitorName: visitor.full_name,
      visitorCompany: visitor.company,
      previousStart,
      newStart: visit.scheduled_time
    }
  })
}

/**
 * Send a reminder notification for upcoming visits
 */
export async function notifyVisitReminder(
  visit: Visit,
  visitor: Visitor,
  hostId: string
): Promise<void> {
  const notificationService = await createServerNotificationService()

  await notificationService.sendNotification({
    userId: hostId,
    orgId: visit.org_id,
    notificationType: NotificationType.VISIT_REMINDER,
    title: 'Upcoming Visit Reminder',
    message: `Reminder: You have a visit with ${visitor.full_name} from ${visitor.company || 'N/A'} scheduled for ${formatDate(visit.scheduled_time)}.`,
    data: {
      visitId: visit.id,
      visitorId: visitor.id,
      visitorName: visitor.full_name,
      visitorCompany: visitor.company,
      scheduledTime: visit.scheduled_time,
      purpose: visit.purpose
    }
  })
}

// Helper function to format date
function formatDate(dateString: string | null): string {
  if (!dateString) return 'N/A'
  
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: 'numeric'
  }).format(date)
}

// Helper function to calculate duration between two timestamps
function calculateDuration(start: string | null, end: string | null): string {
  if (!start || !end) return 'N/A'
  
  const startDate = new Date(start)
  const endDate = new Date(end)
  const durationMs = endDate.getTime() - startDate.getTime()
  
  const hours = Math.floor(durationMs / (1000 * 60 * 60))
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`
  } else {
    return `${minutes}m`
  }
}
