import { 
  Notification, 
  NotificationChannel, 
  NotificationType, 
  NotificationPreference 
} from '@/types'
import { SupabaseClient } from '@supabase/supabase-js'
import { v4 as uuidv4 } from 'uuid'

// This will be replaced with actual email service implementation
const sendEmail = async (to: string, subject: string, message: string): Promise<boolean> => {
  console.log(`Sending email to ${to} with subject: ${subject} and message: ${message}`)
  // In a real implementation, this would use a service like SendGrid or AWS SES
  return true
}

// This will be replaced with actual SMS service implementation
const sendSMS = async (to: string, message: string): Promise<boolean> => {
  console.log(`Sending SMS to ${to} with message: ${message}`)
  // In a real implementation, this would use a service like Twilio
  return true
}

export interface NotificationData {
  userId: string
  orgId: string
  notificationType: NotificationType
  title: string
  message: string
  data?: Record<string, unknown>
}

export class NotificationService {
  private supabase: SupabaseClient

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase
  }

  /**
   * Get notification preferences for a user
   */
  async getUserPreferences(userId: string): Promise<NotificationPreference[]> {
    const { data, error } = await this.supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)

    if (error) {
      console.error('Error fetching notification preferences:', error)
      return []
    }

    return data as unknown as NotificationPreference[]
  }

  /**
   * Get notification preference for a specific notification type
   */
  async getUserPreferenceForType(
    userId: string,
    notificationType: NotificationType
  ): Promise<NotificationPreference | null> {
    const { data, error } = await this.supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .eq('notification_type', notificationType)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        // No preference found, return default
        return this.getDefaultPreference(userId, notificationType)
      }
      console.error('Error fetching notification preference:', error)
      return null
    }

    return data as unknown as NotificationPreference
  }

  /**
   * Get default notification preference for a notification type
   */
  private async getDefaultPreference(
    userId: string,
    notificationType: NotificationType
  ): Promise<NotificationPreference> {
    // Get user to get org_id
    const { data: userData, error: userError } = await this.supabase
      .from('users')
      .select('org_id')
      .eq('id', userId)
      .single()

    if (userError) {
      console.error('Error fetching user data:', userError)
      throw new Error('Could not fetch user data')
    }

    const orgId = userData?.org_id as string

    // Default preference: email and in-app enabled, SMS disabled
    return {
      id: uuidv4(),
      user_id: userId,
      org_id: orgId,
      notification_type: notificationType,
      email_enabled: true,
      sms_enabled: false,
      in_app_enabled: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  }

  /**
   * Set notification preference for a user
   */
  async setUserPreference(
    userId: string,
    notificationType: NotificationType,
    emailEnabled: boolean,
    smsEnabled: boolean,
    inAppEnabled: boolean
  ): Promise<NotificationPreference> {
    // Get user to get org_id
    const { data: userData, error: userError } = await this.supabase
      .from('users')
      .select('org_id')
      .eq('id', userId)
      .single()

    if (userError) {
      console.error('Error fetching user data:', userError)
      throw new Error('Could not fetch user data')
    }

    const orgId = userData?.org_id as string

    // Check if preference already exists
    const { data: existingPref, error: prefError } = await this.supabase
      .from('notification_preferences')
      .select('id')
      .eq('user_id', userId)
      .eq('notification_type', notificationType)
      .single()

    const now = new Date().toISOString()
    
    if (prefError && prefError.code !== 'PGRST116') {
      console.error('Error checking existing preference:', prefError)
      throw new Error('Could not check existing preference')
    }

    if (existingPref) {
      // Update existing preference
      const { data, error } = await this.supabase
        .from('notification_preferences')
        .update({
          email_enabled: emailEnabled,
          sms_enabled: smsEnabled,
          in_app_enabled: inAppEnabled,
          updated_at: now
        })
        .eq('id', existingPref.id)
        .select()
        .single()

      if (error) {
        console.error('Error updating notification preference:', error)
        throw new Error('Could not update notification preference')
      }

      return data as unknown as NotificationPreference
    } else {
      // Create new preference
      const newPreference = {
        id: uuidv4(),
        user_id: userId,
        org_id: orgId,
        notification_type: notificationType,
        email_enabled: emailEnabled,
        sms_enabled: smsEnabled,
        in_app_enabled: inAppEnabled,
        created_at: now,
        updated_at: now
      }

      const { data, error } = await this.supabase
        .from('notification_preferences')
        .insert(newPreference)
        .select()
        .single()

      if (error) {
        console.error('Error creating notification preference:', error)
        throw new Error('Could not create notification preference')
      }

      return data as unknown as NotificationPreference
    }
  }

  /**
   * Send a notification to a user
   */
  async sendNotification(notificationData: NotificationData): Promise<void> {
    const { userId, orgId, notificationType, title, message, data } = notificationData

    // Get user preferences
    const preference = await this.getUserPreferenceForType(userId, notificationType)
    if (!preference) {
      console.error('No notification preference found for user:', userId)
      return
    }

    // Get user contact information
    const { data: userData, error: userError } = await this.supabase
      .from('users')
      .select('email, phone')
      .eq('id', userId)
      .single()

    if (userError) {
      console.error('Error fetching user data:', userError)
      return
    }

    const email = userData?.email as string | undefined
    const phone = userData?.phone as string | undefined
    const channels: NotificationChannel[] = []

    // Determine which channels to send to based on preferences
    if (preference.email_enabled && email) {
      channels.push(NotificationChannel.EMAIL)
    }

    if (preference.sms_enabled && phone) {
      channels.push(NotificationChannel.SMS)
    }

    if (preference.in_app_enabled) {
      channels.push(NotificationChannel.IN_APP)
    }

    // Send notifications through each channel
    for (const channel of channels) {
      const notification: Record<string, unknown> = {
        id: uuidv4(),
        user_id: userId,
        org_id: orgId,
        notification_type: notificationType,
        channel,
        title,
        message,
        data: data || {} as Record<string, unknown>,
        is_read: false,
        created_at: new Date().toISOString(),
        sent_at: null,
        delivered_at: null,
        error: null
      }

      // Save notification to database
      const { error: insertError } = await this.supabase
        .from('notifications')
        .insert(notification)

      if (insertError) {
        console.error('Error saving notification:', insertError)
        continue
      }

      // Send notification through appropriate channel
      try {
        let success = false

        if (channel === NotificationChannel.EMAIL && email) {
          success = await sendEmail(email, title, message)
        } else if (channel === NotificationChannel.SMS && phone) {
          success = await sendSMS(phone, message)
        } else if (channel === NotificationChannel.IN_APP) {
          // In-app notifications are already saved to the database
          success = true
        }

        // Update notification status
        if (success) {
          const now = new Date().toISOString()
          await this.supabase
            .from('notifications')
            .update({
              sent_at: now,
              delivered_at: now
            })
            .eq('id', notification.id)
        } else {
          await this.supabase
            .from('notifications')
            .update({
              error: 'Failed to send notification'
            })
            .eq('id', notification.id)
        }
      } catch (error) {
        console.error(`Error sending ${channel} notification:`, error)
        await this.supabase
          .from('notifications')
          .update({
            error: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
          })
          .eq('id', notification.id)
      }
    }
  }

  /**
   * Get unread notifications for a user
   */
  async getUnreadNotifications(userId: string): Promise<Notification[]> {
    const { data, error } = await this.supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .eq('is_read', false)
      .eq('channel', NotificationChannel.IN_APP)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching unread notifications:', error)
      return []
    }

    return data as unknown as Notification[]
  }

  /**
   * Mark a notification as read
   */
  async markNotificationAsRead(notificationId: string): Promise<void> {
    const { error } = await this.supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId)

    if (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllNotificationsAsRead(userId: string): Promise<void> {
    const { error } = await this.supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('user_id', userId)
      .eq('is_read', false)

    if (error) {
      console.error('Error marking all notifications as read:', error)
    }
  }

  /**
   * Delete a notification
   */
  async deleteNotification(notificationId: string): Promise<void> {
    const { error } = await this.supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId)

    if (error) {
      console.error('Error deleting notification:', error)
    }
  }
}
