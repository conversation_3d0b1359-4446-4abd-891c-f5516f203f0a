import { createBrowserClient } from '@supabase/ssr'
import { Database } from '@/types/supabase'
import { fixUrlEncodedSupabaseCookies } from './cookies'

/**
 * Create a Supabase client for client components
 * Uses the new @supabase/ssr package
 * Automatically fixes URL-encoded cookies before creating the client
 */
export const createClient = () => {
  // Fix any URL-encoded cookies before creating the client
  if (typeof window !== 'undefined') {
    fixUrlEncodedSupabaseCookies()
  }

  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
