import { createBrowserClient } from '@supabase/ssr'
import { Database } from '@/types/supabase'
import { clearStaleSupabaseCookiesIfNeeded } from './cookies'

/**
 * Create a Supabase client for client components
 * Uses the new @supabase/ssr package
 * Automatically clears stale cookies before creating the client
 */
export const createClient = () => {
  // Clear any stale cookies before creating the client
  if (typeof window !== 'undefined') {
    clearStaleSupabaseCookiesIfNeeded()
  }

  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
