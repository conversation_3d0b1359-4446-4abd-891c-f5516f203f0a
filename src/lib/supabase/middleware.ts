import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  // Debug logging
  const path = request.nextUrl.pathname
  console.log(`[Middleware] Processing ${path}`)

  const cookies = request.cookies.getAll()
  const supabaseCookies = cookies.filter(c => c.name.startsWith('sb-'))
  console.log(`[Middleware] Found ${supabaseCookies.length} Supabase cookies:`, supabaseCookies.map(c => `${c.name}=${c.value.substring(0, 20)}...`))

  // Check for malformed cookies and clear them if accessing dashboard
  let hasMalformedCookies = false
  for (const cookie of supabaseCookies) {
    if (cookie.value) {
      // Check for URL-encoded cookies (contain %)
      if (cookie.value.includes('%')) {
        console.log(`[Middleware] Detected URL-encoded cookie: ${cookie.name}`)
        hasMalformedCookies = true
        break
      }

      // Check for invalid base64 prefixes
      if (cookie.value.startsWith('base64l-') || cookie.value.includes('base64l-')) {
        console.log(`[Middleware] Detected malformed base64 cookie: ${cookie.name}`)
        hasMalformedCookies = true
        break
      }

      // Try to decode as base64
      try {
        atob(cookie.value)
      } catch {
        console.log(`[Middleware] Detected invalid base64 cookie: ${cookie.name}`)
        hasMalformedCookies = true
        break
      }
    }
  }

  // If we found malformed cookies and user is trying to access dashboard, clear them
  if (hasMalformedCookies && path.startsWith('/dashboard')) {
    console.log(`[Middleware] Clearing malformed cookies and redirecting to login`)
    const redirectUrl = request.nextUrl.clone()
    redirectUrl.pathname = '/login'
    redirectUrl.searchParams.set('cookies_cleared', 'true')

    const response = NextResponse.redirect(redirectUrl)

    // Clear all Supabase cookies
    supabaseCookies.forEach(cookie => {
      response.cookies.delete(cookie.name)
    })

    return response
  }

  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  try {
    const {
      data: { user },
      error
    } = await supabase.auth.getUser()

    console.log(`[Middleware] Auth result:`, {
      hasUser: !!user,
      userId: user?.id,
      error: error?.message
    })

    if (error) {
      console.error(`[Middleware] Auth error:`, error)
    }

    if (
      !user &&
      !request.nextUrl.pathname.startsWith('/login') &&
      !request.nextUrl.pathname.startsWith('/auth') &&
      !request.nextUrl.pathname.startsWith('/signup') &&
      !request.nextUrl.pathname.startsWith('/clear-cookies') &&
      request.nextUrl.pathname.startsWith('/dashboard')
    ) {
      // no user, potentially respond by redirecting the user to the login page
      console.log(`[Middleware] No user found, redirecting ${path} to /login`)
      const url = request.nextUrl.clone()
      url.pathname = '/login'
      return NextResponse.redirect(url)
    }

    console.log(`[Middleware] Access granted for ${path}`)

  } catch (authError) {
    console.error(`[Middleware] Exception during auth:`, authError)

    // If there's an exception and they're trying to access dashboard, redirect to login
    if (request.nextUrl.pathname.startsWith('/dashboard')) {
      console.log(`[Middleware] Auth exception on dashboard access, redirecting to /login`)
      const url = request.nextUrl.clone()
      url.pathname = '/login'
      return NextResponse.redirect(url)
    }
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
  // creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!
  return supabaseResponse
}