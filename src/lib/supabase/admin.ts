import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

// Ensure this file is only used on the server side
if (typeof window !== 'undefined') {
  throw new Error('This file should only be used on the server side');
}

/**
 * Creates a Supabase client with the service role key
 * This client bypasses RLS policies and should only be used in server-side code
 */
export const createAdminClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase URL or service role key');
  }

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};
