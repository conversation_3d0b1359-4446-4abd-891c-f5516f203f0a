/**
 * Utility functions for managing Supabase cookies
 */

/**
 * Clear all Supabase-related cookies to fix stale session issues
 * This is useful when encountering "stale cookie data" errors
 */
export function clearSupabaseCookies(): void {
  if (typeof document === 'undefined') {
    // Server-side, can't clear cookies
    return
  }

  try {
    // List of known Supabase cookie names
    const cookiesToClear = [
      'sb-access-token',
      'sb-refresh-token', 
      'supabase-auth-token',
      'supabase.auth.token'
    ]
    
    // Clear specific cookies
    cookiesToClear.forEach(cookieName => {
      // Clear for current domain
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname}`
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`
      // Clear for parent domain (in case of subdomain)
      const parentDomain = window.location.hostname.split('.').slice(-2).join('.')
      if (parentDomain !== window.location.hostname) {
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${parentDomain}`
      }
    })
    
    // Also clear any cookies that start with 'sb-' (Supabase cookies)
    document.cookie.split(';').forEach(cookie => {
      const cookieName = cookie.split('=')[0].trim()
      if (cookieName.startsWith('sb-')) {
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname}`
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`
        // Clear for parent domain
        const parentDomain = window.location.hostname.split('.').slice(-2).join('.')
        if (parentDomain !== window.location.hostname) {
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${parentDomain}`
        }
      }
    })
    
    console.log('Supabase cookies cleared successfully')
  } catch (err) {
    console.error('Error clearing Supabase cookies:', err)
  }
}

/**
 * Check if there are any Supabase cookies present
 */
export function hasSupabaseCookies(): boolean {
  if (typeof document === 'undefined') {
    return false
  }

  const cookies = document.cookie.split(';')
  return cookies.some(cookie => {
    const cookieName = cookie.split('=')[0].trim()
    return cookieName.startsWith('sb-') || 
           cookieName.includes('supabase')
  })
}

/**
 * Get all Supabase-related cookies for debugging
 */
export function getSupabaseCookies(): Record<string, string> {
  if (typeof document === 'undefined') {
    return {}
  }

  const supabaseCookies: Record<string, string> = {}

  document.cookie.split(';').forEach(cookie => {
    const [name, value] = cookie.split('=').map(s => s.trim())
    if (name.startsWith('sb-') || name.includes('supabase')) {
      supabaseCookies[name] = value || ''
    }
  })

  return supabaseCookies
}

/**
 * Check if any Supabase cookies contain invalid/stale data
 * This can help detect cookies that might cause "stale cookie data" errors
 * Only considers cookies stale if they're actually corrupted, not just old
 */
export function hasStaleSupabaseCookies(): boolean {
  if (typeof document === 'undefined') {
    return false
  }

  const cookies = getSupabaseCookies()

  // If no cookies exist, there's nothing stale
  if (Object.keys(cookies).length === 0) {
    return false
  }

  // Check each cookie value to see if it's valid base64 and can be decoded
  for (const [name, value] of Object.entries(cookies)) {
    if (value && value !== '') {
      try {
        // Try to decode the cookie value as base64
        const decoded = atob(value)

        // Check if the decoded value looks like valid JSON (most Supabase cookies are JSON)
        try {
          const parsed = JSON.parse(decoded)

          // If it's a valid session-like object, it's probably not stale
          if (parsed && typeof parsed === 'object') {
            // Check for common session properties
            if (parsed.access_token || parsed.refresh_token || parsed.user || parsed.expires_at) {
              console.log(`Cookie ${name} appears to be a valid session cookie`)
              continue // This cookie is fine
            }
          }
        } catch (jsonError) {
          // If it's not JSON, it might still be valid (some cookies aren't JSON)
          // Only consider it stale if it contains replacement characters
          if (decoded.includes('\uFFFD') || decoded.includes('�')) {
            console.log(`Detected stale cookie with invalid UTF-8: ${name}`)
            return true
          }
        }
      } catch (error) {
        console.log(`Detected stale cookie with invalid base64: ${name}`)
        return true
      }
    }
  }

  return false
}

/**
 * Proactively clear stale cookies before they cause issues
 * This should be called before creating any Supabase clients
 */
export function clearStaleSupabaseCookiesIfNeeded(): void {
  if (hasStaleSupabaseCookies()) {
    console.log('Detected stale Supabase cookies, clearing them proactively')
    clearSupabaseCookies()
  }
}
