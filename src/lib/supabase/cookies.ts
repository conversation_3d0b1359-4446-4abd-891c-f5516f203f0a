/**
 * Utility functions for managing Supabase cookies
 */

/**
 * Clear all Supabase-related cookies to fix stale session issues
 * This is useful when encountering "stale cookie data" errors
 */
export function clearSupabaseCookies(): void {
  if (typeof document === 'undefined') {
    // Server-side, can't clear cookies
    return
  }

  try {
    // List of known Supabase cookie names
    const cookiesToClear = [
      'sb-access-token',
      'sb-refresh-token', 
      'supabase-auth-token',
      'supabase.auth.token'
    ]
    
    // Clear specific cookies
    cookiesToClear.forEach(cookieName => {
      // Clear for current domain
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname}`
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`
      // Clear for parent domain (in case of subdomain)
      const parentDomain = window.location.hostname.split('.').slice(-2).join('.')
      if (parentDomain !== window.location.hostname) {
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${parentDomain}`
      }
    })
    
    // Also clear any cookies that start with 'sb-' (Supabase cookies)
    document.cookie.split(';').forEach(cookie => {
      const cookieName = cookie.split('=')[0].trim()
      if (cookieName.startsWith('sb-')) {
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname}`
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`
        // Clear for parent domain
        const parentDomain = window.location.hostname.split('.').slice(-2).join('.')
        if (parentDomain !== window.location.hostname) {
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${parentDomain}`
        }
      }
    })
    
    console.log('Supabase cookies cleared successfully')
  } catch (err) {
    console.error('Error clearing Supabase cookies:', err)
  }
}

/**
 * Check if there are any Supabase cookies present
 */
export function hasSupabaseCookies(): boolean {
  if (typeof document === 'undefined') {
    return false
  }

  const cookies = document.cookie.split(';')
  return cookies.some(cookie => {
    const cookieName = cookie.split('=')[0].trim()
    return cookieName.startsWith('sb-') || 
           cookieName.includes('supabase')
  })
}

/**
 * Get all Supabase-related cookies for debugging
 */
export function getSupabaseCookies(): Record<string, string> {
  if (typeof document === 'undefined') {
    return {}
  }

  const supabaseCookies: Record<string, string> = {}
  
  document.cookie.split(';').forEach(cookie => {
    const [name, value] = cookie.split('=').map(s => s.trim())
    if (name.startsWith('sb-') || name.includes('supabase')) {
      supabaseCookies[name] = value || ''
    }
  })
  
  return supabaseCookies
}
