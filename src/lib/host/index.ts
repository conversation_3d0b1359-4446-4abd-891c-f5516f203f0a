import { createServerHostService } from './server';
import { createClientHostService } from './client';

/**
 * Creates a host service instance based on the environment.
 *
 * @returns A host service instance
 */
export async function createHostService() {
  if (typeof window === 'undefined') {
    // Server-side
    return await createServerHostService();
  } else {
    // Client-side
    return createClientHostService();
  }
}

export type { HostService } from './server';
export type { ClientHostService } from './client';
export * from '../../types/host';
