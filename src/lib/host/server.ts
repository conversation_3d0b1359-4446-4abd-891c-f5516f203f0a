/* eslint-disable */
// @ts-nocheck
import { createServerComponentClient } from '@/lib/supabase/server-component-client';
import { cookies } from 'next/headers';
import {
  HostStatus,
  HostStatusData,
  HostStatusDuration,
  HostStatusHistory,
  CalendarIntegration,
  CalendarEvent,
  HostStatusInsert,
  HostStatusUpdate,
  CalendarIntegrationInsert,
  CalendarIntegrationUpdate,
  CalendarEventInsert,
  CalendarEventUpdate
} from '../../types/host';

/**
 * Create a server-side host service
 */
export const createServerHostService = async () => {
  const supabase = await createServerComponentClient();
  return new HostService(supabase);
};

/**
 * Server-side host service for managing host statuses and calendar integrations
 */
export class HostService {
  private supabase;

  constructor(supabase: ReturnType<typeof createServerComponentClient>) {
    this.supabase = supabase;
  }

  /**
   * Get the current status of a host
   * @param userId The ID of the host user
   * @returns The current host status or null if not set
   */
  async getHostStatus(userId: string): Promise<HostStatusData | null> {
    const { data, error } = await this.supabase
      .from('host_statuses')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !data) {
      return null;
    }

    return this.mapHostStatusRowToData(data);
  }

  /**
   * Get the status history of a host
   * @param userId The ID of the host user
   * @param limit The maximum number of history items to return
   * @param offset The offset for pagination
   * @returns An array of host status history items
   */
  async getHostStatusHistory(
    userId: string,
    limit: number = 10,
    offset: number = 0
  ): Promise<HostStatusHistory[]> {
    const { data, error } = await this.supabase
      .from('host_status_history')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error || !data) {
      return [];
    }

    return data.map(row => ({
      id: row.id,
      user_id: row.user_id,
      status: row.status as HostStatus,
      message: row.message || undefined,
      duration: {
        type: row.duration_type as HostStatusDuration['type'],
        endTime: row.end_time || undefined,
        endDate: row.end_date || undefined,
      },
      created_at: row.created_at,
      ended_at: row.ended_at,
    }));
  }

  /**
   * Set the status of a host
   * @param userId The ID of the host user
   * @param status The status to set
   * @param message Optional message to accompany the status
   * @param duration The duration of the status
   * @returns The updated host status
   */
  async setHostStatus(
    userId: string,
    status: HostStatus,
    message?: string,
    duration: HostStatusDuration = { type: 'indefinite' }
  ): Promise<HostStatusData | null> {
    // Check if the host already has a status
    const currentStatus = await this.getHostStatus(userId);

    const statusData: HostStatusInsert = {
      user_id: userId,
      status,
      message: message || null,
      duration_type: duration.type,
      end_time: duration.type === 'until_time' ? duration.endTime || null : null,
      end_date: duration.type === 'all_day' ? duration.endDate || null : null,
    };

    let result;

    if (currentStatus) {
      // Update existing status
      const { data, error } = await this.supabase
        .from('host_statuses')
        .update(statusData as HostStatusUpdate)
        .eq('id', currentStatus.id)
        .select('*')
        .single();

      if (error) {
        console.error('Error updating host status:', error);
        return null;
      }

      result = data;
    } else {
      // Insert new status
      const { data, error } = await this.supabase
        .from('host_statuses')
        .insert(statusData)
        .select('*')
        .single();

      if (error) {
        console.error('Error inserting host status:', error);
        return null;
      }

      result = data;
    }

    return this.mapHostStatusRowToData(result);
  }

  /**
   * Clear the status of a host
   * @param userId The ID of the host user
   * @returns True if the status was cleared successfully
   */
  async clearHostStatus(userId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('host_statuses')
      .delete()
      .eq('user_id', userId);

    return !error;
  }

  /**
   * Get the calendar integrations for a host
   * @param userId The ID of the host user
   * @returns An array of calendar integrations
   */
  async getCalendarIntegrations(userId: string): Promise<CalendarIntegration[]> {
    const { data, error } = await this.supabase
      .from('calendar_integrations')
      .select('*')
      .eq('user_id', userId);

    if (error || !data) {
      return [];
    }

    return data.map(row => ({
      id: row.id,
      user_id: row.user_id,
      provider: row.provider as CalendarIntegration['provider'],
      is_active: row.is_active,
      sync_frequency: row.sync_frequency as CalendarIntegration['sync_frequency'],
      last_synced_at: row.last_synced_at || undefined,
      created_at: row.created_at,
      updated_at: row.updated_at,
    }));
  }

  /**
   * Add a calendar integration for a host
   * @param userId The ID of the host user
   * @param provider The calendar provider
   * @param syncFrequency The frequency of calendar syncing
   * @returns The created calendar integration
   */
  async addCalendarIntegration(
    userId: string,
    provider: CalendarIntegration['provider'],
    syncFrequency: CalendarIntegration['sync_frequency'] = 'daily'
  ): Promise<CalendarIntegration | null> {
    const integrationData: CalendarIntegrationInsert = {
      user_id: userId,
      provider,
      sync_frequency: syncFrequency,
      is_active: true,
    };

    const { data, error } = await this.supabase
      .from('calendar_integrations')
      .insert(integrationData)
      .select('*')
      .single();

    if (error) {
      console.error('Error adding calendar integration:', error);
      return null;
    }

    return {
      id: data.id,
      user_id: data.user_id,
      provider: data.provider as CalendarIntegration['provider'],
      is_active: data.is_active,
      sync_frequency: data.sync_frequency as CalendarIntegration['sync_frequency'],
      last_synced_at: data.last_synced_at || undefined,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };
  }

  /**
   * Update a calendar integration
   * @param integrationId The ID of the integration to update
   * @param updates The updates to apply
   * @returns The updated calendar integration
   */
  async updateCalendarIntegration(
    integrationId: string,
    updates: Partial<Pick<CalendarIntegration, 'is_active' | 'sync_frequency'>>
  ): Promise<CalendarIntegration | null> {
    const updateData: CalendarIntegrationUpdate = {};

    if (updates.is_active !== undefined) {
      updateData.is_active = updates.is_active;
    }

    if (updates.sync_frequency) {
      updateData.sync_frequency = updates.sync_frequency;
    }

    const { data, error } = await this.supabase
      .from('calendar_integrations')
      .update(updateData)
      .eq('id', integrationId)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating calendar integration:', error);
      return null;
    }

    return {
      id: data.id,
      user_id: data.user_id,
      provider: data.provider as CalendarIntegration['provider'],
      is_active: data.is_active,
      sync_frequency: data.sync_frequency as CalendarIntegration['sync_frequency'],
      last_synced_at: data.last_synced_at || undefined,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };
  }

  /**
   * Delete a calendar integration
   * @param integrationId The ID of the integration to delete
   * @returns True if the integration was deleted successfully
   */
  async deleteCalendarIntegration(integrationId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('calendar_integrations')
      .delete()
      .eq('id', integrationId);

    return !error;
  }

  /**
   * Get calendar events for a host
   * @param userId The ID of the host user
   * @param startDate The start date for filtering events
   * @param endDate The end date for filtering events
   * @returns An array of calendar events
   */
  async getCalendarEvents(
    userId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<CalendarEvent[]> {
    let query = this.supabase
      .from('calendar_events')
      .select('*')
      .eq('user_id', userId);

    if (startDate) {
      query = query.gte('start_time', startDate.toISOString());
    }

    if (endDate) {
      query = query.lte('end_time', endDate.toISOString());
    }

    const { data, error } = await query.order('start_time', { ascending: true });

    if (error || !data) {
      return [];
    }

    return data.map(row => ({
      id: row.id,
      user_id: row.user_id,
      calendar_integration_id: row.calendar_integration_id,
      external_event_id: row.external_event_id,
      title: row.title,
      description: row.description || undefined,
      location: row.location || undefined,
      start_time: row.start_time,
      end_time: row.end_time,
      is_all_day: row.is_all_day,
      status: row.status as CalendarEvent['status'],
      created_at: row.created_at,
      updated_at: row.updated_at,
    }));
  }

  /**
   * Add a calendar event
   * @param event The event to add
   * @returns The created calendar event
   */
  async addCalendarEvent(event: Omit<CalendarEvent, 'id' | 'created_at' | 'updated_at'>): Promise<CalendarEvent | null> {
    const eventData: CalendarEventInsert = {
      user_id: event.user_id,
      calendar_integration_id: event.calendar_integration_id,
      external_event_id: event.external_event_id,
      title: event.title,
      description: event.description || null,
      location: event.location || null,
      start_time: event.start_time,
      end_time: event.end_time,
      is_all_day: event.is_all_day,
      status: event.status,
    };

    const { data, error } = await this.supabase
      .from('calendar_events')
      .insert(eventData)
      .select('*')
      .single();

    if (error) {
      console.error('Error adding calendar event:', error);
      return null;
    }

    return {
      id: data.id,
      user_id: data.user_id,
      calendar_integration_id: data.calendar_integration_id,
      external_event_id: data.external_event_id,
      title: data.title,
      description: data.description || undefined,
      location: data.location || undefined,
      start_time: data.start_time,
      end_time: data.end_time,
      is_all_day: data.is_all_day,
      status: data.status as CalendarEvent['status'],
      created_at: data.created_at,
      updated_at: data.updated_at,
    };
  }

  /**
   * Update a calendar event
   * @param eventId The ID of the event to update
   * @param updates The updates to apply
   * @returns The updated calendar event
   */
  async updateCalendarEvent(
    eventId: string,
    updates: Partial<Pick<CalendarEvent, 'title' | 'description' | 'location' | 'start_time' | 'end_time' | 'is_all_day' | 'status'>>
  ): Promise<CalendarEvent | null> {
    const updateData: CalendarEventUpdate = {};

    if (updates.title !== undefined) {
      updateData.title = updates.title;
    }

    if (updates.description !== undefined) {
      updateData.description = updates.description || null;
    }

    if (updates.location !== undefined) {
      updateData.location = updates.location || null;
    }

    if (updates.start_time !== undefined) {
      updateData.start_time = updates.start_time;
    }

    if (updates.end_time !== undefined) {
      updateData.end_time = updates.end_time;
    }

    if (updates.is_all_day !== undefined) {
      updateData.is_all_day = updates.is_all_day;
    }

    if (updates.status !== undefined) {
      updateData.status = updates.status;
    }

    const { data, error } = await this.supabase
      .from('calendar_events')
      .update(updateData)
      .eq('id', eventId)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating calendar event:', error);
      return null;
    }

    return {
      id: data.id,
      user_id: data.user_id,
      calendar_integration_id: data.calendar_integration_id,
      external_event_id: data.external_event_id,
      title: data.title,
      description: data.description || undefined,
      location: data.location || undefined,
      start_time: data.start_time,
      end_time: data.end_time,
      is_all_day: data.is_all_day,
      status: data.status as CalendarEvent['status'],
      created_at: data.created_at,
      updated_at: data.updated_at,
    };
  }

  /**
   * Delete a calendar event
   * @param eventId The ID of the event to delete
   * @returns True if the event was deleted successfully
   */
  async deleteCalendarEvent(eventId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('calendar_events')
      .delete()
      .eq('id', eventId);

    return !error;
  }

  /**
   * Get the current status of multiple hosts
   * @param userIds The IDs of the host users
   * @returns A map of user IDs to their current status
   */
  async getMultipleHostStatuses(userIds: string[]): Promise<Record<string, HostStatusData | null>> {
    if (userIds.length === 0) {
      return {};
    }

    const { data, error } = await this.supabase
      .from('host_statuses')
      .select('*')
      .in('user_id', userIds);

    if (error || !data) {
      return {};
    }

    const statusMap: Record<string, HostStatusData | null> = {};

    // Initialize all requested user IDs with null
    userIds.forEach(userId => {
      statusMap[userId] = null;
    });

    // Fill in the statuses that exist
    data.forEach(row => {
      statusMap[row.user_id] = this.mapHostStatusRowToData(row);
    });

    return statusMap;
  }

  /**
   * Check if a host is available
   * @param userId The ID of the host user
   * @returns True if the host is available, false otherwise
   */
  async isHostAvailable(userId: string): Promise<boolean> {
    const status = await this.getHostStatus(userId);

    if (!status) {
      // No status set, assume available
      return true;
    }

    if (status.status !== 'available') {
      return false;
    }

    // Check if the status has expired
    if (status.duration.type === 'until_time' && status.duration.endTime) {
      const endTime = new Date(status.duration.endTime);
      if (endTime < new Date()) {
        // Status has expired, host is available
        await this.clearHostStatus(userId);
        return true;
      }
    }

    if (status.duration.type === 'all_day' && status.duration.endDate) {
      const endDate = new Date(status.duration.endDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (endDate < today) {
        // Status has expired, host is available
        await this.clearHostStatus(userId);
        return true;
      }
    }

    return true;
  }

  /**
   * Map a host status row to a HostStatusData object
   * @param row The host status row from the database
   * @returns A HostStatusData object
   */
  private mapHostStatusRowToData(row: any): HostStatusData {
    return {
      id: row.id,
      user_id: row.user_id,
      status: row.status as HostStatus,
      message: row.message || undefined,
      duration: {
        type: row.duration_type as HostStatusDuration['type'],
        endTime: row.end_time || undefined,
        endDate: row.end_date || undefined,
      },
      created_at: row.created_at,
      updated_at: row.updated_at,
    };
  }
}
