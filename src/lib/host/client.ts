import { createClient } from '@supabase/supabase-js';
import { Database } from '../../types/supabase';
import {
  HostStatus,
  HostStatusData,
  HostStatusDuration,
  HostStatusHistory,
  CalendarIntegration,
  CalendarEvent
} from '../../types/host';

/**
 * Client-side host service for managing host statuses and calendar integrations
 */
export interface ClientHostService {
  getHostStatus(userId: string): Promise<HostStatusData | null>;
  getHostStatusHistory(userId: string, limit?: number, offset?: number): Promise<HostStatusHistory[]>;
  setHostStatus(userId: string, status: HostStatus, message?: string, duration?: HostStatusDuration): Promise<HostStatusData | null>;
  clearHostStatus(userId: string): Promise<boolean>;
  getCalendarIntegrations(userId: string): Promise<CalendarIntegration[]>;
  addCalendarIntegration(userId: string, provider: CalendarIntegration['provider'], syncFrequency?: CalendarIntegration['sync_frequency']): Promise<CalendarIntegration | null>;
  updateCalendarIntegration(integrationId: string, updates: Partial<Pick<CalendarIntegration, 'is_active' | 'sync_frequency'>>): Promise<CalendarIntegration | null>;
  deleteCalendarIntegration(integrationId: string): Promise<boolean>;
  getCalendarEvents(userId: string, startDate?: Date, endDate?: Date): Promise<CalendarEvent[]>;
  addCalendarEvent(event: Omit<CalendarEvent, 'id' | 'created_at' | 'updated_at'>): Promise<CalendarEvent | null>;
  updateCalendarEvent(eventId: string, updates: Partial<Pick<CalendarEvent, 'title' | 'description' | 'location' | 'start_time' | 'end_time' | 'is_all_day' | 'status'>>): Promise<CalendarEvent | null>;
  deleteCalendarEvent(eventId: string): Promise<boolean>;
  getMultipleHostStatuses(userIds: string[]): Promise<Record<string, HostStatusData | null>>;
  isHostAvailable(userId: string): Promise<boolean>;
}

// Create a singleton Supabase client
let supabaseClient: ReturnType<typeof createClient<Database>> | null = null;

function getSupabaseClient() {
  if (!supabaseClient) {
    supabaseClient = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
  }
  return supabaseClient;
}

/**
 * Create a client-side host service
 * @returns A client host service instance
 */
export function createClientHostService(): ClientHostService {
  const supabase = getSupabaseClient();

  return {
    /**
     * Get the current status of a host
     * @param userId The ID of the host user
     * @returns The current host status or null if not set
     */
    async getHostStatus(userId: string): Promise<HostStatusData | null> {
      const { data, error } = await supabase
        .from('host_statuses')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error || !data) {
        return null;
      }

      return {
        id: data.id,
        user_id: data.user_id,
        status: data.status as HostStatus,
        message: data.message || undefined,
        duration: {
          type: data.duration_type as HostStatusDuration['type'],
          endTime: data.end_time || undefined,
          endDate: data.end_date || undefined,
        },
        created_at: data.created_at,
        updated_at: data.updated_at,
      };
    },

    /**
     * Get the status history of a host
     * @param userId The ID of the host user
     * @param limit The maximum number of history items to return
     * @param offset The offset for pagination
     * @returns An array of host status history items
     */
    async getHostStatusHistory(
      userId: string,
      limit: number = 10,
      offset: number = 0
    ): Promise<HostStatusHistory[]> {
      const { data, error } = await supabase
        .from('host_status_history')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error || !data) {
        return [];
      }

      return data.map(row => ({
        id: row.id,
        user_id: row.user_id,
        status: row.status as HostStatus,
        message: row.message || undefined,
        duration: {
          type: row.duration_type as HostStatusDuration['type'],
          endTime: row.end_time || undefined,
          endDate: row.end_date || undefined,
        },
        created_at: row.created_at,
        ended_at: row.ended_at,
      }));
    },

    /**
     * Set the status of a host
     * @param userId The ID of the host user
     * @param status The status to set
     * @param message Optional message to accompany the status
     * @param duration The duration of the status
     * @returns The updated host status
     */
    async setHostStatus(
      userId: string,
      status: HostStatus,
      message?: string,
      duration: HostStatusDuration = { type: 'indefinite' }
    ): Promise<HostStatusData | null> {
      // Make a POST request to the API endpoint
      const response = await fetch('/api/host/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          status,
          message,
          duration,
        }),
      });

      if (!response.ok) {
        console.error('Error setting host status:', await response.text());
        return null;
      }

      return await response.json();
    },

    /**
     * Clear the status of a host
     * @param userId The ID of the host user
     * @returns True if the status was cleared successfully
     */
    async clearHostStatus(userId: string): Promise<boolean> {
      // Make a DELETE request to the API endpoint
      const response = await fetch(`/api/host/status?userId=${userId}`, {
        method: 'DELETE',
      });

      return response.ok;
    },

    /**
     * Get the calendar integrations for a host
     * @param userId The ID of the host user
     * @returns An array of calendar integrations
     */
    async getCalendarIntegrations(userId: string): Promise<CalendarIntegration[]> {
      const { data, error } = await supabase
        .from('calendar_integrations')
        .select('*')
        .eq('user_id', userId);

      if (error || !data) {
        return [];
      }

      return data.map(row => ({
        id: row.id,
        user_id: row.user_id,
        provider: row.provider as CalendarIntegration['provider'],
        is_active: row.is_active,
        sync_frequency: row.sync_frequency as CalendarIntegration['sync_frequency'],
        last_synced_at: row.last_synced_at || undefined,
        created_at: row.created_at,
        updated_at: row.updated_at,
      }));
    },

    /**
     * Add a calendar integration for a host
     * @param userId The ID of the host user
     * @param provider The calendar provider
     * @param syncFrequency The frequency of calendar syncing
     * @returns The created calendar integration
     */
    async addCalendarIntegration(
      userId: string,
      provider: CalendarIntegration['provider'],
      syncFrequency: CalendarIntegration['sync_frequency'] = 'daily'
    ): Promise<CalendarIntegration | null> {
      // Make a POST request to the API endpoint
      const response = await fetch('/api/host/calendar-integration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          provider,
          syncFrequency,
        }),
      });

      if (!response.ok) {
        console.error('Error adding calendar integration:', await response.text());
        return null;
      }

      return await response.json();
    },

    /**
     * Update a calendar integration
     * @param integrationId The ID of the integration to update
     * @param updates The updates to apply
     * @returns The updated calendar integration
     */
    async updateCalendarIntegration(
      integrationId: string,
      updates: Partial<Pick<CalendarIntegration, 'is_active' | 'sync_frequency'>>
    ): Promise<CalendarIntegration | null> {
      // Make a PATCH request to the API endpoint
      const response = await fetch(`/api/host/calendar-integration/${integrationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        console.error('Error updating calendar integration:', await response.text());
        return null;
      }

      return await response.json();
    },

    /**
     * Delete a calendar integration
     * @param integrationId The ID of the integration to delete
     * @returns True if the integration was deleted successfully
     */
    async deleteCalendarIntegration(integrationId: string): Promise<boolean> {
      // Make a DELETE request to the API endpoint
      const response = await fetch(`/api/host/calendar-integration/${integrationId}`, {
        method: 'DELETE',
      });

      return response.ok;
    },

    /**
     * Get calendar events for a host
     * @param userId The ID of the host user
     * @param startDate The start date for filtering events
     * @param endDate The end date for filtering events
     * @returns An array of calendar events
     */
    async getCalendarEvents(
      userId: string,
      startDate?: Date,
      endDate?: Date
    ): Promise<CalendarEvent[]> {
      let url = `/api/host/calendar-events?userId=${userId}`;

      if (startDate) {
        url += `&startDate=${startDate.toISOString()}`;
      }

      if (endDate) {
        url += `&endDate=${endDate.toISOString()}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        console.error('Error getting calendar events:', await response.text());
        return [];
      }

      return await response.json();
    },

    /**
     * Add a calendar event
     * @param event The event to add
     * @returns The created calendar event
     */
    async addCalendarEvent(event: Omit<CalendarEvent, 'id' | 'created_at' | 'updated_at'>): Promise<CalendarEvent | null> {
      // Make a POST request to the API endpoint
      const response = await fetch('/api/host/calendar-event', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(event),
      });

      if (!response.ok) {
        console.error('Error adding calendar event:', await response.text());
        return null;
      }

      return await response.json();
    },

    /**
     * Update a calendar event
     * @param eventId The ID of the event to update
     * @param updates The updates to apply
     * @returns The updated calendar event
     */
    async updateCalendarEvent(
      eventId: string,
      updates: Partial<Pick<CalendarEvent, 'title' | 'description' | 'location' | 'start_time' | 'end_time' | 'is_all_day' | 'status'>>
    ): Promise<CalendarEvent | null> {
      // Make a PATCH request to the API endpoint
      const response = await fetch(`/api/host/calendar-event/${eventId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        console.error('Error updating calendar event:', await response.text());
        return null;
      }

      return await response.json();
    },

    /**
     * Delete a calendar event
     * @param eventId The ID of the event to delete
     * @returns True if the event was deleted successfully
     */
    async deleteCalendarEvent(eventId: string): Promise<boolean> {
      // Make a DELETE request to the API endpoint
      const response = await fetch(`/api/host/calendar-event/${eventId}`, {
        method: 'DELETE',
      });

      return response.ok;
    },

    /**
     * Get the current status of multiple hosts
     * @param userIds The IDs of the host users
     * @returns A map of user IDs to their current status
     */
    async getMultipleHostStatuses(userIds: string[]): Promise<Record<string, HostStatusData | null>> {
      if (userIds.length === 0) {
        return {};
      }

      // Make a POST request to the API endpoint
      const response = await fetch('/api/host/statuses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userIds }),
      });

      if (!response.ok) {
        console.error('Error getting multiple host statuses:', await response.text());
        return {};
      }

      return await response.json();
    },

    /**
     * Check if a host is available
     * @param userId The ID of the host user
     * @returns True if the host is available, false otherwise
     */
    async isHostAvailable(userId: string): Promise<boolean> {
      const response = await fetch(`/api/host/available?userId=${userId}`);

      if (!response.ok) {
        console.error('Error checking host availability:', await response.text());
        return true; // Default to available in case of error
      }

      const data = await response.json();
      return data.available;
    }
  };
}
