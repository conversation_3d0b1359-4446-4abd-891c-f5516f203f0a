import { createClient } from '@/lib/supabase/server';
import {
  VisitRequest,
  VisitApproval,
  VisitApprovalRequest,
  VisitApprovalResponse,
  VisitRequestFilter,
  VisitStatus,
  VisitStats
} from '@/types/visit';
import { Database } from '@/types/supabase';
import { SupabaseClient } from '@supabase/supabase-js';

export class VisitService {
  private supabase: SupabaseClient<Database>;

  constructor() {
    this.supabase = createClient();
  }

  /**
   * Get a visit request by ID
   */
  async getVisitRequest(id: string): Promise<VisitRequest | null> {
    // Get the visit data
    const { data: visitData, error: visitError } = await this.supabase
      .from('visits')
      .select('*')
      .eq('id', id)
      .single();

    if (visitError) {
      console.error('Error getting visit request:', visitError);
      return null;
    }

    if (!visitData) {
      return null;
    }

    // Get visitor data
    const { data: visitorData, error: visitorError } = await this.supabase
      .from('visitors')
      .select('*')
      .eq('id', visitData.visitor_id)
      .single();

    if (visitorError) {
      console.error('Error getting visitor data:', visitorError);
    }

    // Get host data
    const { data: userData, error: userError } = await this.supabase
      .from('users')
      .select('*')
      .eq('id', visitData.host_id)
      .single();

    if (userError) {
      console.error('Error getting host data:', userError);
    }

    // Get location data
    const { data: locationData, error: locationError } = await this.supabase
      .from('locations')
      .select('*')
      .eq('id', visitData.location_id)
      .single();

    if (locationError) {
      console.error('Error getting location data:', locationError);
    }

    // Transform the data to match the VisitRequest interface
    const visitRequest: VisitRequest = {
      id: visitData.id,
      visitor_id: visitData.visitor_id,
      host_id: visitData.host_id,
      location_id: visitData.location_id,
      purpose: visitData.purpose,
      start_time: visitData.start_time,
      end_time: visitData.end_time,
      status: visitData.status as VisitStatus,
      notes: visitData.notes,
      created_at: visitData.created_at,
      updated_at: visitData.updated_at,
      visitor_name: visitorData?.name || 'Unknown',
      visitor_email: visitorData?.email || 'Unknown',
      visitor_phone: visitorData?.phone,
      visitor_company: visitorData?.company,
      host_name: userData?.user_metadata?.full_name || 'Unknown',
      host_email: userData?.email || 'Unknown',
      location_name: locationData?.name || 'Unknown'
    };

    return visitRequest;
  }

  /**
   * Get visit requests based on filters
   */
  async getVisitRequests(filter: VisitRequestFilter, limit = 10, offset = 0): Promise<VisitRequest[]> {
    let query = this.supabase
      .from('visits')
      .select('*')
      .order('start_time', { ascending: true });

    // Apply filters
    if (filter.host_id) {
      query = query.eq('host_id', filter.host_id);
    }

    if (filter.visitor_id) {
      query = query.eq('visitor_id', filter.visitor_id);
    }

    if (filter.location_id) {
      query = query.eq('location_id', filter.location_id);
    }

    if (filter.status) {
      if (Array.isArray(filter.status)) {
        query = query.in('status', filter.status);
      } else {
        query = query.eq('status', filter.status);
      }
    }

    if (filter.start_date) {
      query = query.gte('start_time', filter.start_date);
    }

    if (filter.end_date) {
      query = query.lte('start_time', filter.end_date);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data, error } = await query;

    if (error) {
      console.error('Error getting visit requests:', error);
      return [];
    }

    // Get all visitor, host, and location IDs
    const visitorIds = [...new Set(data.map(visit => visit.visitor_id))];
    const hostIds = [...new Set(data.map(visit => visit.host_id))];
    const locationIds = [...new Set(data.map(visit => visit.location_id))];

    // Fetch all visitors, hosts, and locations in bulk
    const { data: visitors } = await this.supabase
      .from('visitors')
      .select('*')
      .in('id', visitorIds);

    const { data: users } = await this.supabase
      .from('users')
      .select('*')
      .in('id', hostIds);

    const { data: locations } = await this.supabase
      .from('locations')
      .select('*')
      .in('id', locationIds);

    // Create lookup maps
    const visitorMap = new Map(visitors?.map(v => [v.id, v]) || []);
    const userMap = new Map(users?.map(u => [u.id, u]) || []);
    const locationMap = new Map(locations?.map(l => [l.id, l]) || []);

    // Transform the data to match the VisitRequest interface
    const visitRequests: VisitRequest[] = data.map(visit => {
      const visitor = visitorMap.get(visit.visitor_id);
      const user = userMap.get(visit.host_id);
      const location = locationMap.get(visit.location_id);

      return {
        id: visit.id,
        visitor_id: visit.visitor_id,
        host_id: visit.host_id,
        location_id: visit.location_id,
        purpose: visit.purpose,
        start_time: visit.start_time,
        end_time: visit.end_time,
        status: visit.status as VisitStatus,
        notes: visit.notes,
        created_at: visit.created_at,
        updated_at: visit.updated_at,
        visitor_name: visitor?.name || 'Unknown',
        visitor_email: visitor?.email || 'Unknown',
        visitor_phone: visitor?.phone,
        visitor_company: visitor?.company,
        host_name: user?.user_metadata?.full_name || 'Unknown',
        host_email: user?.email || 'Unknown',
        location_name: location?.name || 'Unknown'
      };
    });

    return visitRequests;
  }

  /**
   * Get visit statistics for a host
   */
  async getVisitStats(hostId: string): Promise<VisitStats> {
    const { data, error } = await this.supabase
      .from('visits')
      .select('status')
      .eq('host_id', hostId);

    if (error) {
      console.error('Error getting visit stats:', error);
      return {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        checked_in: 0,
        checked_out: 0,
        cancelled: 0,
        no_show: 0
      };
    }

    const stats: VisitStats = {
      total: data.length,
      pending: data.filter((item: { status: string }) => item.status === 'pending').length,
      approved: data.filter((item: { status: string }) => item.status === 'approved').length,
      rejected: data.filter((item: { status: string }) => item.status === 'rejected').length,
      checked_in: data.filter((item: { status: string }) => item.status === 'checked_in').length,
      checked_out: data.filter((item: { status: string }) => item.status === 'checked_out').length,
      cancelled: data.filter((item: { status: string }) => item.status === 'cancelled').length,
      no_show: data.filter((item: { status: string }) => item.status === 'no_show').length
    };

    return stats;
  }

  /**
   * Update a visit request status
   */
  async updateVisitStatus(id: string, status: VisitStatus, notes?: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('visits')
      .update({
        status,
        notes: notes || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) {
      console.error('Error updating visit status:', error);
      return false;
    }

    return true;
  }

  /**
   * Get approvals for a visit
   */
  async getVisitApprovals(visitId: string): Promise<VisitApproval[]> {
    const { data, error } = await this.supabase
      .from('visit_approvals')
      .select('*')
      .eq('visit_id', visitId);

    if (error) {
      console.error('Error getting visit approvals:', error);
      return [];
    }

    // Get all approver IDs
    const approverIds = [...new Set(data.map(approval => approval.approver_id))];

    // Fetch all approvers in bulk
    const { data: approvers } = await this.supabase
      .from('users')
      .select('*')
      .in('id', approverIds);

    // Create lookup map
    const approverMap = new Map(approvers?.map(a => [a.id, a]) || []);

    // Transform the data to match the VisitApproval interface
    const visitApprovals: VisitApproval[] = data.map(approval => {
      const approver = approverMap.get(approval.approver_id);

      return {
        id: approval.id,
        visit_id: approval.visit_id,
        approver_id: approval.approver_id,
        status: approval.status as 'pending' | 'approved' | 'rejected',
        notes: approval.notes,
        created_at: approval.created_at,
        updated_at: approval.updated_at,
        approver_name: approver?.user_metadata?.full_name || 'Unknown',
        approver_email: approver?.email || 'Unknown'
      };
    });

    return visitApprovals;
  }

  /**
   * Create a visit approval request
   */
  async createVisitApprovalRequest(request: VisitApprovalRequest): Promise<VisitApproval | null> {
    const { data, error } = await this.supabase
      .from('visit_approvals')
      .insert({
        visit_id: request.visit_id,
        approver_id: request.approver_id,
        status: 'pending',
        notes: request.notes || null
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating visit approval request:', error);
      return null;
    }

    // Get approver data
    const { data: approverData } = await this.supabase
      .from('users')
      .select('*')
      .eq('id', request.approver_id)
      .single();

    // Transform the data to match the VisitApproval interface
    const visitApproval: VisitApproval = {
      id: data.id,
      visit_id: data.visit_id,
      approver_id: data.approver_id,
      status: data.status as 'pending' | 'approved' | 'rejected',
      notes: data.notes,
      created_at: data.created_at,
      updated_at: data.updated_at,
      approver_name: approverData?.user_metadata?.full_name || 'Unknown',
      approver_email: approverData?.email || 'Unknown'
    };

    return visitApproval;
  }

  /**
   * Respond to a visit approval request
   */
  async respondToVisitApprovalRequest(response: VisitApprovalResponse): Promise<boolean> {
    const { error } = await this.supabase
      .from('visit_approvals')
      .update({
        status: response.status,
        notes: response.notes || null,
        updated_at: new Date().toISOString()
      })
      .eq('visit_id', response.visit_id)
      .eq('approver_id', response.approver_id);

    if (error) {
      console.error('Error responding to visit approval request:', error);
      return false;
    }

    // If approved, update the visit status
    if (response.status === 'approved') {
      // Check if all required approvals are approved
      const { data: approvals, error: approvalsError } = await this.supabase
        .from('visit_approvals')
        .select('status')
        .eq('visit_id', response.visit_id);

      if (approvalsError) {
        console.error('Error checking visit approvals:', approvalsError);
        return false;
      }

      // If all approvals are approved, update the visit status
      const allApproved = approvals.every((approval: { status: string }) => approval.status === 'approved');
      if (allApproved) {
        const updateSuccess = await this.updateVisitStatus(response.visit_id, 'approved');
        if (!updateSuccess) {
          return false;
        }
      }
    } else if (response.status === 'rejected') {
      // If rejected, update the visit status
      const updateSuccess = await this.updateVisitStatus(response.visit_id, 'rejected', response.notes);
      if (!updateSuccess) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get pending approval requests for a user
   */
  async getPendingApprovalRequests(userId: string, limit = 10, offset = 0): Promise<VisitRequest[]> {
    const { data, error } = await this.supabase
      .from('visit_approvals')
      .select(`
        visit_id
      `)
      .eq('approver_id', userId)
      .eq('status', 'pending')
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error getting pending approval requests:', error);
      return [];
    }

    // Get the visit requests for the pending approvals
    const visitIds = data.map((item: { visit_id: string }) => item.visit_id);
    if (visitIds.length === 0) {
      return [];
    }

    const visitRequests = await Promise.all(
      visitIds.map((id: string) => this.getVisitRequest(id))
    );

    // Filter out null values
    return visitRequests.filter(Boolean) as VisitRequest[];
  }

  /**
   * Get approval workflows for an organization
   */
  async getApprovalWorkflows(organizationId: string) {
    const { data, error } = await this.supabase
      .from('approval_workflows')
      .select(`
        id,
        name,
        description,
        is_active,
        created_at,
        updated_at,
        approval_workflow_steps (
          id,
          step_number,
          approver_type,
          approver_id,
          is_required
        )
      `)
      .eq('organization_id', organizationId)
      .order('name');

    if (error) {
      console.error('Error getting approval workflows:', error);
      return [];
    }

    return data;
  }

  /**
   * Create an approval workflow
   */
  async createApprovalWorkflow(organizationId: string, name: string, description?: string) {
    const { data, error } = await this.supabase
      .from('approval_workflows')
      .insert({
        organization_id: organizationId,
        name,
        description: description || null
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating approval workflow:', error);
      return null;
    }

    return data;
  }

  /**
   * Add a step to an approval workflow
   */
  async addApprovalWorkflowStep(
    workflowId: string,
    stepNumber: number,
    approverType: 'user' | 'role' | 'department',
    approverId: string,
    isRequired = true
  ) {
    const { data, error } = await this.supabase
      .from('approval_workflow_steps')
      .insert({
        workflow_id: workflowId,
        step_number: stepNumber,
        approver_type: approverType,
        approver_id: approverId,
        is_required: isRequired
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding approval workflow step:', error);
      return null;
    }

    return data;
  }
}
