import { 
  VisitRequest, 
  VisitApproval, 
  VisitApprovalRequest, 
  VisitApprovalResponse, 
  VisitRequestFilter,
  VisitStatus,
  VisitStats
} from '@/types/visit';

/**
 * Client-side service for visit management
 */
export class ClientVisitService {
  /**
   * Get a visit request by ID
   */
  async getVisitRequest(id: string): Promise<VisitRequest | null> {
    try {
      const response = await fetch(`/api/visit/${id}`);
      if (!response.ok) {
        throw new Error(`Failed to get visit request: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting visit request:', error);
      return null;
    }
  }

  /**
   * Get visit requests based on filters
   */
  async getVisitRequests(filter: VisitRequestFilter, limit = 10, offset = 0): Promise<VisitRequest[]> {
    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filter.host_id) params.append('host_id', filter.host_id);
      if (filter.visitor_id) params.append('visitor_id', filter.visitor_id);
      if (filter.location_id) params.append('location_id', filter.location_id);
      if (filter.status && !Array.isArray(filter.status)) params.append('status', filter.status);
      if (filter.status && Array.isArray(filter.status)) {
        filter.status.forEach(s => params.append('status', s));
      }
      if (filter.start_date) params.append('start_date', filter.start_date);
      if (filter.end_date) params.append('end_date', filter.end_date);
      if (filter.search) params.append('search', filter.search);
      params.append('limit', limit.toString());
      params.append('offset', offset.toString());

      const response = await fetch(`/api/visit?${params.toString()}`);
      if (!response.ok) {
        throw new Error(`Failed to get visit requests: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting visit requests:', error);
      return [];
    }
  }

  /**
   * Get visit statistics for a host
   */
  async getVisitStats(hostId: string): Promise<VisitStats> {
    try {
      const response = await fetch(`/api/visit/stats?host_id=${hostId}`);
      if (!response.ok) {
        throw new Error(`Failed to get visit stats: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting visit stats:', error);
      return {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        checked_in: 0,
        checked_out: 0,
        cancelled: 0,
        no_show: 0
      };
    }
  }

  /**
   * Update a visit request status
   */
  async updateVisitStatus(id: string, status: VisitStatus, notes?: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/visit/${id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status, notes }),
      });
      if (!response.ok) {
        throw new Error(`Failed to update visit status: ${response.statusText}`);
      }
      return true;
    } catch (error) {
      console.error('Error updating visit status:', error);
      return false;
    }
  }

  /**
   * Get approvals for a visit
   */
  async getVisitApprovals(visitId: string): Promise<VisitApproval[]> {
    try {
      const response = await fetch(`/api/visit/${visitId}/approvals`);
      if (!response.ok) {
        throw new Error(`Failed to get visit approvals: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting visit approvals:', error);
      return [];
    }
  }

  /**
   * Create a visit approval request
   */
  async createVisitApprovalRequest(request: VisitApprovalRequest): Promise<VisitApproval | null> {
    try {
      const response = await fetch('/api/visit/approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });
      if (!response.ok) {
        throw new Error(`Failed to create visit approval request: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error creating visit approval request:', error);
      return null;
    }
  }

  /**
   * Respond to a visit approval request
   */
  async respondToVisitApprovalRequest(response: VisitApprovalResponse): Promise<boolean> {
    try {
      const apiResponse = await fetch('/api/visit/approval/respond', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(response),
      });
      if (!apiResponse.ok) {
        throw new Error(`Failed to respond to visit approval request: ${apiResponse.statusText}`);
      }
      return true;
    } catch (error) {
      console.error('Error responding to visit approval request:', error);
      return false;
    }
  }

  /**
   * Get pending approval requests for a user
   */
  async getPendingApprovalRequests(userId: string, limit = 10, offset = 0): Promise<VisitRequest[]> {
    try {
      const response = await fetch(`/api/visit/approval/pending?user_id=${userId}&limit=${limit}&offset=${offset}`);
      if (!response.ok) {
        throw new Error(`Failed to get pending approval requests: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting pending approval requests:', error);
      return [];
    }
  }

  /**
   * Get approval workflows for an organization
   */
  async getApprovalWorkflows(organizationId: string) {
    try {
      const response = await fetch(`/api/visit/workflows?organization_id=${organizationId}`);
      if (!response.ok) {
        throw new Error(`Failed to get approval workflows: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting approval workflows:', error);
      return [];
    }
  }

  /**
   * Create an approval workflow
   */
  async createApprovalWorkflow(organizationId: string, name: string, description?: string) {
    try {
      const response = await fetch('/api/visit/workflow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ organization_id: organizationId, name, description }),
      });
      if (!response.ok) {
        throw new Error(`Failed to create approval workflow: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error creating approval workflow:', error);
      return null;
    }
  }

  /**
   * Add a step to an approval workflow
   */
  async addApprovalWorkflowStep(
    workflowId: string,
    stepNumber: number,
    approverType: 'user' | 'role' | 'department',
    approverId: string,
    isRequired = true
  ) {
    try {
      const response = await fetch('/api/visit/workflow/step', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflow_id: workflowId,
          step_number: stepNumber,
          approver_type: approverType,
          approver_id: approverId,
          is_required: isRequired
        }),
      });
      if (!response.ok) {
        throw new Error(`Failed to add approval workflow step: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error adding approval workflow step:', error);
      return null;
    }
  }
}

/**
 * Create a client-side visit service
 */
export function createClientVisitService(): ClientVisitService {
  return new ClientVisitService();
}
