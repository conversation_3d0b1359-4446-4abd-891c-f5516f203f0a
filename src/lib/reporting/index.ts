import { SupabaseClient } from '@supabase/supabase-js'

export interface VisitorTrafficData {
  date: string
  count: number
}

export interface VisitorTrafficByHostData {
  hostId: string
  hostName: string
  count: number
}

export interface VisitorTrafficByLocationData {
  locationId: string
  locationName: string
  count: number
}

export interface VisitorTrafficByPurposeData {
  purpose: string
  count: number
}

export interface VisitorTrafficByCompanyData {
  company: string
  count: number
}

export interface PeakTimeData {
  day: string
  hour: number
  count: number
}

export interface VisitDurationData {
  visitId: string
  duration: number // in minutes
}

export interface VisitDurationByHostData {
  hostId: string
  hostName: string
  averageDuration: number // in minutes
}

export interface VisitDurationByPurposeData {
  purpose: string
  averageDuration: number // in minutes
}

export interface VisitDurationByCompanyData {
  company: string
  averageDuration: number // in minutes
}

export interface LocationVisitDurationData {
  locationId: string
  locationName: string
  averageDuration: number // in minutes
}

export type TimeRange = 'day' | 'week' | 'month' | 'year' | 'custom'

export interface DateRange {
  startDate: Date
  endDate: Date
}

export interface ReportFilters {
  timeRange: TimeRange
  dateRange?: DateRange
  locationIds?: string[]
  hostIds?: string[]
  purposes?: string[]
  companies?: string[]
}

export class ReportingService {
  private supabase: SupabaseClient
  
  // Expose supabase client for direct queries
  public getSupabaseClient(): SupabaseClient {
    return this.supabase
  }

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase
  }

  /**
   * Get visitor traffic data for a specific time range
   */
  async getVisitorTraffic(
    orgId: string,
    filters: ReportFilters
  ): Promise<VisitorTrafficData[]> {
    const { startDate, endDate } = this.getDateRangeFromFilters(filters)
    
    // Format dates for SQL query
    const startDateStr = startDate.toISOString()
    const endDateStr = endDate.toISOString()
    
    // Build query
    let query = this.supabase
      .from('visits')
      .select('check_in_time')
      .eq('org_id', orgId)
      .gte('check_in_time', startDateStr)
      .lte('check_in_time', endDateStr)
      .not('check_in_time', 'is', null)
    
    // Apply additional filters
    if (filters.locationIds && filters.locationIds.length > 0) {
      query = query.in('location_id', filters.locationIds)
    }
    
    if (filters.hostIds && filters.hostIds.length > 0) {
      query = query.in('host_id', filters.hostIds)
    }
    
    if (filters.purposes && filters.purposes.length > 0) {
      query = query.in('purpose', filters.purposes)
    }
    
    // Execute query
    const { data, error } = await query
    
    if (error) {
      console.error('Error fetching visitor traffic data:', error)
      return []
    }
    
    // Process data
    const trafficByDate = new Map<string, number>()
    
    // Initialize all dates in the range
    const currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      const dateStr = this.formatDate(currentDate, filters.timeRange)
      trafficByDate.set(dateStr, 0)
      
      // Increment date based on time range
      if (filters.timeRange === 'day') {
        currentDate.setHours(currentDate.getHours() + 1)
      } else if (filters.timeRange === 'week') {
        currentDate.setDate(currentDate.getDate() + 1)
      } else if (filters.timeRange === 'month') {
        currentDate.setDate(currentDate.getDate() + 1)
      } else if (filters.timeRange === 'year') {
        currentDate.setDate(currentDate.getDate() + 1)
      }
    }
    
    // Count visits by date
    for (const visit of data) {
      if (visit.check_in_time) {
        const checkInDate = new Date(visit.check_in_time)
        const dateStr = this.formatDate(checkInDate, filters.timeRange)
        
        const count = trafficByDate.get(dateStr) || 0
        trafficByDate.set(dateStr, count + 1)
      }
    }
    
    // Convert map to array
    return Array.from(trafficByDate.entries()).map(([date, count]) => ({
      date,
      count
    }))
  }
  
  /**
   * Get visitor traffic data grouped by host
   */
  async getVisitorTrafficByHost(
    orgId: string,
    filters: ReportFilters
  ): Promise<VisitorTrafficByHostData[]> {
    const { startDate, endDate } = this.getDateRangeFromFilters(filters)
    
    // Format dates for SQL query
    const startDateStr = startDate.toISOString()
    const endDateStr = endDate.toISOString()
    
    // Build query to get visits with host data
    const { data, error } = await this.supabase
      .from('visits')
      .select(`
        host_id,
        users!host_id (
          full_name
        )
      `)
      .eq('org_id', orgId)
      .gte('check_in_time', startDateStr)
      .lte('check_in_time', endDateStr)
      .not('check_in_time', 'is', null)
    
    if (error) {
      console.error('Error fetching visitor traffic by host data:', error)
      return []
    }
    
    // Process data
    const trafficByHost = new Map<string, { hostName: string; count: number }>()
    
    for (const visit of data) {
      const hostId = visit.host_id
      // Access the first item in the users array and get its full_name property
      const hostName = visit.users && Array.isArray(visit.users) && visit.users[0]?.full_name || 'Unknown'
      
      const hostData = trafficByHost.get(hostId) || { hostName, count: 0 }
      hostData.count += 1
      
      trafficByHost.set(hostId, hostData)
    }
    
    // Convert map to array and sort by count (descending)
    return Array.from(trafficByHost.entries())
      .map(([hostId, { hostName, count }]) => ({
        hostId,
        hostName,
        count
      }))
      .sort((a, b) => b.count - a.count)
  }
  
  /**
   * Get visitor traffic data grouped by location
   */
  async getVisitorTrafficByLocation(
    orgId: string,
    filters: ReportFilters
  ): Promise<VisitorTrafficByLocationData[]> {
    const { startDate, endDate } = this.getDateRangeFromFilters(filters)
    
    // Format dates for SQL query
    const startDateStr = startDate.toISOString()
    const endDateStr = endDate.toISOString()
    
    // Build query to get visits with location data
    const { data, error } = await this.supabase
      .from('visits')
      .select(`
        location_id,
        locations!location_id (
          name
        )
      `)
      .eq('org_id', orgId)
      .gte('check_in_time', startDateStr)
      .lte('check_in_time', endDateStr)
      .not('check_in_time', 'is', null)
    
    if (error) {
      console.error('Error fetching visitor traffic by location data:', error)
      return []
    }
    
    // Process data
    const trafficByLocation = new Map<string, { locationName: string; count: number }>()
    
    for (const visit of data) {
      const locationId = visit.location_id
      // Access the first item in the locations array and get its name property
      const locationName = visit.locations && Array.isArray(visit.locations) && visit.locations[0]?.name || 'Unknown'
      
      const locationData = trafficByLocation.get(locationId) || { locationName, count: 0 }
      locationData.count += 1
      
      trafficByLocation.set(locationId, locationData)
    }
    
    // Convert map to array and sort by count (descending)
    return Array.from(trafficByLocation.entries())
      .map(([locationId, { locationName, count }]) => ({
        locationId,
        locationName,
        count
      }))
      .sort((a, b) => b.count - a.count)
  }
  
  /**
   * Get visitor traffic data grouped by purpose
   */
  async getVisitorTrafficByPurpose(
    orgId: string,
    filters: ReportFilters
  ): Promise<VisitorTrafficByPurposeData[]> {
    const { startDate, endDate } = this.getDateRangeFromFilters(filters)
    
    // Format dates for SQL query
    const startDateStr = startDate.toISOString()
    const endDateStr = endDate.toISOString()
    
    // Build query
    const { data, error } = await this.supabase
      .from('visits')
      .select('purpose')
      .eq('org_id', orgId)
      .gte('check_in_time', startDateStr)
      .lte('check_in_time', endDateStr)
      .not('check_in_time', 'is', null)
    
    if (error) {
      console.error('Error fetching visitor traffic by purpose data:', error)
      return []
    }
    
    // Process data
    const trafficByPurpose = new Map<string, number>()
    
    for (const visit of data) {
      const purpose = visit.purpose || 'Not specified'
      const count = trafficByPurpose.get(purpose) || 0
      trafficByPurpose.set(purpose, count + 1)
    }
    
    // Convert map to array and sort by count (descending)
    return Array.from(trafficByPurpose.entries())
      .map(([purpose, count]) => ({
        purpose,
        count
      }))
      .sort((a, b) => b.count - a.count)
  }
  
  /**
   * Get visitor traffic data grouped by company
   */
  async getVisitorTrafficByCompany(
    orgId: string,
    filters: ReportFilters
  ): Promise<VisitorTrafficByCompanyData[]> {
    const { startDate, endDate } = this.getDateRangeFromFilters(filters)
    
    // Format dates for SQL query
    const startDateStr = startDate.toISOString()
    const endDateStr = endDate.toISOString()
    
    // Build query to get visits with visitor data
    const { data, error } = await this.supabase
      .from('visits')
      .select(`
        visitor_id,
        visitors!visitor_id (
          company
        )
      `)
      .eq('org_id', orgId)
      .gte('check_in_time', startDateStr)
      .lte('check_in_time', endDateStr)
      .not('check_in_time', 'is', null)
    
    if (error) {
      console.error('Error fetching visitor traffic by company data:', error)
      return []
    }
    
    // Process data
    const trafficByCompany = new Map<string, number>()
    
    for (const visit of data) {
      // Access the first item in the visitors array and get its company property
      const company = visit.visitors && Array.isArray(visit.visitors) && visit.visitors[0]?.company || 'Not specified'
      const count = trafficByCompany.get(company) || 0
      trafficByCompany.set(company, count + 1)
    }
    
    // Convert map to array and sort by count (descending)
    return Array.from(trafficByCompany.entries())
      .map(([company, count]) => ({
        company,
        count
      }))
      .sort((a, b) => b.count - a.count)
  }
  
  /**
   * Get peak time data (visitor traffic by day and hour)
   */
  async getPeakTimeData(
    orgId: string,
    filters: ReportFilters
  ): Promise<PeakTimeData[]> {
    const { startDate, endDate } = this.getDateRangeFromFilters(filters)
    
    // Format dates for SQL query
    const startDateStr = startDate.toISOString()
    const endDateStr = endDate.toISOString()
    
    // Build query
    const { data, error } = await this.supabase
      .from('visits')
      .select('check_in_time')
      .eq('org_id', orgId)
      .gte('check_in_time', startDateStr)
      .lte('check_in_time', endDateStr)
      .not('check_in_time', 'is', null)
    
    if (error) {
      console.error('Error fetching peak time data:', error)
      return []
    }
    
    // Process data
    const peakTimeMap = new Map<string, number>()
    
    for (const visit of data) {
      if (visit.check_in_time) {
        const checkInDate = new Date(visit.check_in_time)
        const day = checkInDate.toLocaleDateString('en-US', { weekday: 'long' })
        const hour = checkInDate.getHours()
        const key = `${day}-${hour}`
        
        const count = peakTimeMap.get(key) || 0
        peakTimeMap.set(key, count + 1)
      }
    }
    
    // Convert map to array
    return Array.from(peakTimeMap.entries()).map(([key, count]) => {
      const [day, hourStr] = key.split('-')
      const hour = parseInt(hourStr, 10)
      
      return {
        day,
        hour,
        count
      }
    })
  }
  
  /**
   * Get visit duration data
   */
  async getVisitDurationData(
    orgId: string,
    filters: ReportFilters
  ): Promise<VisitDurationData[]> {
    const { startDate, endDate } = this.getDateRangeFromFilters(filters)
    
    // Format dates for SQL query
    const startDateStr = startDate.toISOString()
    const endDateStr = endDate.toISOString()
    
    // Build query
    const { data, error } = await this.supabase
      .from('visits')
      .select('id, check_in_time, check_out_time')
      .eq('org_id', orgId)
      .gte('check_in_time', startDateStr)
      .lte('check_in_time', endDateStr)
      .not('check_in_time', 'is', null)
      .not('check_out_time', 'is', null)
    
    if (error) {
      console.error('Error fetching visit duration data:', error)
      return []
    }
    
    // Process data
    return data.map(visit => {
      const checkInTime = new Date(visit.check_in_time as string).getTime()
      const checkOutTime = new Date(visit.check_out_time as string).getTime()
      const durationMs = checkOutTime - checkInTime
      const durationMinutes = Math.round(durationMs / (1000 * 60))
      
      return {
        visitId: visit.id,
        duration: durationMinutes
      }
    })
  }
  
  /**
   * Get average visit duration by host
   */
  async getVisitDurationByHost(
    orgId: string,
    filters: ReportFilters
  ): Promise<VisitDurationByHostData[]> {
    const { startDate, endDate } = this.getDateRangeFromFilters(filters)
    
    // Format dates for SQL query
    const startDateStr = startDate.toISOString()
    const endDateStr = endDate.toISOString()
    
    // Build query
    const { data, error } = await this.supabase
      .from('visits')
      .select(`
        id, 
        check_in_time, 
        check_out_time, 
        host_id,
        users!host_id (
          full_name
        )
      `)
      .eq('org_id', orgId)
      .gte('check_in_time', startDateStr)
      .lte('check_in_time', endDateStr)
      .not('check_in_time', 'is', null)
      .not('check_out_time', 'is', null)
    
    if (error) {
      console.error('Error fetching visit duration by host data:', error)
      return []
    }
    
    // Process data
    const durationByHost = new Map<string, { hostName: string; totalDuration: number; count: number }>()
    
    for (const visit of data) {
      const hostId = visit.host_id
      // Access the first item in the users array and get its full_name property
      const hostName = visit.users && Array.isArray(visit.users) && visit.users[0]?.full_name || 'Unknown'
      
      const checkInTime = new Date(visit.check_in_time as string).getTime()
      const checkOutTime = new Date(visit.check_out_time as string).getTime()
      const durationMinutes = Math.round((checkOutTime - checkInTime) / (1000 * 60))
      
      const hostData = durationByHost.get(hostId) || { hostName, totalDuration: 0, count: 0 }
      hostData.totalDuration += durationMinutes
      hostData.count += 1
      
      durationByHost.set(hostId, hostData)
    }
    
    // Calculate average duration and convert map to array
    return Array.from(durationByHost.entries())
      .map(([hostId, { hostName, totalDuration, count }]) => ({
        hostId,
        hostName,
        averageDuration: Math.round(totalDuration / count)
      }))
      .sort((a, b) => b.averageDuration - a.averageDuration)
  }
  
  /**
   * Get average visit duration by purpose
   */
  async getVisitDurationByPurpose(
    orgId: string,
    filters: ReportFilters
  ): Promise<VisitDurationByPurposeData[]> {
    const { startDate, endDate } = this.getDateRangeFromFilters(filters)
    
    // Format dates for SQL query
    const startDateStr = startDate.toISOString()
    const endDateStr = endDate.toISOString()
    
    // Build query
    const { data, error } = await this.supabase
      .from('visits')
      .select('id, check_in_time, check_out_time, purpose')
      .eq('org_id', orgId)
      .gte('check_in_time', startDateStr)
      .lte('check_in_time', endDateStr)
      .not('check_in_time', 'is', null)
      .not('check_out_time', 'is', null)
    
    if (error) {
      console.error('Error fetching visit duration by purpose data:', error)
      return []
    }
    
    // Process data
    const durationByPurpose = new Map<string, { totalDuration: number; count: number }>()
    
    for (const visit of data) {
      const purpose = visit.purpose || 'Not specified'
      
      const checkInTime = new Date(visit.check_in_time as string).getTime()
      const checkOutTime = new Date(visit.check_out_time as string).getTime()
      const durationMinutes = Math.round((checkOutTime - checkInTime) / (1000 * 60))
      
      const purposeData = durationByPurpose.get(purpose) || { totalDuration: 0, count: 0 }
      purposeData.totalDuration += durationMinutes
      purposeData.count += 1
      
      durationByPurpose.set(purpose, purposeData)
    }
    
    // Calculate average duration and convert map to array
    return Array.from(durationByPurpose.entries())
      .map(([purpose, { totalDuration, count }]) => ({
        purpose,
        averageDuration: Math.round(totalDuration / count)
      }))
      .sort((a, b) => b.averageDuration - a.averageDuration)
  }
  
  /**
   * Get average visit duration by company
   */
  async getVisitDurationByCompany(
    orgId: string,
    filters: ReportFilters
  ): Promise<VisitDurationByCompanyData[]> {
    const { startDate, endDate } = this.getDateRangeFromFilters(filters)
    
    // Format dates for SQL query
    const startDateStr = startDate.toISOString()
    const endDateStr = endDate.toISOString()
    
    // Build query
    const { data, error } = await this.supabase
      .from('visits')
      .select(`
        id, 
        check_in_time, 
        check_out_time, 
        visitor_id,
        visitors!visitor_id (
          company
        )
      `)
      .eq('org_id', orgId)
      .gte('check_in_time', startDateStr)
      .lte('check_in_time', endDateStr)
      .not('check_in_time', 'is', null)
      .not('check_out_time', 'is', null)
    
    if (error) {
      console.error('Error fetching visit duration by company data:', error)
      return []
    }
    
    // Process data
    const durationByCompany = new Map<string, { totalDuration: number; count: number }>()
    
    for (const visit of data) {
      // Access the first item in the visitors array and get its company property
      const company = visit.visitors && Array.isArray(visit.visitors) && visit.visitors[0]?.company || 'Not specified'
      
      const checkInTime = new Date(visit.check_in_time as string).getTime()
      const checkOutTime = new Date(visit.check_out_time as string).getTime()
      const durationMinutes = Math.round((checkOutTime - checkInTime) / (1000 * 60))
      
      const companyData = durationByCompany.get(company) || { totalDuration: 0, count: 0 }
      companyData.totalDuration += durationMinutes
      companyData.count += 1
      
      durationByCompany.set(company, companyData)
    }
    
    // Calculate average duration and convert map to array
    return Array.from(durationByCompany.entries())
      .map(([company, { totalDuration, count }]) => ({
        company,
        averageDuration: Math.round(totalDuration / count)
      }))
      .sort((a, b) => b.averageDuration - a.averageDuration)
  }
  
  /**
   * Get average visit duration by location
   */
  async getLocationVisitDuration(
    orgId: string,
    filters: ReportFilters
  ): Promise<LocationVisitDurationData[]> {
    const { startDate, endDate } = this.getDateRangeFromFilters(filters)
    
    // Format dates for SQL query
    const startDateStr = startDate.toISOString()
    const endDateStr = endDate.toISOString()
    
    // Build query
    const { data, error } = await this.supabase
      .from('visits')
      .select(`
        id, 
        check_in_time, 
        check_out_time, 
        location_id,
        locations!location_id (
          name
        )
      `)
      .eq('org_id', orgId)
      .gte('check_in_time', startDateStr)
      .lte('check_in_time', endDateStr)
      .not('check_in_time', 'is', null)
      .not('check_out_time', 'is', null)
    
    if (error) {
      console.error('Error fetching location visit duration data:', error)
      return []
    }
    
    // Process data
    const durationByLocation = new Map<string, { locationName: string; totalDuration: number; count: number }>()
    
    for (const visit of data) {
      const locationId = visit.location_id
      // Access the first item in the locations array and get its name property
      const locationName = visit.locations && Array.isArray(visit.locations) && visit.locations[0]?.name || 'Unknown'
      
      const checkInTime = new Date(visit.check_in_time as string).getTime()
      const checkOutTime = new Date(visit.check_out_time as string).getTime()
      const durationMinutes = Math.round((checkOutTime - checkInTime) / (1000 * 60))
      
      const locationData = durationByLocation.get(locationId) || { 
        locationName, 
        totalDuration: 0, 
        count: 0 
      }
      
      locationData.totalDuration += durationMinutes
      locationData.count += 1
      
      durationByLocation.set(locationId, locationData)
    }
    
    // Calculate average duration and convert map to array
    return Array.from(durationByLocation.entries())
      .map(([locationId, { locationName, totalDuration, count }]) => ({
        locationId,
        locationName,
        averageDuration: Math.round(totalDuration / count)
      }))
      .sort((a, b) => b.averageDuration - a.averageDuration)
  }
  
  /**
   * Helper method to get date range from filters
   */
  private getDateRangeFromFilters(filters: ReportFilters): DateRange {
    if (filters.dateRange) {
      return filters.dateRange
    }
    
    const endDate = new Date()
    const startDate = new Date()
    
    switch (filters.timeRange) {
      case 'day':
        startDate.setHours(0, 0, 0, 0)
        break
      case 'week':
        startDate.setDate(startDate.getDate() - 7)
        break
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1)
        break
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1)
        break
      default:
        startDate.setDate(startDate.getDate() - 30) // Default to 30 days
    }
    
    return { startDate, endDate }
  }
  
  /**
   * Helper method to format date based on time range
   */
  private formatDate(date: Date, timeRange: TimeRange): string {
    switch (timeRange) {
      case 'day':
        return date.toLocaleTimeString('en-US', { hour: '2-digit' })
      case 'week':
      case 'month':
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
      case 'year':
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
      default:
        return date.toLocaleDateString('en-US')
    }
  }
}
