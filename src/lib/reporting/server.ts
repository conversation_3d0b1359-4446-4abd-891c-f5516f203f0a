import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import { cookies } from 'next/headers'
import { ReportingService } from '.'

/**
 * Create a reporting service instance for server components
 */
export async function createServerReportingService(): Promise<ReportingService> {
  const supabase = createServerComponentClient()
  return new ReportingService(supabase)
}
