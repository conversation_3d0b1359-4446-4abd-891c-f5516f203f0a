import { SubscriptionPlan } from '@/types'

// Define the subscription plans
export const PLANS: Record<string, SubscriptionPlan> = {
  basic: {
    id: 'basic',
    name: 'Basic',
    description: 'Perfect for small businesses just getting started',
    price: 49,
    features: [
      'Up to 100 visitors per month',
      'Up to 1 location',
      'Up to 5 users',
      'Email notifications',
      'Basic visitor check-in',
      'Standard support',
    ],
    visitorLimit: 100,
    locationLimit: 1,
    userLimit: 5,
    customFields: false,
    analytics: false,
    apiAccess: false,
  },
  professional: {
    id: 'professional',
    name: 'Professional',
    description: 'Ideal for growing businesses with multiple locations',
    price: 99,
    features: [
      'Up to 500 visitors per month',
      'Up to 3 locations',
      'Up to 15 users',
      'Email & SMS notifications',
      'Custom check-in fields',
      'Basic analytics',
      'Priority support',
    ],
    visitorLimit: 500,
    locationLimit: 3,
    userLimit: 15,
    customFields: true,
    analytics: true,
    apiAccess: false,
  },
  business: {
    id: 'business',
    name: 'Business',
    description: 'For established businesses with advanced needs',
    price: 199,
    features: [
      'Up to 2,000 visitors per month',
      'Up to 10 locations',
      'Up to 50 users',
      'Email & SMS notifications',
      'Advanced custom fields',
      'Comprehensive analytics',
      'API access',
      'Dedicated support',
    ],
    visitorLimit: 2000,
    locationLimit: 10,
    userLimit: 50,
    customFields: true,
    analytics: true,
    apiAccess: true,
  },
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Custom solution for large organizations',
    price: 0, // Custom pricing
    features: [
      'Unlimited visitors',
      'Unlimited locations',
      'Unlimited users',
      'All available features',
      'Custom integrations',
      'Dedicated account manager',
      'SLA guarantees',
      '24/7 premium support',
    ],
    visitorLimit: Infinity,
    locationLimit: Infinity,
    userLimit: Infinity,
    customFields: true,
    analytics: true,
    apiAccess: true,
  },
}

// Get all plans as an array
export const getAllPlans = (): SubscriptionPlan[] => {
  return Object.values(PLANS)
}

// Get a plan by ID
export const getPlanById = (id: string): SubscriptionPlan | undefined => {
  return PLANS[id]
}

// Get Stripe price IDs for each plan
// These must be created in the Stripe dashboard and referenced here
// Format should be 'price_1234567890' not the actual price amount

// Log the environment variables for debugging
console.log('Stripe Price IDs from environment variables:', {
  basic: process.env.NEXT_PUBLIC_STRIPE_PRICE_BASIC,
  professional: process.env.NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL,
  business: process.env.NEXT_PUBLIC_STRIPE_PRICE_BUSINESS,
});

// Helper function to validate price IDs
const isValidPriceId = (priceId?: string): boolean => {
  return !!priceId && priceId.startsWith('price_') && priceId.length > 10 && !priceId.includes('XXXXXXX');
};

// Get the price IDs from environment variables or use placeholders
const basicPriceId = process.env.NEXT_PUBLIC_STRIPE_PRICE_BASIC;
const professionalPriceId = process.env.NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL;
const businessPriceId = process.env.NEXT_PUBLIC_STRIPE_PRICE_BUSINESS;

export const STRIPE_PRICE_IDS: Record<string, string> = {
  // Replace these with your actual Stripe price IDs
  // You need to create these in the Stripe dashboard first
  basic: isValidPriceId(basicPriceId) ? basicPriceId! : 'price_placeholder_basic',
  professional: isValidPriceId(professionalPriceId) ? professionalPriceId! : 'price_placeholder_professional',
  business: isValidPriceId(businessPriceId) ? businessPriceId! : 'price_placeholder_business',
  // Enterprise plans are typically custom priced and created on demand
}
