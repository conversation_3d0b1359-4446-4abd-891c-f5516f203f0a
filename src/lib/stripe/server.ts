import Stripe from 'stripe'

// Log Stripe configuration for debugging
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
console.log('Stripe configuration:', {
  secretKeyExists: !!stripeSecretKey,
  secretKeyLength: stripeSecretKey?.length,
  secretKeyPrefix: stripeSecretKey?.substring(0, 7),
});

if (!stripeSecretKey) {
  console.error('STRIPE_SECRET_KEY is not set. Stripe functionality will not work.');
}

// Create Stripe instance with error handling
let stripe: Stripe;
try {
  stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: '2025-02-24.acacia', // Use the latest API version
    appInfo: {
      name: 'Visitor Management SaaS',
      version: '0.1.0',
    },
  });
} catch (error) {
  console.error('Error initializing Stripe:', error);
  // Create a dummy Stripe instance that will throw errors when used
  // This allows the app to start even if Stripe is not configured
  stripe = {} as Stripe;
}

export { stripe }

/**
 * Create a Stripe customer
 */
export async function createCustomer({
  email,
  name,
  orgId,
}: {
  email: string
  name: string
  orgId: string
}) {
  return stripe.customers.create({
    email,
    name,
    metadata: {
      orgId,
    },
  })
}

/**
 * Create a Stripe checkout session for subscription
 */
export async function createCheckoutSession({
  customerId,
  priceId,
  orgId,
  userId,
  successUrl,
  cancelUrl,
}: {
  customerId: string
  priceId: string
  orgId: string
  userId: string
  successUrl: string
  cancelUrl: string
}) {
  // Log the parameters for debugging
  console.log('Creating checkout session with:', {
    customerId,
    priceId,
    orgId,
    userId,
    successUrl,
    cancelUrl,
  });

  try {
    // Validate the price ID
    if (!priceId || !priceId.startsWith('price_')) {
      throw new Error(`Invalid price ID: ${priceId}. Price IDs must start with 'price_'.`);
    }

    // Create the checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        orgId,
        userId,
      },
    });

    console.log('Checkout session created successfully:', {
      sessionId: session.id,
      url: session.url,
    });

    return session;
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw new Error(`Stripe API Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create a Stripe checkout session for the enterprise plan
 * This creates a custom checkout session for a custom price
 */
export async function createEnterpriseCheckoutSession({
  customerId,
  orgId,
  userId,
  successUrl,
  cancelUrl,
}: {
  customerId: string
  orgId: string
  userId: string
  successUrl: string
  cancelUrl: string
}) {
  // For enterprise plans, we'll create a custom checkout session
  // that will require a Stripe representative to review and approve
  return stripe.checkout.sessions.create({
    customer: customerId,
    payment_method_types: ['card'],
    mode: 'setup',
    success_url: successUrl,
    cancel_url: cancelUrl,
    metadata: {
      orgId,
      userId,
      planId: 'enterprise',
    },
  })
}

/**
 * Retrieve a subscription by ID
 */
export async function getSubscription(subscriptionId: string) {
  return stripe.subscriptions.retrieve(subscriptionId)
}

/**
 * Update a subscription
 */
export async function updateSubscription({
  subscriptionId,
  priceId,
}: {
  subscriptionId: string
  priceId: string
}) {
  return stripe.subscriptions.update(subscriptionId, {
    items: [
      {
        id: subscriptionId,
        price: priceId,
      },
    ],
  })
}

/**
 * Cancel a subscription
 */
export async function cancelSubscription(subscriptionId: string) {
  return stripe.subscriptions.cancel(subscriptionId)
}

/**
 * Create a billing portal session
 */
export async function createBillingPortalSession({
  customerId,
  returnUrl,
}: {
  customerId: string
  returnUrl: string
}) {
  return stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: returnUrl,
  })
}

/**
 * Get a checkout session by ID
 */
export async function getCheckoutSession(sessionId: string) {
  return stripe.checkout.sessions.retrieve(sessionId)
}
