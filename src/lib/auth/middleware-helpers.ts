import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';
import { createAdminClient } from '@/lib/supabase/admin';

// This file contains server-side code that uses the admin client
// It should never be imported in client-side code
// It is only used in the middleware

/**
 * Ensures that a user record exists in the public.users table
 * This is a simplified version for middleware use
 *
 * @param supabase Supabase client
 * @param userId User ID from auth.users
 * @returns True if the user exists or was created, false otherwise
 */
export async function ensureUserExistsMiddleware(
  supabase: SupabaseClient<Database>,
  userId: string
): Promise<boolean> {
  // Create an admin client to bypass RLS
  const supabaseAdmin = createAdminClient();
  try {
    console.log('[Middleware] Checking if user exists:', userId);

    // Check if the user already exists in the public.users table
    const { data: existingUser, error: fetchError } = await supabaseAdmin
      .from('users')
      .select('id, org_id')
      .eq('id', userId)
      .single();

    if (!fetchError && existingUser) {
      // User already exists
      console.log('[Middleware] User exists in public.users table with organization:', existingUser.org_id);
      return true;
    }

    // User doesn't exist in the public.users table
    console.log('[Middleware] User does not exist in public.users table');
    console.log('[Middleware] User needs to complete registration with an organization');

    // We don't create users automatically anymore - they need to sign up with an organization
    return false;
  } catch (error) {
    console.error('[Middleware] Error in ensureUserExistsMiddleware:', error);
    return false;
  }
}
