import { createClient } from '@/lib/supabase/client';

/**
 * Ensures that a user record exists in the public.users table
 * This is a client-side utility function that calls the API
 *
 * @param organizationName Optional organization name to create a new organization
 * @returns The user record
 */
export async function ensureUserExistsClient(organizationName?: string) {
  try {
    // Check if we have a session before proceeding
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      console.error('No session available in ensureUserExistsClient');
      throw new Error('No session available');
    }

    console.log('Session found, calling API to ensure user exists...');

    // Get user details from the session
    const userId = session.user.id;
    const email = session.user.email;
    const fullName = session.user.user_metadata?.full_name || session.user.user_metadata?.name || '';

    console.log('User details:', { userId, email, fullName, organizationName });

    const response = await fetch('/api/auth/ensure-user', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        organizationName,
        userId,
        email,
        fullName
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('API error response:', error);
      throw new Error(error.error || 'Failed to ensure user exists');
    }

    const result = await response.json();
    console.log('API response:', result);
    return result;
  } catch (error) {
    console.error('Error ensuring user exists:', error);
    throw error;
  }
}
