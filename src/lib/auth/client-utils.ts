import { createClient } from '@/lib/supabase/client';
import { Session } from '@supabase/supabase-js';

/**
 * Ensures that a user record exists in the public.users table
 * This is a client-side utility function that calls the API
 *
 * @param organizationName Optional organization name to create a new organization
 * @param session Optional session object to use instead of fetching it
 * @returns The user record
 */
export async function ensureUserExistsClient(organizationName?: string, session?: Session | null) {
  try {
    console.log('ensureUserExistsClient called with:', {
      hasOrganizationName: !!organizationName,
      hasSessionParam: !!session,
      sessionUserId: session?.user?.id
    });

    let userSession = session;

    // If no session provided, try to get it
    if (!userSession) {
      console.log('No session provided, fetching from Supabase...');
      const supabase = createClient();
      const { data: { session: fetchedSession }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Error fetching session:', error);
        throw new Error(`Error fetching session: ${error.message}`);
      }

      userSession = fetchedSession;
      console.log('Fetched session:', {
        hasSession: !!fetchedSession,
        userId: fetchedSession?.user?.id
      });
    } else {
      console.log('Using provided session, skipping Supabase client creation');
    }

    if (!userSession) {
      console.error('No session available in ensureUserExistsClient');
      throw new Error('No session available');
    }

    console.log('Session found, calling API to ensure user exists...');

    // Get user details from the session
    const userId = userSession.user.id;
    const email = userSession.user.email;
    const fullName = userSession.user.user_metadata?.full_name || userSession.user.user_metadata?.name || '';

    console.log('User details:', { userId, email, fullName, organizationName });

    const response = await fetch('/api/auth/ensure-user', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        organizationName: organizationName || undefined, // Send undefined if no org name
        userId,
        email,
        fullName
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('API error response:', error);

      // If the error is about organization name being required, it means this is a new user
      // who needs to complete registration
      if (error.error && error.error.includes('Organization name is required')) {
        console.log('User needs to complete registration - redirecting to signup');
        throw new Error('Organization name is required');
      }

      throw new Error(error.error || 'Failed to ensure user exists');
    }

    const result = await response.json();
    console.log('API response:', result);
    return result;
  } catch (error) {
    console.error('Error ensuring user exists:', error);
    throw error;
  }
}
