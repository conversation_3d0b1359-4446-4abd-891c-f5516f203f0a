import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

// This file contains server-side code that uses the admin client
// It should never be imported in client-side code

/**
 * Ensures that a user record exists in the public.users table
 * If the user doesn't exist, creates a new record with the provided organization
 *
 * @param supabase Supabase client
 * @param userId User ID from auth.users
 * @param organizationId Organization ID to associate with the user
 * @returns The user record
 */
export async function ensureUserExists(
  supabase: SupabaseClient<Database>,
  userId: string,
  organizationId: string
) {
  try {
    console.log('Ensuring user exists:', userId);

    // First, check if the user already exists in the public.users table
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (!fetchError && existingUser) {
      console.log('User already exists in public.users table');
      return existingUser;
    }

    console.log('User does not exist in public.users table, creating...');

    // Get the user's session to get their email
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Error getting session in ensureUserExists:', sessionError);
      throw new Error(`Error getting session: ${sessionError.message}`);
    }

    if (!session) {
      console.error('No session available in ensureUserExists');
      throw new Error('No session available');
    }

    console.log('Session found in ensureUserExists, user ID:', session.user.id);

    const user = session.user;

    // Verify that the organization exists
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name')
      .eq('id', organizationId)
      .single();

    if (orgError || !organization) {
      console.error('Error finding organization:', orgError);
      throw new Error(`Organization not found: ${organizationId}`);
    }

    console.log('Using organization:', organization.name);

    // Create a new user record
    const newUser = {
      id: userId,
      org_id: organizationId,
      full_name: user.user_metadata?.full_name || user.user_metadata?.name || '',
      email: user.email || '',
      role: 'admin', // Default role for new users
    };

    console.log('Creating new user record:', newUser);

    const { data: createdUser, error: createError } = await supabase
      .from('users')
      .insert([newUser])
      .select('*')
      .single();

    if (createError) {
      console.error('Error creating user:', createError);
      throw new Error(`Error creating user: ${createError.message}`);
    }

    console.log('User created successfully, creating user_organizations record');

    // Create user_organizations record for RLS policies
    const { error: userOrgError } = await supabase
      .from('user_organizations')
      .insert([{
        user_id: userId,
        organization_id: organizationId,
        role: 'admin'
      }]);

    if (userOrgError) {
      console.error('Error creating user_organizations record:', userOrgError);
      // Continue anyway - the user record was created
    } else {
      console.log('user_organizations record created successfully');
    }

    return createdUser;
  } catch (error) {
    console.error('Error in ensureUserExists:', error);
    throw error;
  }
}


