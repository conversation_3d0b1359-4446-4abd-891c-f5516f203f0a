import { BadgeField, BadgeFieldType, BadgeTemplateData, Visit, Visitor } from '@/types'
import { v4 as uuidv4 } from 'uuid'

/**
 * Create a new empty badge template with default values
 */
export function createEmptyBadgeTemplate(): BadgeTemplateData {
  return {
    name: 'New Badge Template',
    width: 86, // Standard ID card width in mm
    height: 54, // Standard ID card height in mm
    backgroundColor: '#ffffff',
    borderColor: '#cccccc',
    borderWidth: 1,
    fields: [],
    isDefault: false,
  }
}

/**
 * Create a new badge field with default values based on field type
 */
export function createBadgeField(type: BadgeFieldType): BadgeField {
  const baseField: BadgeField = {
    id: uuidv4(),
    type,
    x: 10,
    y: 10,
    width: 80,
    height: 10,
  }

  switch (type) {
    case BadgeFieldType.TEXT:
      return {
        ...baseField,
        value: 'Text Field',
        fontSize: 14,
        fontColor: '#000000',
        fontWeight: 'normal',
        alignment: 'left',
      }
    case BadgeFieldType.VISITOR_NAME:
      return {
        ...baseField,
        fontSize: 18,
        fontColor: '#000000',
        fontWeight: 'bold',
        alignment: 'center',
      }
    case BadgeFieldType.VISITOR_PHOTO:
      return {
        ...baseField,
        width: 30,
        height: 40,
      }
    case BadgeFieldType.QR_CODE:
    case BadgeFieldType.BARCODE:
      return {
        ...baseField,
        width: 30,
        height: 30,
      }
    case BadgeFieldType.ORGANIZATION_LOGO:
      return {
        ...baseField,
        width: 30,
        height: 15,
      }
    default:
      return baseField
  }
}

/**
 * Get a display name for a badge field type
 */
export function getBadgeFieldTypeName(type: BadgeFieldType): string {
  switch (type) {
    case BadgeFieldType.TEXT:
      return 'Text'
    case BadgeFieldType.IMAGE:
      return 'Image'
    case BadgeFieldType.VISITOR_NAME:
      return 'Visitor Name'
    case BadgeFieldType.VISITOR_COMPANY:
      return 'Visitor Company'
    case BadgeFieldType.VISITOR_EMAIL:
      return 'Visitor Email'
    case BadgeFieldType.VISITOR_PHOTO:
      return 'Visitor Photo'
    case BadgeFieldType.HOST_NAME:
      return 'Host Name'
    case BadgeFieldType.VISIT_DATE:
      return 'Visit Date'
    case BadgeFieldType.VISIT_PURPOSE:
      return 'Visit Purpose'
    case BadgeFieldType.QR_CODE:
      return 'QR Code'
    case BadgeFieldType.BARCODE:
      return 'Barcode'
    case BadgeFieldType.ORGANIZATION_LOGO:
      return 'Organization Logo'
    case BadgeFieldType.ORGANIZATION_NAME:
      return 'Organization Name'
    case BadgeFieldType.LOCATION_NAME:
      return 'Location Name'
    default:
      return 'Unknown Field'
  }
}

/**
 * Generate field value based on visitor and visit data
 */
export function generateFieldValue(
  field: BadgeField,
  visitor: Visitor,
  visit: Visit,
  hostName: string,
  orgName: string,
  locationName?: string
): string {
  switch (field.type) {
    case BadgeFieldType.TEXT:
      return field.value || ''
    case BadgeFieldType.VISITOR_NAME:
      return visitor.full_name
    case BadgeFieldType.VISITOR_COMPANY:
      return visitor.company
    case BadgeFieldType.VISITOR_EMAIL:
      return visitor.email
    case BadgeFieldType.HOST_NAME:
      return hostName
    case BadgeFieldType.VISIT_DATE:
      return new Date(visit.check_in_time || visit.scheduled_time || '').toLocaleDateString()
    case BadgeFieldType.VISIT_PURPOSE:
      return visit.purpose
    case BadgeFieldType.ORGANIZATION_NAME:
      return orgName
    case BadgeFieldType.LOCATION_NAME:
      return locationName || 'Unknown Location'
    default:
      return ''
  }
}

/**
 * Convert badge template to CSS styles for rendering
 */
export function badgeTemplateToStyles(template: BadgeTemplateData): string {
  const { width, height, backgroundColor, borderColor, borderWidth } = template
  
  return `
    .badge-container {
      width: ${width}mm;
      height: ${height}mm;
      background-color: ${backgroundColor};
      ${borderColor && borderWidth ? `border: ${borderWidth}px solid ${borderColor};` : ''}
      position: relative;
      box-sizing: border-box;
      overflow: hidden;
      page-break-inside: avoid;
    }
    
    @media print {
      .badge-container {
        width: ${width}mm;
        height: ${height}mm;
        margin: 0;
        padding: 0;
      }
      
      @page {
        size: ${width + 10}mm ${height + 10}mm;
        margin: 5mm;
      }
    }
  `
}

/**
 * Convert badge field to CSS styles for rendering
 */
export function badgeFieldToStyles(field: BadgeField): string {
  const { x, y, width, height, rotation } = field
  
  let styles = `
    .field-${field.id} {
      position: absolute;
      left: ${x}%;
      top: ${y}%;
      width: ${width}%;
      height: ${height}%;
      ${rotation ? `transform: rotate(${rotation}deg);` : ''}
    }
  `
  
  if (field.type === BadgeFieldType.TEXT || 
      field.type === BadgeFieldType.VISITOR_NAME || 
      field.type === BadgeFieldType.VISITOR_COMPANY || 
      field.type === BadgeFieldType.VISITOR_EMAIL || 
      field.type === BadgeFieldType.HOST_NAME || 
      field.type === BadgeFieldType.VISIT_DATE || 
      field.type === BadgeFieldType.VISIT_PURPOSE || 
      field.type === BadgeFieldType.ORGANIZATION_NAME ||
      field.type === BadgeFieldType.LOCATION_NAME) {
    styles += `
      .field-${field.id} {
        font-size: ${field.fontSize || 14}px;
        color: ${field.fontColor || '#000000'};
        font-weight: ${field.fontWeight || 'normal'};
        text-align: ${field.alignment || 'left'};
        display: flex;
        align-items: center;
        ${field.alignment === 'center' ? 'justify-content: center;' : ''}
        ${field.alignment === 'right' ? 'justify-content: flex-end;' : ''}
        overflow: hidden;
      }
    `
  }
  
  return styles
}

/**
 * Generate a QR code data URL
 */
export function generateQRCode(data: string): string {
  // In a real implementation, you would use a QR code library
  // For now, we'll return a placeholder
  return `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(data)}`
}

/**
 * Generate a barcode data URL
 */
export function generateBarcode(data: string): string {
  // In a real implementation, you would use a barcode library
  // For now, we'll return a placeholder
  return `https://bwipjs-api.metafloor.com/?bcid=code128&text=${encodeURIComponent(data)}&scale=2&includetext&textxalign=center`
}
