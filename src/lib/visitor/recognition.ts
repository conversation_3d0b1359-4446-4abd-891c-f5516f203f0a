/* eslint-disable */
// @ts-nocheck
import { createClient } from '@/lib/supabase/server';
import { Visitor } from '@/types';

export interface VisitorMatch {
  visitor: Visitor;
  confidence: number;
  matchedOn: string[];
}

export class VisitorRecognitionService {
  private supabase;

  constructor(supabaseClient: any) {
    this.supabase = supabaseClient;
  }

  /**
   * Find potential matches for a visitor based on provided information
   * @param visitorInfo Partial visitor information to match against
   * @param threshold Minimum confidence threshold (0-100)
   * @returns Array of potential visitor matches with confidence scores
   */
  async findPotentialMatches(
    visitorInfo: Partial<Visitor>,
    threshold: number = 70
  ): Promise<VisitorMatch[]> {
    // Start with empty matches array
    const matches: VisitorMatch[] = [];

    // Extract key identifiers for matching
    const { email, phone, full_name } = visitorInfo;

    // If we have an email, it's the strongest identifier
    if (email) {
      const { data: emailMatches } = await this.supabase
        .from('visitors')
        .select('*')
        .ilike('email', email)
        .order('created_at', { ascending: false });

      if (emailMatches && emailMatches.length > 0) {
        emailMatches.forEach(visitor => {
          matches.push({
            visitor: visitor as Visitor,
            confidence: this.calculateEmailConfidence(email, visitor.email),
            matchedOn: ['email']
          });
        });
      }
    }

    // If we have a phone number, it's also a strong identifier
    if (phone) {
      const { data: phoneMatches } = await this.supabase
        .from('visitors')
        .select('*')
        .ilike('phone', this.formatPhoneForComparison(phone))
        .order('created_at', { ascending: false });

      if (phoneMatches && phoneMatches.length > 0) {
        phoneMatches.forEach(visitor => {
          // Check if this visitor is already in matches (from email)
          const existingMatch = matches.find(m => m.visitor.id === visitor.id);

          if (existingMatch) {
            // Update existing match with additional confidence
            existingMatch.confidence = Math.min(
              100,
              existingMatch.confidence + 20
            );
            existingMatch.matchedOn.push('phone');
          } else {
            // Add new match
            matches.push({
              visitor: visitor as Visitor,
              confidence: 85, // Phone matches are fairly confident
              matchedOn: ['phone']
            });
          }
        });
      }
    }

    // If we have name information, use it for matching
    if (full_name) {
      const { data: nameMatches } = await this.supabase
        .from('visitors')
        .select('*')
        .ilike('full_name', full_name)
        .order('created_at', { ascending: false });

      if (nameMatches && nameMatches.length > 0) {
        nameMatches.forEach(visitor => {
          // Check if this visitor is already in matches
          const existingMatch = matches.find(m => m.visitor.id === visitor.id);

          if (existingMatch) {
            // Update existing match with additional confidence
            existingMatch.confidence = Math.min(
              100,
              existingMatch.confidence + 15
            );
            existingMatch.matchedOn.push('name');
          } else {
            // Add new match
            matches.push({
              visitor: visitor as Visitor,
              confidence: 70, // Name matches are less confident
              matchedOn: ['name']
            });
          }
        });
      }
    }

    // Filter matches by threshold and sort by confidence
    return matches
      .filter(match => match.confidence >= threshold)
      .sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Calculate confidence score for email match
   */
  private calculateEmailConfidence(input: string, stored: string): number {
    if (!input || !stored) return 0;

    // Exact match
    if (input.toLowerCase() === stored.toLowerCase()) {
      return 95;
    }

    // Partial match (e.g. typo)
    const similarity = this.calculateStringSimilarity(
      input.toLowerCase(),
      stored.toLowerCase()
    );

    return Math.round(similarity * 90); // Max 90% for similar but not exact
  }

  /**
   * Format phone number for comparison by removing non-numeric characters
   */
  private formatPhoneForComparison(phone: string): string {
    return phone.replace(/\D/g, '');
  }

  /**
   * Calculate string similarity using Levenshtein distance
   * Returns a value between 0 (completely different) and 1 (identical)
   */
  private calculateStringSimilarity(a: string, b: string): number {
    if (a === b) return 1;
    if (a.length === 0 || b.length === 0) return 0;

    const matrix = Array(a.length + 1).fill(null).map(() =>
      Array(b.length + 1).fill(null)
    );

    for (let i = 0; i <= a.length; i++) {
      matrix[i][0] = i;
    }

    for (let j = 0; j <= b.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= a.length; i++) {
      for (let j = 1; j <= b.length; j++) {
        const cost = a[i - 1] === b[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost
        );
      }
    }

    const distance = matrix[a.length][b.length];
    const maxLength = Math.max(a.length, b.length);

    return 1 - distance / maxLength;
  }

  /**
   * Get visitor history with visit details
   */
  async getVisitorHistory(visitorId: string) {
    const { data, error } = await this.supabase
      .from('visits')
      .select(`
        *,
        visitor:visitors(*),
        host:users(*),
        location:locations(*)
      `)
      .eq('visitor_id', visitorId)
      .order('check_in_time', { ascending: false });

    if (error) {
      throw new Error(`Error fetching visitor history: ${error.message}`);
    }

    return data;
  }
}

// Factory function for server-side usage
export async function createVisitorRecognitionService() {
  const supabase = await createClient();
  return new VisitorRecognitionService(supabase);
}
