import { createClient } from '@/lib/supabase/client'
import { AdminService } from './index'
import {
  OrganizationSettings,
  UserRole,
  Location,
  LocationGroup,
  CustomField,
  Workflow,
  BrandingSettings
} from '@/types/admin'

export function createAdminService() {
  const supabase = createClient()
  return new AdminService(supabase)
}

export async function getOrganizationSettings(orgId: string) {
  const adminService = createAdminService()
  return adminService.getOrganizationSettings(orgId)
}

export async function updateOrganizationSettings(orgId: string, settings: Partial<OrganizationSettings>) {
  const adminService = createAdminService()
  return adminService.updateOrganizationSettings(orgId, settings)
}

export async function getUserRoles(orgId: string) {
  const adminService = createAdminService()
  return adminService.getUserRoles(orgId)
}

export async function createUserRole(role: Omit<UserRole, 'id' | 'created_at' | 'updated_at'>) {
  const adminService = createAdminService()
  return adminService.createUserRole(role)
}

export async function updateUserRole(roleId: string, role: Partial<UserRole>) {
  const adminService = createAdminService()
  return adminService.updateUserRole(roleId, role)
}

export async function deleteUserRole(roleId: string) {
  const adminService = createAdminService()
  return adminService.deleteUserRole(roleId)
}

export async function getUsers(orgId: string) {
  const adminService = createAdminService()
  return adminService.getUsers(orgId)
}

export async function updateUserRoleAssignment(userId: string, roleId: string) {
  const adminService = createAdminService()
  return adminService.updateUserRoleAssignment(userId, roleId)
}

export async function assignUserToLocation(userId: string, locationId: string, isPrimary = false) {
  const adminService = createAdminService()
  return adminService.assignUserToLocation(userId, locationId, isPrimary)
}

export async function removeUserFromLocation(userId: string, locationId: string) {
  const adminService = createAdminService()
  return adminService.removeUserFromLocation(userId, locationId)
}

export async function getLocations(orgId: string, includeInactive: boolean = false) {
  const adminService = createAdminService()
  return adminService.getLocations(orgId, includeInactive)
}

export async function getLocationHierarchy(orgId: string) {
  const adminService = createAdminService()
  return adminService.getLocationHierarchy(orgId)
}

export async function getLocation(locationId: string) {
  const adminService = createAdminService()
  return adminService.getLocation(locationId)
}

export async function getLocationChildren(locationId: string) {
  const adminService = createAdminService()
  return adminService.getLocationChildren(locationId)
}

export async function createLocation(location: Omit<Location, 'id' | 'created_at' | 'updated_at'>) {
  const adminService = createAdminService()
  return adminService.createLocation(location)
}

export async function updateLocation(locationId: string, location: Partial<Location>) {
  const adminService = createAdminService()
  return adminService.updateLocation(locationId, location)
}

export async function deleteLocation(locationId: string) {
  const adminService = createAdminService()
  return adminService.deleteLocation(locationId)
}

export async function deactivateLocation(locationId: string) {
  const adminService = createAdminService()
  return adminService.deactivateLocation(locationId)
}

export async function getLocationGroups(orgId: string) {
  const adminService = createAdminService()
  return adminService.getLocationGroups(orgId)
}

export async function getLocationGroup(groupId: string) {
  const adminService = createAdminService()
  return adminService.getLocationGroup(groupId)
}

export async function createLocationGroup(group: Omit<LocationGroup, 'id' | 'created_at' | 'updated_at'>) {
  const adminService = createAdminService()
  return adminService.createLocationGroup(group)
}

export async function updateLocationGroup(groupId: string, group: Partial<LocationGroup>) {
  const adminService = createAdminService()
  return adminService.updateLocationGroup(groupId, group)
}

export async function deleteLocationGroup(groupId: string) {
  const adminService = createAdminService()
  return adminService.deleteLocationGroup(groupId)
}

export async function getLocationPermissions(userId: string) {
  const adminService = createAdminService()
  return adminService.getLocationPermissions(userId)
}

export async function getUsersWithLocationPermission(locationId: string, permission?: string) {
  const adminService = createAdminService()
  return adminService.getUsersWithLocationPermission(locationId, permission)
}

export async function addLocationPermission(userId: string, locationId: string, permission: string) {
  const adminService = createAdminService()
  return adminService.addLocationPermission(userId, locationId, permission)
}

export async function removeLocationPermission(userId: string, locationId: string, permission: string) {
  const adminService = createAdminService()
  return adminService.removeLocationPermission(userId, locationId, permission)
}

export async function hasLocationPermission(userId: string, locationId: string, permission: string) {
  const adminService = createAdminService()
  return adminService.hasLocationPermission(userId, locationId, permission)
}

export async function getCustomFields(orgId: string, entityType?: string) {
  const adminService = createAdminService()
  return adminService.getCustomFields(orgId, entityType)
}

export async function createCustomField(field: Omit<CustomField, 'id' | 'created_at' | 'updated_at'>) {
  const adminService = createAdminService()
  return adminService.createCustomField(field)
}

export async function updateCustomField(fieldId: string, field: Partial<CustomField>) {
  const adminService = createAdminService()
  return adminService.updateCustomField(fieldId, field)
}

export async function deleteCustomField(fieldId: string) {
  const adminService = createAdminService()
  return adminService.deleteCustomField(fieldId)
}

export async function reorderCustomFields(orgId: string, entityType: string, fieldIds: string[]) {
  const adminService = createAdminService()
  return adminService.reorderCustomFields(orgId, entityType, fieldIds)
}

export async function getWorkflows(orgId: string) {
  const adminService = createAdminService()
  return adminService.getWorkflows(orgId)
}

export async function createWorkflow(workflow: Omit<Workflow, 'id' | 'created_at' | 'updated_at'>) {
  const adminService = createAdminService()
  return adminService.createWorkflow(workflow)
}

export async function updateWorkflow(workflowId: string, workflow: Partial<Workflow>) {
  const adminService = createAdminService()
  return adminService.updateWorkflow(workflowId, workflow)
}

export async function deleteWorkflow(workflowId: string) {
  const adminService = createAdminService()
  return adminService.deleteWorkflow(workflowId)
}

export async function toggleWorkflowStatus(workflowId: string, isActive: boolean) {
  const adminService = createAdminService()
  return adminService.toggleWorkflowStatus(workflowId, isActive)
}

export async function getBrandingSettings(orgId: string) {
  const adminService = createAdminService()
  return adminService.getBrandingSettings(orgId)
}

export async function updateBrandingSettings(orgId: string, settings: Partial<BrandingSettings>) {
  const adminService = createAdminService()
  return adminService.updateBrandingSettings(orgId, settings)
}
