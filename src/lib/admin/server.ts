import { createClient } from '@/lib/supabase/server'
import { AdminService } from './index'

export async function createAdminService() {
  const supabase = createClient()
  return new AdminService(supabase)
}

export async function getOrganizationSettings(orgId: string) {
  const adminService = await createAdminService()
  return adminService.getOrganizationSettings(orgId)
}

export async function getUserRoles(orgId: string) {
  const adminService = await createAdminService()
  return adminService.getUserRoles(orgId)
}

export async function getUsers(orgId: string) {
  const adminService = await createAdminService()
  return adminService.getUsers(orgId)
}

export async function getLocations(orgId: string) {
  const adminService = await createAdminService()
  return adminService.getLocations(orgId)
}

export async function getCustomFields(orgId: string, entityType?: string) {
  const adminService = await createAdminService()
  return adminService.getCustomFields(orgId, entityType)
}

export async function getWorkflows(orgId: string) {
  const adminService = await createAdminService()
  return adminService.getWorkflows(orgId)
}

export async function getBrandingSettings(orgId: string) {
  const adminService = await createAdminService()
  return adminService.getBrandingSettings(orgId)
}
