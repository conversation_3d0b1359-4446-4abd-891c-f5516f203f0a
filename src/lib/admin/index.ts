import { SupabaseClient } from '@supabase/supabase-js'
import {
  OrganizationSettings,
  UserRole,
  UserWithRole,
  LocationAssignment,
  Location,
  LocationHierarchy,
  LocationGroup,
  CustomField,
  Workflow,
  BrandingSettings
} from '@/types/admin'

export class AdminService {
  private supabase: SupabaseClient

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase
  }

  // Organization Settings Methods
  async getOrganizationSettings(orgId: string): Promise<OrganizationSettings | null> {
    const { data, error } = await this.supabase
      .from('organization_settings')
      .select('*')
      .eq('org_id', orgId)
      .single()

    if (error) {
      console.error('Error fetching organization settings:', error)
      return null
    }

    return data
  }

  async updateOrganizationSettings(
    orgId: string,
    settings: Partial<OrganizationSettings>
  ): Promise<OrganizationSettings | null> {
    const { data, error } = await this.supabase
      .from('organization_settings')
      .update({
        ...settings,
        updated_at: new Date().toISOString()
      })
      .eq('org_id', orgId)
      .select()
      .single()

    if (error) {
      console.error('Error updating organization settings:', error)
      return null
    }

    return data
  }

  async createOrganizationSettings(
    settings: Omit<OrganizationSettings, 'id' | 'created_at' | 'updated_at'>
  ): Promise<OrganizationSettings | null> {
    const { data, error } = await this.supabase
      .from('organization_settings')
      .insert({
        ...settings,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating organization settings:', error)
      return null
    }

    return data
  }

  // User Role Methods
  async getUserRoles(orgId: string): Promise<UserRole[]> {
    const { data, error } = await this.supabase
      .from('user_roles')
      .select('*')
      .eq('org_id', orgId)
      .order('name')

    if (error) {
      console.error('Error fetching user roles:', error)
      return []
    }

    return data
  }

  async getUserRole(roleId: string): Promise<UserRole | null> {
    const { data, error } = await this.supabase
      .from('user_roles')
      .select('*')
      .eq('id', roleId)
      .single()

    if (error) {
      console.error('Error fetching user role:', error)
      return null
    }

    return data
  }

  async createUserRole(role: Omit<UserRole, 'id' | 'created_at' | 'updated_at'>): Promise<UserRole | null> {
    const { data, error } = await this.supabase
      .from('user_roles')
      .insert({
        ...role,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating user role:', error)
      return null
    }

    return data
  }

  async updateUserRole(
    roleId: string,
    role: Partial<UserRole>
  ): Promise<UserRole | null> {
    const { data, error } = await this.supabase
      .from('user_roles')
      .update({
        ...role,
        updated_at: new Date().toISOString()
      })
      .eq('id', roleId)
      .select()
      .single()

    if (error) {
      console.error('Error updating user role:', error)
      return null
    }

    return data
  }

  async deleteUserRole(roleId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('user_roles')
      .delete()
      .eq('id', roleId)

    if (error) {
      console.error('Error deleting user role:', error)
      return false
    }

    return true
  }

  // User Management Methods
  async getUsers(orgId: string): Promise<UserWithRole[]> {
    const { data, error } = await this.supabase
      .from('users')
      .select(`
        *,
        user_roles!role_id (*)
      `)
      .eq('org_id', orgId)
      .order('full_name')

    if (error) {
      console.error('Error fetching users:', error)
      return []
    }

    // Get location assignments for each user
    const usersWithRoles: UserWithRole[] = []
    
    for (const user of data) {
      const { data: locationAssignments, error: locationError } = await this.supabase
        .from('user_location_assignments')
        .select(`
          id,
          location_id,
          locations!location_id (name),
          is_primary
        `)
        .eq('user_id', user.id)

      if (locationError) {
        console.error('Error fetching location assignments:', locationError)
      }

      // Handle the locations property correctly
      const formattedLocationAssignments: LocationAssignment[] = []
      
      if (locationAssignments) {
        for (const assignment of locationAssignments) {
          const locationName = assignment.locations 
            ? typeof assignment.locations === 'object' && 'name' in assignment.locations
              ? String(assignment.locations.name)
              : 'Unknown'
            : 'Unknown'
            
          formattedLocationAssignments.push({
            location_id: assignment.location_id,
            location_name: locationName,
            is_primary: assignment.is_primary
          })
        }
      }

      usersWithRoles.push({
        ...user,
        role: user.user_roles,
        locations: formattedLocationAssignments
      })
    }

    return usersWithRoles
  }

  async updateUserRoleAssignment(userId: string, roleId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('users')
      .update({ role_id: roleId })
      .eq('id', userId)

    if (error) {
      console.error('Error updating user role assignment:', error)
      return false
    }

    return true
  }

  async assignUserToLocation(
    userId: string,
    locationId: string,
    isPrimary: boolean = false
  ): Promise<boolean> {
    // If this is the primary location, update any existing primary locations
    if (isPrimary) {
      await this.supabase
        .from('user_location_assignments')
        .update({ is_primary: false })
        .eq('user_id', userId)
        .eq('is_primary', true)
    }

    // Check if assignment already exists
    const { data: existingAssignment } = await this.supabase
      .from('user_location_assignments')
      .select('id')
      .eq('user_id', userId)
      .eq('location_id', locationId)
      .single()

    if (existingAssignment) {
      // Update existing assignment
      const { error } = await this.supabase
        .from('user_location_assignments')
        .update({ is_primary: isPrimary })
        .eq('id', existingAssignment.id)

      if (error) {
        console.error('Error updating location assignment:', error)
        return false
      }
    } else {
      // Create new assignment
      const { error } = await this.supabase
        .from('user_location_assignments')
        .insert({
          user_id: userId,
          location_id: locationId,
          is_primary: isPrimary
        })

      if (error) {
        console.error('Error creating location assignment:', error)
        return false
      }
    }

    return true
  }

  async removeUserFromLocation(userId: string, locationId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('user_location_assignments')
      .delete()
      .eq('user_id', userId)
      .eq('location_id', locationId)

    if (error) {
      console.error('Error removing location assignment:', error)
      return false
    }

    return true
  }

  // Location Management Methods
  async getLocations(orgId: string, includeInactive: boolean = false): Promise<Location[]> {
    let query = this.supabase
      .from('locations')
      .select('*')
      .eq('org_id', orgId)
      .order('name')
    
    if (!includeInactive) {
      query = query.eq('is_active', true)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching locations:', error)
      return []
    }

    return data
  }
  
  async getLocationHierarchy(orgId: string): Promise<LocationHierarchy[]> {
    // First get all locations for the organization
    const { data: locations, error } = await this.supabase
      .from('locations')
      .select(`
        *,
        location_groups!group_id (*)
      `)
      .eq('org_id', orgId)
      .eq('is_active', true)
      .order('name')
    
    if (error) {
      console.error('Error fetching location hierarchy:', error)
      return []
    }
    
    // Get all locations with their parent and group information
    const locationsWithDetails = locations.map(location => {
      return {
        ...location,
        group: location.location_groups,
        children: []
      } as LocationHierarchy
    })
    
    // Create a map for quick lookup
    const locationMap = new Map<string, LocationHierarchy>()
    locationsWithDetails.forEach(location => {
      locationMap.set(location.id, location)
    })
    
    // Build the hierarchy
    const rootLocations: LocationHierarchy[] = []
    
    locationsWithDetails.forEach(location => {
      if (location.parent_id) {
        const parent = locationMap.get(location.parent_id)
        if (parent) {
          if (!parent.children) {
            parent.children = []
          }
          parent.children.push(location)
        } else {
          // If parent not found, treat as root
          rootLocations.push(location)
        }
      } else {
        // No parent, so it's a root location
        rootLocations.push(location)
      }
    })
    
    return rootLocations
  }

  async getLocation(locationId: string): Promise<Location | null> {
    const { data, error } = await this.supabase
      .from('locations')
      .select(`
        *,
        parent:parent_id (*),
        location_groups!group_id (*)
      `)
      .eq('id', locationId)
      .single()

    if (error) {
      console.error('Error fetching location:', error)
      return null
    }

    return data
  }
  
  async getLocationChildren(locationId: string): Promise<Location[]> {
    const { data, error } = await this.supabase
      .from('locations')
      .select('*')
      .eq('parent_id', locationId)
      .eq('is_active', true)
      .order('name')
    
    if (error) {
      console.error('Error fetching location children:', error)
      return []
    }
    
    return data
  }

  async createLocation(location: Omit<Location, 'id' | 'created_at' | 'updated_at'>): Promise<Location | null> {
    // Set default values for new fields if not provided
    const locationWithDefaults = {
      ...location,
      location_type: location.location_type || 'office',
      is_active: location.is_active !== undefined ? location.is_active : true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    
    const { data, error } = await this.supabase
      .from('locations')
      .insert(locationWithDefaults)
      .select()
      .single()

    if (error) {
      console.error('Error creating location:', error)
      return null
    }

    return data
  }

  async updateLocation(
    locationId: string,
    location: Partial<Location>
  ): Promise<Location | null> {
    const { data, error } = await this.supabase
      .from('locations')
      .update({
        ...location,
        updated_at: new Date().toISOString()
      })
      .eq('id', locationId)
      .select()
      .single()

    if (error) {
      console.error('Error updating location:', error)
      return null
    }

    return data
  }

  async deleteLocation(locationId: string): Promise<boolean> {
    // Check if location has children
    const { data: children } = await this.supabase
      .from('locations')
      .select('id')
      .eq('parent_id', locationId)
    
    if (children && children.length > 0) {
      console.error('Cannot delete location with children')
      return false
    }
    
    const { error } = await this.supabase
      .from('locations')
      .delete()
      .eq('id', locationId)

    if (error) {
      console.error('Error deleting location:', error)
      return false
    }

    return true
  }
  
  async deactivateLocation(locationId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('locations')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', locationId)
    
    if (error) {
      console.error('Error deactivating location:', error)
      return false
    }
    
    return true
  }

  // Location Group Methods
  async getLocationGroups(orgId: string): Promise<LocationGroup[]> {
    const { data, error } = await this.supabase
      .from('location_groups')
      .select('*')
      .eq('org_id', orgId)
      .order('name')
    
    if (error) {
      console.error('Error fetching location groups:', error)
      return []
    }
    
    return data
  }
  
  async getLocationGroup(groupId: string): Promise<LocationGroup | null> {
    const { data, error } = await this.supabase
      .from('location_groups')
      .select('*')
      .eq('id', groupId)
      .single()
    
    if (error) {
      console.error('Error fetching location group:', error)
      return null
    }
    
    return data
  }
  
  async createLocationGroup(group: Omit<LocationGroup, 'id' | 'created_at' | 'updated_at'>): Promise<LocationGroup | null> {
    const { data, error } = await this.supabase
      .from('location_groups')
      .insert({
        ...group,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.error('Error creating location group:', error)
      return null
    }
    
    return data
  }
  
  async updateLocationGroup(groupId: string, group: Partial<LocationGroup>): Promise<LocationGroup | null> {
    const { data, error } = await this.supabase
      .from('location_groups')
      .update({
        ...group,
        updated_at: new Date().toISOString()
      })
      .eq('id', groupId)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating location group:', error)
      return null
    }
    
    return data
  }
  
  async deleteLocationGroup(groupId: string): Promise<boolean> {
    // First update any locations in this group to have no group
    await this.supabase
      .from('locations')
      .update({ group_id: null })
      .eq('group_id', groupId)
    
    const { error } = await this.supabase
      .from('location_groups')
      .delete()
      .eq('id', groupId)
    
    if (error) {
      console.error('Error deleting location group:', error)
      return false
    }
    
    return true
  }
  
  // Location Permissions Methods
  async getLocationPermissions(userId: string): Promise<Record<string, string[]>> {
    const { data, error } = await this.supabase
      .from('location_permissions')
      .select('location_id, permission')
      .eq('user_id', userId)
    
    if (error) {
      console.error('Error fetching location permissions:', error)
      return {}
    }
    
    // Group permissions by location
    const permissionsByLocation: Record<string, string[]> = {}
    
    data.forEach(item => {
      if (!permissionsByLocation[item.location_id]) {
        permissionsByLocation[item.location_id] = []
      }
      permissionsByLocation[item.location_id].push(item.permission)
    })
    
    return permissionsByLocation
  }
  
  async getUsersWithLocationPermission(locationId: string, permission?: string): Promise<string[]> {
    let query = this.supabase
      .from('location_permissions')
      .select('user_id')
      .eq('location_id', locationId)
    
    if (permission) {
      query = query.eq('permission', permission)
    }
    
    const { data, error } = await query
    
    if (error) {
      console.error('Error fetching users with location permission:', error)
      return []
    }
    
    return data.map(item => item.user_id)
  }
  
  async addLocationPermission(userId: string, locationId: string, permission: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('location_permissions')
      .insert({
        user_id: userId,
        location_id: locationId,
        permission,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    
    if (error) {
      // If the error is a unique constraint violation, the permission already exists
      if (error.code === '23505') {
        return true
      }
      console.error('Error adding location permission:', error)
      return false
    }
    
    return true
  }
  
  async removeLocationPermission(userId: string, locationId: string, permission: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('location_permissions')
      .delete()
      .eq('user_id', userId)
      .eq('location_id', locationId)
      .eq('permission', permission)
    
    if (error) {
      console.error('Error removing location permission:', error)
      return false
    }
    
    return true
  }
  
  async hasLocationPermission(userId: string, locationId: string, permission: string): Promise<boolean> {
    const { data, error } = await this.supabase
      .from('location_permissions')
      .select('id')
      .eq('user_id', userId)
      .eq('location_id', locationId)
      .eq('permission', permission)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        // No permission found
        return false
      }
      console.error('Error checking location permission:', error)
      return false
    }
    
    return !!data
  }

  // Custom Fields Methods
  async getCustomFields(orgId: string, entityType?: string): Promise<CustomField[]> {
    let query = this.supabase
      .from('custom_fields')
      .select('*')
      .eq('org_id', orgId)
      .order('order')

    if (entityType) {
      query = query.eq('entity_type', entityType)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching custom fields:', error)
      return []
    }

    return data
  }

  async getCustomField(fieldId: string): Promise<CustomField | null> {
    const { data, error } = await this.supabase
      .from('custom_fields')
      .select('*')
      .eq('id', fieldId)
      .single()

    if (error) {
      console.error('Error fetching custom field:', error)
      return null
    }

    return data
  }

  async createCustomField(field: Omit<CustomField, 'id' | 'created_at' | 'updated_at'>): Promise<CustomField | null> {
    const { data, error } = await this.supabase
      .from('custom_fields')
      .insert({
        ...field,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating custom field:', error)
      return null
    }

    return data
  }

  async updateCustomField(
    fieldId: string,
    field: Partial<CustomField>
  ): Promise<CustomField | null> {
    const { data, error } = await this.supabase
      .from('custom_fields')
      .update({
        ...field,
        updated_at: new Date().toISOString()
      })
      .eq('id', fieldId)
      .select()
      .single()

    if (error) {
      console.error('Error updating custom field:', error)
      return null
    }

    return data
  }

  async deleteCustomField(fieldId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('custom_fields')
      .delete()
      .eq('id', fieldId)

    if (error) {
      console.error('Error deleting custom field:', error)
      return false
    }

    return true
  }

  async reorderCustomFields(
    orgId: string,
    entityType: string,
    fieldIds: string[]
  ): Promise<boolean> {
    // Start a transaction to update all field orders
    const updates = fieldIds.map((fieldId, index) => {
      return this.supabase
        .from('custom_fields')
        .update({ order: index })
        .eq('id', fieldId)
        .eq('org_id', orgId)
        .eq('entity_type', entityType)
    })

    try {
      await Promise.all(updates)
      return true
    } catch (error) {
      console.error('Error reordering custom fields:', error)
      return false
    }
  }

  // Workflow Methods
  async getWorkflows(orgId: string): Promise<Workflow[]> {
    const { data, error } = await this.supabase
      .from('workflows')
      .select('*')
      .eq('org_id', orgId)
      .order('name')

    if (error) {
      console.error('Error fetching workflows:', error)
      return []
    }

    return data
  }

  async getWorkflow(workflowId: string): Promise<Workflow | null> {
    const { data, error } = await this.supabase
      .from('workflows')
      .select('*')
      .eq('id', workflowId)
      .single()

    if (error) {
      console.error('Error fetching workflow:', error)
      return null
    }

    return data
  }

  async createWorkflow(workflow: Omit<Workflow, 'id' | 'created_at' | 'updated_at'>): Promise<Workflow | null> {
    const { data, error } = await this.supabase
      .from('workflows')
      .insert({
        ...workflow,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating workflow:', error)
      return null
    }

    return data
  }

  async updateWorkflow(
    workflowId: string,
    workflow: Partial<Workflow>
  ): Promise<Workflow | null> {
    const { data, error } = await this.supabase
      .from('workflows')
      .update({
        ...workflow,
        updated_at: new Date().toISOString()
      })
      .eq('id', workflowId)
      .select()
      .single()

    if (error) {
      console.error('Error updating workflow:', error)
      return null
    }

    return data
  }

  async deleteWorkflow(workflowId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('workflows')
      .delete()
      .eq('id', workflowId)

    if (error) {
      console.error('Error deleting workflow:', error)
      return false
    }

    return true
  }

  async toggleWorkflowStatus(workflowId: string, isActive: boolean): Promise<boolean> {
    const { error } = await this.supabase
      .from('workflows')
      .update({ is_active: isActive })
      .eq('id', workflowId)

    if (error) {
      console.error('Error toggling workflow status:', error)
      return false
    }

    return true
  }

  // Branding Settings Methods
  async getBrandingSettings(orgId: string): Promise<BrandingSettings | null> {
    const { data, error } = await this.supabase
      .from('branding_settings')
      .select('*')
      .eq('org_id', orgId)
      .single()

    if (error) {
      console.error('Error fetching branding settings:', error)
      return null
    }

    return data
  }

  async updateBrandingSettings(
    orgId: string,
    settings: Partial<BrandingSettings>
  ): Promise<BrandingSettings | null> {
    const { data, error } = await this.supabase
      .from('branding_settings')
      .update({
        ...settings,
        updated_at: new Date().toISOString()
      })
      .eq('org_id', orgId)
      .select()
      .single()

    if (error) {
      console.error('Error updating branding settings:', error)
      return null
    }

    return data
  }

  async createBrandingSettings(
    settings: Omit<BrandingSettings, 'id' | 'created_at' | 'updated_at'>
  ): Promise<BrandingSettings | null> {
    const { data, error } = await this.supabase
      .from('branding_settings')
      .insert({
        ...settings,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating branding settings:', error)
      return null
    }

    return data
  }
}
