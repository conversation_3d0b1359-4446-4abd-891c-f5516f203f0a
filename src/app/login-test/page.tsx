'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { ensureUserExistsClient } from '@/lib/auth/client-utils'

export default function LoginTestPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [logs, setLogs] = useState<string[]>([])
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    const logMessage = `${timestamp}: ${message}`
    setLogs(prev => [...prev, logMessage])
    console.log(logMessage)
  }

  const testLogin = async () => {
    if (!email || !password) {
      setError('Please enter email and password')
      return
    }

    setLoading(true)
    setError(null)
    setLogs([])

    try {
      addLog('Starting login test...')
      
      // Step 1: Sign in with email and password
      addLog('Calling signInWithPassword...')
      const { error: loginError, data } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (loginError) {
        addLog(`Login error: ${loginError.message}`)
        throw loginError
      }

      addLog(`Login successful! Session exists: ${!!data.session}`)
      
      if (data.session) {
        addLog(`User ID: ${data.session.user.id}`)
        addLog(`Email: ${data.session.user.email}`)
        
        // Step 2: Test session retrieval immediately after login
        addLog('Testing immediate session retrieval...')
        const { data: { session: immediateSession }, error: immediateError } = await supabase.auth.getSession()
        
        if (immediateError) {
          addLog(`Immediate session error: ${immediateError.message}`)
        } else if (immediateSession) {
          addLog('✅ Immediate session retrieval successful')
        } else {
          addLog('❌ Immediate session retrieval returned null')
        }
        
        // Step 3: Test ensureUserExistsClient with session passed directly
        addLog('Testing ensureUserExistsClient with session...')
        try {
          const userResult = await ensureUserExistsClient(undefined, data.session)
          addLog(`✅ ensureUserExistsClient successful: ${JSON.stringify(userResult)}`)
        } catch (userError) {
          addLog(`❌ ensureUserExistsClient error: ${userError}`)
        }
        
        // Step 4: Test ensureUserExistsClient without session (should fetch it)
        addLog('Testing ensureUserExistsClient without session...')
        try {
          const userResult2 = await ensureUserExistsClient()
          addLog(`✅ ensureUserExistsClient (no session param) successful: ${JSON.stringify(userResult2)}`)
        } catch (userError2) {
          addLog(`❌ ensureUserExistsClient (no session param) error: ${userError2}`)
        }
        
        // Step 5: Test session after delay
        addLog('Waiting 2 seconds then testing session retrieval...')
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        const { data: { session: delayedSession }, error: delayedError } = await supabase.auth.getSession()
        
        if (delayedError) {
          addLog(`Delayed session error: ${delayedError.message}`)
        } else if (delayedSession) {
          addLog('✅ Delayed session retrieval successful')
        } else {
          addLog('❌ Delayed session retrieval returned null')
        }
        
        addLog('Login test completed successfully!')
      } else {
        addLog('❌ No session returned from signInWithPassword')
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      addLog(`❌ Login test failed: ${errorMessage}`)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const clearLogs = () => {
    setLogs([])
    setError(null)
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Login Test Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Test Login</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your email"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your password"
              />
            </div>
            
            <button
              onClick={testLogin}
              disabled={loading}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Testing...' : 'Test Login'}
            </button>
            
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Test Results</h2>
            <button
              onClick={clearLogs}
              className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
            >
              Clear
            </button>
          </div>
          
          <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <p>No test results yet...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">{log}</div>
              ))
            )}
          </div>
        </div>
      </div>
      
      <div className="mt-6 text-center">
        <a
          href="/login"
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 inline-flex items-center"
        >
          Go to Regular Login
        </a>
      </div>
    </div>
  )
}
