import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import NotificationPreferences from '@/components/notification/NotificationPreferences'

export default async function NotificationSettingsPage() {
  const supabase = await createServerComponentClient()
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login')
  }
  
  // Get user data
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', session.user.id)
    .single()
  
  if (userError || !userData) {
    console.error('Error fetching user data:', userError)
    redirect('/dashboard')
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Notification Settings</h1>
        <p className="text-gray-600 mt-2">
          Manage how you receive notifications about visitor activities.
        </p>
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        <NotificationPreferences userId={userData.id} />
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">About Notifications</h2>
          <div className="space-y-4">
            <p className="text-gray-700">
              You can receive notifications through the following channels:
            </p>
            <ul className="list-disc pl-5 text-gray-700 space-y-2">
              <li>
                <strong>Email</strong> - Notifications will be sent to your registered email address.
              </li>
              <li>
                <strong>SMS</strong> - Text messages will be sent to your registered phone number.
              </li>
              <li>
                <strong>In-App</strong> - Notifications will appear in the notification center within the application.
              </li>
            </ul>
            <p className="text-gray-700">
              You can customize which types of notifications you receive through each channel.
              For example, you might want to receive visitor check-in notifications via email and SMS,
              but only receive visit reminders via in-app notifications.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
