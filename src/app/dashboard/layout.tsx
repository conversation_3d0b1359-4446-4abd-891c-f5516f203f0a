'use client'

import { useRouter } from 'next/navigation'
import { useSupabaseSession } from '@/hooks/useSupabaseSession'
import DashboardNav from '@/components/ui/DashboardNav'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()

  // Use our simplified session hook
  const { session, loading: isSessionLoading } = useSupabaseSession()

  // Redirect to login if no session (with delay to avoid race conditions)
  if (!isSessionLoading && !session) {
    // Add a small delay to avoid race conditions with middleware
    setTimeout(() => {
      router.push('/login')
    }, 100)
  }

  if (isSessionLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <DashboardNav />

      <div className="flex-1">
        {children}
      </div>
    </div>
  )
}