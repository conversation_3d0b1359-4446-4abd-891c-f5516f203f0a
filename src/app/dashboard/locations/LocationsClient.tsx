'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Location } from '@/types/admin'
import DeleteLocationConfirm from '@/components/admin/DeleteLocationConfirm'
import { useRouter } from 'next/navigation'

interface LocationsClientProps {
  locations: Location[]
}

export default function LocationsClient({ locations }: LocationsClientProps) {
  const router = useRouter()
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [locationToDelete, setLocationToDelete] = useState<Location | null>(null)
  
  const handleDeleteClick = (location: Location) => {
    setLocationToDelete(location)
    setShowDeleteModal(true)
  }
  
  const handleDeleteSuccess = () => {
    setShowDeleteModal(false)
    setLocationToDelete(null)
    router.refresh() // Refresh the page to show updated data
  }
  
  const handleDeleteCancel = () => {
    setShowDeleteModal(false)
    setLocationToDelete(null)
  }
  
  return (
    <>
      {locations && locations.length > 0 ? (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Address
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {locations.map((location) => (
                <tr key={location.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-900">{location.name}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-gray-900">{location.address}</div>
                    <div className="text-gray-500">
                      {location.city}, {location.state} {location.postal_code}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      location.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {location.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex justify-end space-x-4">
                      <Link 
                        href={`/dashboard/locations/${location.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDeleteClick(location)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p className="text-gray-500 mb-4">No locations found. Add your first location to get started.</p>
          <Link 
            href="/dashboard/locations/new" 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Add New Location
          </Link>
        </div>
      )}
      
      {/* Delete Confirmation Modal */}
      {showDeleteModal && locationToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <DeleteLocationConfirm
              locationId={locationToDelete.id}
              locationName={locationToDelete.name}
              onSuccess={handleDeleteSuccess}
              onCancel={handleDeleteCancel}
            />
          </div>
        </div>
      )}
    </>
  )
}
