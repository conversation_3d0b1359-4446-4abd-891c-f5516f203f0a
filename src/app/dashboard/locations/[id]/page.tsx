import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import EditLocationClient from './EditLocationClient'

interface EditLocationPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function EditLocationPage({ params }: EditLocationPageProps) {
  const { id } = await params
  const supabase = createServerComponentClient()
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login')
  }
  
  // Get user data
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', session.user.id)
    .single()
  
  if (userError || !userData) {
    console.error('Error fetching user data:', userError)
    redirect('/dashboard')
  }
  
  // Check if user has permission to edit locations
  if (!['admin', 'manager'].includes(userData.role)) {
    redirect('/dashboard')
  }
  
  // Get location data
  const { data: location, error: locationError } = await supabase
    .from('locations')
    .select('*')
    .eq('id', id)
    .eq('org_id', userData.org_id)
    .single()
  
  if (locationError || !location) {
    console.error('Error fetching location:', locationError)
    redirect('/dashboard/locations')
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Edit Location</h1>
        <p className="text-gray-600">Update location details for {location.name}</p>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <EditLocationClient
          orgId={userData.org_id}
          location={location}
        />
      </div>
    </div>
  )
}
