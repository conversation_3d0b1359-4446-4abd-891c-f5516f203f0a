'use client'

import { useRouter } from 'next/navigation'
import LocationForm from '@/components/admin/LocationForm'
import { Location } from '@/types/admin'

interface EditLocationClientProps {
  orgId: string
  location: Location
}

export default function EditLocationClient({ orgId, location }: EditLocationClientProps) {
  const router = useRouter()
  
  const handleSuccess = () => {
    router.push('/dashboard/locations')
  }
  
  const handleCancel = () => {
    router.push('/dashboard/locations')
  }
  
  return (
    <LocationForm 
      orgId={orgId}
      location={location}
      onSuccess={handleSuccess}
      onCancel={handleCancel}
    />
  )
}
