import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import NewLocationClient from './NewLocationClient'

export default async function NewLocationPage() {
  const supabase = await createServerComponentClient()

  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login')
  }

  // Get user data
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', session.user.id)
    .single()

  if (userError || !userData) {
    console.error('Error fetching user data:', userError)
    redirect('/dashboard')
  }

  // Check if user has permission to create locations
  if (!['admin', 'manager'].includes(userData.role)) {
    redirect('/dashboard')
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Add New Location</h1>
        <p className="text-gray-600">Create a new location for your organization</p>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <NewLocationClient orgId={userData.org_id} />
      </div>
    </div>
  )
}
