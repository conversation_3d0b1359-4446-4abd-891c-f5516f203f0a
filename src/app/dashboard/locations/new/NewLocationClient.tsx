'use client'

import { useRouter } from 'next/navigation'
import LocationForm from '@/components/admin/LocationForm'

interface NewLocationClientProps {
  orgId: string
}

export default function NewLocationClient({ orgId }: NewLocationClientProps) {
  const router = useRouter()
  
  const handleSuccess = () => {
    router.push('/dashboard/locations')
  }
  
  const handleCancel = () => {
    router.push('/dashboard/locations')
  }
  
  return (
    <LocationForm 
      orgId={orgId}
      onSuccess={handleSuccess}
      onCancel={handleCancel}
    />
  )
}
