import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import { cookies } from 'next/headers'
import Link from 'next/link'
import LocationsClient from './LocationsClient'

export default async function LocationsPage() {
  const cookieStore = cookies()
  const supabase = createServerComponentClient()

  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login')
  }

  // Get user data
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', session.user.id)
    .single()

  if (userError || !userData) {
    console.error('Error fetching user data:', userError)
    redirect('/dashboard')
  }

  // Get locations for this organization
  const { data: locations, error: locationsError } = await supabase
    .from('locations')
    .select('*')
    .eq('org_id', userData.org_id)
    .order('name')

  if (locationsError) {
    console.error('Error fetching locations:', locationsError)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Locations</h1>
        <div>
          <Link
            href="/dashboard/locations/new"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Add New Location
          </Link>
        </div>
      </div>

      <LocationsClient locations={locations || []} />
    </div>
  )
}
