import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import Link from 'next/link'

export default async function AppointmentsPage() {
  const supabase = await createServerComponentClient()

  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login')
  }

  // Get user data
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', session.user.id)
    .single()

  if (userError || !userData) {
    console.error('Error fetching user data:', userError)
    redirect('/dashboard')
  }

  // In a real app, we would fetch appointments from the database
  // For now, we'll just display a placeholder

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Appointments</h1>
        <div>
          <Link
            href="/dashboard/appointments/new"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Create New Appointment
          </Link>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Appointment Management</h3>
          <p className="text-gray-500 mb-6">
            This feature is coming soon. You&apos;ll be able to create and manage appointments for visitors.
          </p>
          <div className="flex justify-center space-x-4">
            <Link
              href="/dashboard/visitors/pre-register"
              className="px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Pre-register a Visitor
            </Link>
            <Link
              href="/dashboard/visitors"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Manage Visitors
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
