'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { getOrganizationSettings, updateOrganizationSettings } from '@/lib/admin/client'
import { OrganizationSettings } from '@/types/admin'

export default function OrganizationSettingsPage() {
  const [orgId, setOrgId] = useState<string | null>(null)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [orgSettings, setOrgSettings] = useState<OrganizationSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState<{
    name: string;
    data_retention_days: number;
    default_language: string;
    timezone: string;
    security_settings: {
      require_2fa: boolean;
      password_expiry_days: number;
      session_timeout_minutes: number;
      visitor_data_access_level: 'strict' | 'standard' | 'minimal';
    }
  }>({
    name: '',
    data_retention_days: 90,
    default_language: 'en',
    timezone: 'UTC',
    security_settings: {
      require_2fa: false,
      password_expiry_days: 90,
      session_timeout_minutes: 30,
      visitor_data_access_level: 'standard'
    }
  })
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        router.push('/login')
        return
      }
      
      // Get the organization ID from the user's metadata
      const orgId = user.user_metadata?.org_id
      setOrgId(orgId)
      
      if (orgId) {
        const settings = await getOrganizationSettings(orgId)
        setOrgSettings(settings)
        
        if (settings) {
          setFormData({
            name: settings.name || '',
            data_retention_days: settings.data_retention_days || 90,
            default_language: settings.default_language || 'en',
            timezone: settings.timezone || 'UTC',
            security_settings: {
              require_2fa: settings.security_settings?.require_2fa || false,
              password_expiry_days: settings.security_settings?.password_expiry_days || 90,
              session_timeout_minutes: settings.security_settings?.session_timeout_minutes || 30,
              visitor_data_access_level: settings.security_settings?.visitor_data_access_level || 'standard'
            }
          })
        }
      }
      
      setLoading(false)
    }
    
    getUser()
  }, [router, supabase])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement
    
    if (name.startsWith('security_settings.')) {
      const securityField = name.split('.')[1]
      setFormData({
        ...formData,
        security_settings: {
          ...formData.security_settings,
          [securityField]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
        }
      })
    } else {
      setFormData({
        ...formData,
        [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!orgId) return
    
    setSaving(true)
    
    try {
      const updatedSettings = await updateOrganizationSettings(orgId, formData)
      if (updatedSettings) {
        setOrgSettings(updatedSettings)
        alert('Organization settings saved successfully')
      }
    } catch (error) {
      console.error('Error saving organization settings:', error)
      alert('Error saving organization settings')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Organization Settings</h1>
        <p className="text-gray-600 mt-1">
          Manage your organization profile, security settings, and preferences
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold">Organization Profile</h2>
            
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Organization Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="default_language" className="block text-sm font-medium text-gray-700">
                  Default Language
                </label>
                <select
                  id="default_language"
                  name="default_language"
                  value={formData.default_language}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                >
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="ja">Japanese</option>
                  <option value="zh">Chinese</option>
                </select>
              </div>

              <div>
                <label htmlFor="timezone" className="block text-sm font-medium text-gray-700">
                  Timezone
                </label>
                <select
                  id="timezone"
                  name="timezone"
                  value={formData.timezone}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                >
                  <option value="UTC">UTC</option>
                  <option value="America/New_York">Eastern Time (ET)</option>
                  <option value="America/Chicago">Central Time (CT)</option>
                  <option value="America/Denver">Mountain Time (MT)</option>
                  <option value="America/Los_Angeles">Pacific Time (PT)</option>
                  <option value="Europe/London">London</option>
                  <option value="Europe/Paris">Paris</option>
                  <option value="Asia/Tokyo">Tokyo</option>
                  <option value="Asia/Shanghai">Shanghai</option>
                  <option value="Australia/Sydney">Sydney</option>
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="data_retention_days" className="block text-sm font-medium text-gray-700">
                Data Retention Period (days)
              </label>
              <input
                type="number"
                id="data_retention_days"
                name="data_retention_days"
                value={formData.data_retention_days}
                onChange={handleInputChange}
                min="1"
                max="3650"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
              <p className="mt-1 text-sm text-gray-500">
                Visitor data will be automatically deleted after this many days
              </p>
            </div>
          </div>

          <div className="space-y-4 pt-4 border-t">
            <h2 className="text-lg font-semibold">Security Settings</h2>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="security_settings.require_2fa"
                name="security_settings.require_2fa"
                checked={formData.security_settings.require_2fa}
                onChange={handleInputChange}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="security_settings.require_2fa" className="ml-2 block text-sm text-gray-700">
                Require two-factor authentication for all users
              </label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="security_settings.password_expiry_days" className="block text-sm font-medium text-gray-700">
                  Password Expiry (days)
                </label>
                <input
                  type="number"
                  id="security_settings.password_expiry_days"
                  name="security_settings.password_expiry_days"
                  value={formData.security_settings.password_expiry_days}
                  onChange={handleInputChange}
                  min="0"
                  max="365"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                />
                <p className="mt-1 text-sm text-gray-500">
                  0 = never expire
                </p>
              </div>

              <div>
                <label htmlFor="security_settings.session_timeout_minutes" className="block text-sm font-medium text-gray-700">
                  Session Timeout (minutes)
                </label>
                <input
                  type="number"
                  id="security_settings.session_timeout_minutes"
                  name="security_settings.session_timeout_minutes"
                  value={formData.security_settings.session_timeout_minutes}
                  onChange={handleInputChange}
                  min="5"
                  max="1440"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label htmlFor="security_settings.visitor_data_access_level" className="block text-sm font-medium text-gray-700">
                Visitor Data Access Level
              </label>
              <select
                id="security_settings.visitor_data_access_level"
                name="security_settings.visitor_data_access_level"
                value={formData.security_settings.visitor_data_access_level}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                <option value="strict">Strict - Only admins and hosts can view visitor data</option>
                <option value="standard">Standard - All users can view visitor data</option>
                <option value="minimal">Minimal - Only collect essential visitor information</option>
              </select>
            </div>
          </div>

          <div className="pt-4 flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
