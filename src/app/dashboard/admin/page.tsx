'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { 
  Building2, 
  Users, 
  Map, 
  Database, 
  Workflow, 
  Palette,
  ArrowRight
} from 'lucide-react'
import { getOrganizationSettings } from '@/lib/admin/client'
import { OrganizationSettings } from '@/types/admin'

export default function AdminDashboardPage() {
  const [orgId, setOrgId] = useState<string | null>(null)
  const [orgSettings, setOrgSettings] = useState<OrganizationSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        router.push('/login')
        return
      }
      
      // Get the organization ID from the user's metadata
      const orgId = user.user_metadata?.org_id
      setOrgId(orgId)
      
      if (orgId) {
        const settings = await getOrganizationSettings(orgId)
        setOrgSettings(settings)
      }
      
      setLoading(false)
    }
    
    getUser()
  }, [router, supabase])

  const adminModules = [
    {
      title: 'Organization Settings',
      description: 'Manage organization profile, security settings, and preferences',
      icon: Building2,
      href: '/dashboard/admin/organization',
      color: 'bg-blue-100 text-blue-600',
    },
    {
      title: 'User Management',
      description: 'Manage users, roles, and permissions',
      icon: Users,
      href: '/dashboard/admin/users',
      color: 'bg-green-100 text-green-600',
    },
    {
      title: 'Location Management',
      description: 'Manage multiple locations and location-specific settings',
      icon: Map,
      href: '/dashboard/admin/locations',
      color: 'bg-purple-100 text-purple-600',
    },
    {
      title: 'Custom Fields',
      description: 'Create and manage custom fields for visitors, visits, and hosts',
      icon: Database,
      href: '/dashboard/admin/fields',
      color: 'bg-yellow-100 text-yellow-600',
    },
    {
      title: 'Workflows',
      description: 'Create and manage automated workflows and triggers',
      icon: Workflow,
      href: '/dashboard/admin/workflows',
      color: 'bg-red-100 text-red-600',
    },
    {
      title: 'Branding',
      description: 'Customize the look and feel of your visitor management system',
      icon: Palette,
      href: '/dashboard/admin/branding',
      color: 'bg-indigo-100 text-indigo-600',
    },
  ]

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Admin Dashboard</h1>
        <p className="text-gray-600 mt-1">
          Manage your organization, users, locations, and more
        </p>
      </div>

      {orgSettings ? (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">{orgSettings.name || 'Your Organization'}</h2>
              <p className="text-gray-600 mt-1">
                Organization ID: {orgId}
              </p>
            </div>
            <Link
              href="/dashboard/admin/organization"
              className="text-blue-600 hover:text-blue-800 flex items-center"
            >
              Manage <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Organization Settings</h2>
              <p className="text-gray-600 mt-1">
                Set up your organization profile and preferences
              </p>
            </div>
            <Link
              href="/dashboard/admin/organization"
              className="text-blue-600 hover:text-blue-800 flex items-center"
            >
              Set Up <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {adminModules.map((module) => {
          const Icon = module.icon
          return (
            <Link
              key={module.href}
              href={module.href}
              className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start">
                <div className={`p-3 rounded-full ${module.color} mr-4`}>
                  <Icon className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="font-semibold">{module.title}</h3>
                  <p className="text-gray-600 text-sm mt-1">{module.description}</p>
                </div>
              </div>
            </Link>
          )
        })}
      </div>
    </div>
  )
}
