'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import LocationPermissionsForm from '@/components/admin/LocationPermissionsForm'
import { Shield, Users, MapPin } from 'lucide-react'

export default function PermissionsPage() {
  const [orgId, setOrgId] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()
  
  // Get organization ID from session
  useEffect(() => {
    const getOrgId = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user?.user_metadata?.org_id) {
        setOrgId(session.user.user_metadata.org_id)
        setLoading(false)
      } else {
        setLoading(false)
      }
    }
    
    getOrgId()
  }, [supabase])
  
  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse flex space-x-4">
          <div className="flex-1 space-y-4 py-1">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    )
  }
  
  if (!orgId) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
          Organization ID not found. Please ensure you are logged in with an organization account.
        </div>
      </div>
    )
  }
  
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Location Permissions</h1>
        <p className="text-gray-500">
          Manage user access to different locations within your organization
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center mb-4">
            <div className="bg-blue-100 p-3 rounded-full mr-4">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
            <h2 className="text-lg font-medium">Access Control</h2>
          </div>
          <p className="text-gray-500 text-sm">
            Control which users can access specific locations and what actions they can perform.
            Set permissions to view, edit, or manage location data.
          </p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center mb-4">
            <div className="bg-green-100 p-3 rounded-full mr-4">
              <Users className="h-6 w-6 text-green-600" />
            </div>
            <h2 className="text-lg font-medium">User Management</h2>
          </div>
          <p className="text-gray-500 text-sm">
            Assign users to specific locations and set their primary location.
            Users will default to their primary location when logging in.
          </p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center mb-4">
            <div className="bg-purple-100 p-3 rounded-full mr-4">
              <MapPin className="h-6 w-6 text-purple-600" />
            </div>
            <h2 className="text-lg font-medium">Multi-Location Support</h2>
          </div>
          <p className="text-gray-500 text-sm">
            Enable users to work across multiple locations with appropriate access levels.
            Perfect for managers who oversee multiple branches or facilities.
          </p>
        </div>
      </div>
      
      <LocationPermissionsForm 
        orgId={orgId} 
        onSuccess={() => {
          // You could add a success notification here
          console.log('Permissions updated successfully')
        }}
      />
    </div>
  )
}
