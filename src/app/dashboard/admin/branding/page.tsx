'use client'

import { useState, useEffect } from 'react'
import { getBrandingSettings } from '@/lib/admin/client'
import BrandingForm from '@/components/admin/BrandingForm'
import { BrandingSettings } from '@/types/admin'

export default function BrandingPage() {
  const [orgId, setOrgId] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [settings, setSettings] = useState<BrandingSettings | null>(null)
  
  useEffect(() => {
    const fetchOrgId = async () => {
      try {
        // In a real app, you would get the organization ID from the user's session
        // For now, we'll use a placeholder
        setOrgId('current-org-id')
        
        if (orgId) {
          setLoading(true)
          const brandingSettings = await getBrandingSettings(orgId)
          setSettings(brandingSettings)
          setLoading(false)
        }
      } catch (error) {
        console.error('Error fetching organization ID:', error)
        setLoading(false)
      }
    }
    
    fetchOrgId()
  }, [orgId])
  
  const handleSuccess = () => {
    // Refresh the settings
    if (orgId) {
      getBrandingSettings(orgId).then(setSettings)
    }
  }
  
  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Branding Customization</h1>
      </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="py-4">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <BrandingForm 
              orgId={orgId} 
              settings={settings} 
              onSuccess={handleSuccess} 
            />
          )}
        </div>
      </div>
    </div>
  )
}
