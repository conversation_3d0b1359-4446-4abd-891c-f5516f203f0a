/* eslint-disable jsx-quotes */
/* eslint-disable quotes */
'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { getCustomFields, deleteCustomField } from '@/lib/admin/client'
import { CustomField } from '@/types/admin'
import { PlusCircle, Pencil, Trash2, Search, X, Database, Eye, EyeOff, Check, X as XIcon } from 'lucide-react'
import CustomFieldForm from '@/components/admin/CustomFieldForm'

export default function CustomFieldsManagementPage() {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [orgId, setOrgId] = useState<string | null>(null)
  const [customFields, setCustomFields] = useState<CustomField[]>([])
  const [filteredFields, setFilteredFields] = useState<CustomField[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [entityTypeFilter, setEntityTypeFilter] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedField, setSelectedField] = useState<CustomField | null>(null)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        router.push('/login')
        return
      }
      
      // Get the organization ID from the user's metadata
      const orgId = user.user_metadata?.org_id
      setOrgId(orgId)
      
      if (orgId) {
        const fieldsData = await getCustomFields(orgId)
        setCustomFields(fieldsData)
        setFilteredFields(fieldsData)
      }
      
      setLoading(false)
    }
    
    getUser()
  }, [router, supabase])

  useEffect(() => {
    let filtered = customFields

    // Apply entity type filter
    if (entityTypeFilter) {
      filtered = filtered.filter(field => field.entity_type === entityTypeFilter)
    }

    // Apply search query
    if (searchQuery.trim() !== '') {
      filtered = filtered.filter(field => 
        field.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        field.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
        field.type.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    setFilteredFields(filtered)
  }, [searchQuery, entityTypeFilter, customFields])

  const handleCreateField = () => {
    setShowCreateModal(true)
  }

  const handleEditField = (field: CustomField) => {
    setSelectedField(field)
    setShowEditModal(true)
  }

  const handleDeleteField = (fieldId: string) => {
    if (confirm(`Are you sure you want to delete this custom field? This action cannot be undone.`)) {
      deleteCustomField(fieldId)
        .then((success) => {
          if (success) {
            // Refresh the fields list
            if (orgId) {
              getCustomFields(orgId).then(data => {
                setCustomFields(data)
                setFilteredFields(data)
              })
            }
          } else {
            alert(`Failed to delete custom field. Please try again.`)
          }
        })
        .catch(err => {
          console.error('Error deleting custom field:', err)
          alert(`An error occurred while deleting the custom field.`)
        })
    }
  }

  const handleFieldSaved = () => {
    setShowCreateModal(false)
    setShowEditModal(false)
    setSelectedField(null)
    
    // Refresh the fields list
    if (orgId) {
      getCustomFields(orgId).then(data => {
        setCustomFields(data)
        setFilteredFields(data)
      })
    }
  }

  const getEntityTypeLabel = (type: string) => {
    switch (type) {
      case 'visitor':
        return 'Visitor'
      case 'visit':
        return 'Visit'
      case 'host':
        return 'Host'
      case 'location':
        return 'Location'
      default:
        return type
    }
  }

  const getFieldTypeLabel = (type: string) => {
    switch (type) {
      case 'text':
        return 'Text'
      case 'number':
        return 'Number'
      case 'date':
        return 'Date'
      case 'time':
        return 'Time'
      case 'datetime':
        return 'Date & Time'
      case 'select':
        return 'Dropdown'
      case 'multiselect':
        return 'Multi-select'
      case 'checkbox':
        return 'Checkbox'
      case 'radio':
        return 'Radio Button'
      case 'textarea':
        return 'Text Area'
      default:
        return type
    }
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Custom Fields</h1>
          <p className="text-gray-600 mt-1">
            Manage custom fields for visitors, visits, hosts, and locations
          </p>
        </div>
        <div>
          <button
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onClick={handleCreateField}
          >
            <PlusCircle className="h-5 w-5 mr-2" />
            Add Custom Field
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="px-6 py-4 border-b">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-3 md:space-y-0">
            <div className="flex items-center space-x-2">
              <h2 className="font-semibold">Custom Fields</h2>
              <div className="flex space-x-1">
                <button
                  className={`px-2 py-1 text-xs rounded-md ${entityTypeFilter === null ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}
                  onClick={() => setEntityTypeFilter(null)}
                >
                  All
                </button>
                <button
                  className={`px-2 py-1 text-xs rounded-md ${entityTypeFilter === 'visitor' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}
                  onClick={() => setEntityTypeFilter('visitor')}
                >
                  Visitor
                </button>
                <button
                  className={`px-2 py-1 text-xs rounded-md ${entityTypeFilter === 'visit' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}
                  onClick={() => setEntityTypeFilter('visit')}
                >
                  Visit
                </button>
                <button
                  className={`px-2 py-1 text-xs rounded-md ${entityTypeFilter === 'host' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}
                  onClick={() => setEntityTypeFilter('host')}
                >
                  Host
                </button>
                <button
                  className={`px-2 py-1 text-xs rounded-md ${entityTypeFilter === 'location' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}
                  onClick={() => setEntityTypeFilter('location')}
                >
                  Location
                </button>
              </div>
            </div>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search fields..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              {searchQuery && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    onClick={() => setSearchQuery('')}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Entity
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Required
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Visible
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredFields.length > 0 ? (
                filteredFields.map((field) => (
                  <tr key={field.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600">
                            <Database className="h-5 w-5" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{field.label}</div>
                          <div className="text-xs text-gray-500">{field.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{getFieldTypeLabel(field.type)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                        {getEntityTypeLabel(field.entity_type)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {field.is_required ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <Check className="h-3 w-3 mr-1" />
                          Yes
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <XIcon className="h-3 w-3 mr-1" />
                          No
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {field.is_visible ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <Eye className="h-3 w-3 mr-1" />
                          Yes
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <EyeOff className="h-3 w-3 mr-1" />
                          No
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        className="text-blue-600 hover:text-blue-900 mr-3"
                        onClick={() => handleEditField(field)}
                      >
                        <Pencil className="h-4 w-4" />
                      </button>
                      <button
                        className="text-red-600 hover:text-red-900"
                        onClick={() => handleDeleteField(field.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                    {searchQuery || entityTypeFilter ? "No custom fields found matching your search." : "No custom fields found. Add your first custom field!"}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Custom Field Modal */}
      {showCreateModal && orgId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">Add New Custom Field</h2>
            <CustomFieldForm 
              orgId={orgId} 
              onSuccess={handleFieldSaved} 
              onCancel={() => setShowCreateModal(false)} 
            />
          </div>
        </div>
      )}

      {/* Edit Custom Field Modal */}
      {showEditModal && selectedField && orgId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">Edit Custom Field: {selectedField.label}</h2>
            <CustomFieldForm 
              orgId={orgId} 
              field={selectedField}
              onSuccess={handleFieldSaved} 
              onCancel={() => {
                setShowEditModal(false)
                setSelectedField(null)
              }} 
            />
          </div>
        </div>
      )}
    </div>
  )
}
