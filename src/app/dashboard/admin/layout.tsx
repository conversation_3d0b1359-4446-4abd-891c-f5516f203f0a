'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Building2, 
  Settings, 
  Users, 
  Workflow, 
  Palette, 
  Database, 
  Map,
  Shield
} from 'lucide-react'

export default function AdminDashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const [isSidebarOpen, setIsSidebarOpen] = useState(true)

  const navItems = [
    {
      name: 'Organization',
      href: '/dashboard/admin/organization',
      icon: Building2,
    },
    {
      name: 'Users',
      href: '/dashboard/admin/users',
      icon: Users,
    },
    {
      name: 'Locations',
      href: '/dashboard/admin/locations',
      icon: Map,
    },
    {
      name: 'Permissions',
      href: '/dashboard/admin/permissions',
      icon: Shield,
    },
    {
      name: 'Custom Fields',
      href: '/dashboard/admin/fields',
      icon: Database,
    },
    {
      name: 'Workflows',
      href: '/dashboard/admin/workflows',
      icon: Workflow,
    },
    {
      name: 'Branding',
      href: '/dashboard/admin/branding',
      icon: Palette,
    },
  ]

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Sidebar */}
      <aside
        className={`${
          isSidebarOpen ? 'w-64' : 'w-20'
        } bg-white border-r border-gray-200 transition-all duration-300 ease-in-out hidden md:block`}
      >
        <div className="p-4 border-b flex items-center justify-between">
          <h2 className={`font-bold ${isSidebarOpen ? 'block' : 'hidden'}`}>
            Admin Dashboard
          </h2>
          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="p-1 rounded-md hover:bg-gray-100"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              {isSidebarOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 5l7 7-7 7M5 5l7 7-7 7"
                />
              )}
            </svg>
          </button>
        </div>
        <nav className="p-4 space-y-2">
          <Link
            href="/dashboard"
            className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 ${
              pathname === '/dashboard' ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
            }`}
          >
            <Settings className="h-5 w-5" />
            {isSidebarOpen && <span className="ml-3">Dashboard</span>}
          </Link>

          {navItems.map((item) => {
            const Icon = item.icon
            const isActive = pathname === item.href
            return (
              <Link
                key={item.href}
                href={item.href}
                className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 ${
                  isActive ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                }`}
              >
                <Icon className="h-5 w-5" />
                {isSidebarOpen && <span className="ml-3">{item.name}</span>}
              </Link>
            )
          })}
        </nav>
      </aside>

      {/* Mobile sidebar */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 z-10 bg-white border-t border-gray-200">
        <div className="flex justify-around">
          <Link
            href="/dashboard"
            className={`flex flex-col items-center p-3 ${
              pathname === '/dashboard' ? 'text-blue-600' : 'text-gray-700'
            }`}
          >
            <Settings className="h-6 w-6" />
            <span className="text-xs mt-1">Dashboard</span>
          </Link>

          {navItems.map((item) => {
            const Icon = item.icon
            const isActive = pathname === item.href
            return (
              <Link
                key={item.href}
                href={item.href}
                className={`flex flex-col items-center p-3 ${
                  isActive ? 'text-blue-600' : 'text-gray-700'
                }`}
              >
                <Icon className="h-6 w-6" />
                <span className="text-xs mt-1">{item.name}</span>
              </Link>
            )
          })}
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col">
        <main className="flex-1 p-6 overflow-auto pb-20 md:pb-6">
          {children}
        </main>
      </div>
    </div>
  )
}
