'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import {
  getLocations,
  getLocationGroups,
  getLocationHierarchy,
  deleteLocation,
  deactivateLocation,
  deleteLocationGroup
} from '@/lib/admin/client'
import { Location, LocationGroup, LocationHierarchy } from '@/types/admin'
import LocationForm from '@/components/admin/LocationForm'
import LocationGroupForm from '@/components/admin/LocationGroupForm'
import {
  Building,
  MapPin,
  Plus,
  Edit,
  Trash2,
  ChevronDown,
  ChevronRight,
  EyeOff,
  FolderPlus
} from 'lucide-react'

export default function LocationsPage() {
  const supabase = createClient()
  const [orgId, setOrgId] = useState<string | null>(null)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [allLocations, setAllLocations] = useState<Location[]>([])
  const [locationHierarchy, setLocationHierarchy] = useState<LocationHierarchy[]>([])
  const [locationGroups, setLocationGroups] = useState<LocationGroup[]>([])
  const [loading, setLoading] = useState(true)
  const [showLocationForm, setShowLocationForm] = useState(false)
  const [showGroupForm, setShowGroupForm] = useState(false)
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  const [selectedGroup, setSelectedGroup] = useState<LocationGroup | null>(null)
  const [expandedLocations, setExpandedLocations] = useState<Record<string, boolean>>({})
  const [activeTab, setActiveTab] = useState<'locations' | 'groups'>('locations')

  // Get organization ID from session
  useEffect(() => {
    const getOrgId = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user?.user_metadata?.org_id) {
        setOrgId(session.user.user_metadata.org_id)
      }
    }

    getOrgId()
  }, [supabase])

  // Fetch locations and groups
  useEffect(() => {
    if (orgId) {
      const fetchData = async () => {
        try {
          const [locationsData, hierarchyData, groupsData] = await Promise.all([
            getLocations(orgId, true),
            getLocationHierarchy(orgId),
            getLocationGroups(orgId)
          ])

          setAllLocations(locationsData)
          setLocationHierarchy(hierarchyData)
          setLocationGroups(groupsData)
          setLoading(false)
        } catch (err) {
          console.error('Error fetching locations data:', err)
          setLoading(false)
        }
      }

      fetchData()
    }
  }, [orgId])

  const handleAddLocation = () => {
    setSelectedLocation(null)
    setShowLocationForm(true)
  }

  const handleEditLocation = (location: Location) => {
    setSelectedLocation(location)
    setShowLocationForm(true)
  }

  const handleDeleteLocation = async (locationId: string) => {
    if (confirm(`Are you sure you want to delete this location? This action cannot be undone.`)) {
      try {
        await deleteLocation(locationId)
        // Refresh locations
        if (orgId) {
          const [locationsData, hierarchyData] = await Promise.all([
            getLocations(orgId, true),
            getLocationHierarchy(orgId)
          ])
          setAllLocations(locationsData)
          setLocationHierarchy(hierarchyData)
        }
      } catch (err) {
        console.error('Error deleting location:', err)
        alert('Failed to delete location. It may have associated data or child locations.')
      }
    }
  }

  const handleDeactivateLocation = async (locationId: string) => {
    if (confirm(`Are you sure you want to deactivate this location? It will be hidden from most views but data will be preserved.`)) {
      try {
        await deactivateLocation(locationId)
        // Refresh locations
        if (orgId) {
          const [locationsData, hierarchyData] = await Promise.all([
            getLocations(orgId, true),
            getLocationHierarchy(orgId)
          ])
          setAllLocations(locationsData)
          setLocationHierarchy(hierarchyData)
        }
      } catch (err) {
        console.error('Error deactivating location:', err)
        alert('Failed to deactivate location.')
      }
    }
  }

  const handleAddGroup = () => {
    setSelectedGroup(null)
    setShowGroupForm(true)
  }

  const handleEditGroup = (group: LocationGroup) => {
    setSelectedGroup(group)
    setShowGroupForm(true)
  }

  const handleDeleteGroup = async (groupId: string) => {
    if (confirm(`Are you sure you want to delete this location group? Locations in this group will not be deleted.`)) {
      try {
        await deleteLocationGroup(groupId)
        // Refresh groups
        if (orgId) {
          const groupsData = await getLocationGroups(orgId)
          setLocationGroups(groupsData)
        }
      } catch (err) {
        console.error('Error deleting location group:', err)
        alert('Failed to delete location group.')
      }
    }
  }

  const handleLocationFormSuccess = async () => {
    setShowLocationForm(false)

    // Refresh locations
    if (orgId) {
      const [locationsData, hierarchyData] = await Promise.all([
        getLocations(orgId, true),
        getLocationHierarchy(orgId)
      ])
      setAllLocations(locationsData)
      setLocationHierarchy(hierarchyData)
    }
  }

  const handleGroupFormSuccess = async () => {
    setShowGroupForm(false)

    // Refresh groups
    if (orgId) {
      const groupsData = await getLocationGroups(orgId)
      setLocationGroups(groupsData)
    }
  }

  const toggleExpand = (locationId: string) => {
    setExpandedLocations(prev => ({
      ...prev,
      [locationId]: !prev[locationId]
    }))
  }

  const renderLocationTree = (locations: LocationHierarchy[], level = 0) => {
    return locations.map(location => {
      const hasChildren = location.children && location.children.length > 0
      const isExpanded = expandedLocations[location.id] || false
      const group = location.group

      return (
        <div key={location.id} className="location-tree-item">
          <div
            className={`
              flex items-center justify-between py-3 px-4 hover:bg-gray-50 border-b
              ${level > 0 ? 'pl-' + (level * 8 + 4) : ''}
              ${!location.is_active ? 'opacity-50' : ''}
            `}
          >
            <div className="flex items-center">
              {hasChildren ? (
                <button
                  className="mr-2 p-1 rounded-full hover:bg-gray-200 focus:outline-none"
                  onClick={() => toggleExpand(location.id)}
                >
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  )}
                </button>
              ) : (
                <span className="w-6"></span>
              )}

              {location.location_type === 'headquarters' ? (
                <Building className="h-5 w-5 mr-2 text-blue-600" />
              ) : (
                <MapPin className="h-5 w-5 mr-2 text-gray-600" />
              )}

              <div>
                <div className="font-medium">
                  {location.name}
                  {!location.is_active && (
                    <span className="ml-2 text-xs text-gray-500">(Inactive)</span>
                  )}
                </div>

                <div className="text-sm text-gray-500">
                  {location.location_type === 'headquarters' ? 'Headquarters' :
                   location.location_type === 'branch' ? 'Branch Office' :
                   location.location_type === 'facility' ? 'Facility' :
                   location.location_type === 'office' ? 'Office' : 'Location'}

                  {location.city && (
                    <span className="ml-2">
                      • {location.city}
                      {location.country && `, ${location.country}`}
                    </span>
                  )}
                </div>
              </div>

              {group && (
                <div
                  className="ml-3 px-2 py-1 text-xs rounded-full text-white"
                  style={{ backgroundColor: group.color || '#3B82F6' }}
                >
                  {group.name}
                </div>
              )}
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => handleEditLocation(location)}
                className="p-1 text-gray-500 hover:text-blue-600 focus:outline-none"
                title="Edit Location"
              >
                <Edit className="h-4 w-4" />
              </button>

              {location.is_active && (
                <button
                  onClick={() => handleDeactivateLocation(location.id)}
                  className="p-1 text-gray-500 hover:text-amber-600 focus:outline-none"
                  title="Deactivate Location"
                >
                  <EyeOff className="h-4 w-4" />
                </button>
              )}

              <button
                onClick={() => handleDeleteLocation(location.id)}
                className="p-1 text-gray-500 hover:text-red-600 focus:outline-none"
                title="Delete Location"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>

          {hasChildren && isExpanded && (
            <div className="location-children">
              {renderLocationTree(location.children || [], level + 1)}
            </div>
          )}
        </div>
      )
    })
  }

  if (!orgId) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
          Organization ID not found. Please ensure you are logged in with an organization account.
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Location Management</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleAddLocation}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Location
          </button>
          <button
            onClick={handleAddGroup}
            className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
          >
            <FolderPlus className="h-4 w-4 mr-2" />
            Add Group
          </button>
        </div>
      </div>

      <div className="bg-white rounded-md shadow overflow-hidden">
        <div className="border-b">
          <div className="flex">
            <button
              className={`px-4 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'locations'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('locations')}
            >
              Locations
            </button>
            <button
              className={`px-4 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'groups'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('groups')}
            >
              Location Groups
            </button>
          </div>
        </div>

        {loading ? (
          <div className="p-6 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            {activeTab === 'locations' && (
              <div>
                {locationHierarchy.length > 0 ? (
                  <div className="location-tree">
                    {renderLocationTree(locationHierarchy)}
                  </div>
                ) : (
                  <div className="p-6 text-center text-gray-500">
                    No locations found. Click &quot;Add Location&quot; to create your first location.
                  </div>
                )}
              </div>
            )}

            {activeTab === 'groups' && (
              <div>
                {locationGroups.length > 0 ? (
                  <div className="divide-y">
                    {locationGroups.map(group => (
                      <div
                        key={group.id}
                        className="flex items-center justify-between py-3 px-4 hover:bg-gray-50"
                      >
                        <div className="flex items-center">
                          <div
                            className="h-6 w-6 rounded-full mr-3"
                            style={{ backgroundColor: group.color || '#3B82F6' }}
                          />
                          <div>
                            <div className="font-medium">{group.name}</div>
                            {group.description && (
                              <div className="text-sm text-gray-500">{group.description}</div>
                            )}
                          </div>
                        </div>

                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEditGroup(group)}
                            className="p-1 text-gray-500 hover:text-blue-600 focus:outline-none"
                            title="Edit Group"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteGroup(group.id)}
                            className="p-1 text-gray-500 hover:text-red-600 focus:outline-none"
                            title="Delete Group"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-6 text-center text-gray-500">
                    No location groups found. Click &quot;Add Group&quot; to create your first group.
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>

      {showLocationForm && orgId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h2 className="text-xl font-bold mb-4">
                {selectedLocation ? 'Edit Location' : 'Add Location'}
              </h2>
              <LocationForm
                orgId={orgId}
                location={selectedLocation || undefined}
                onSuccess={handleLocationFormSuccess}
                onCancel={() => setShowLocationForm(false)}
              />
            </div>
          </div>
        </div>
      )}

      {showGroupForm && orgId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="p-6">
              <h2 className="text-xl font-bold mb-4">
                {selectedGroup ? 'Edit Location Group' : 'Add Location Group'}
              </h2>
              <LocationGroupForm
                orgId={orgId}
                group={selectedGroup || undefined}
                onSuccess={handleGroupFormSuccess}
                onCancel={() => setShowGroupForm(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
