/* eslint-disable jsx-quotes */
/* eslint-disable quotes */
'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { getWorkflows, toggleWorkflowStatus, deleteWorkflow } from '@/lib/admin/client'
import { Workflow, WorkflowTrigger, WorkflowAction } from '@/types/admin'
import { PlusCircle, Pencil, Trash2, Search, X, Workflow as WorkflowIcon, Play, Pause } from 'lucide-react'
import WorkflowForm from '@/components/admin/WorkflowForm'

export default function WorkflowsManagementPage() {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [orgId, setOrgId] = useState<string | null>(null)
  const [workflows, setWorkflows] = useState<Workflow[]>([])
  const [filteredWorkflows, setFilteredWorkflows] = useState<Workflow[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        router.push('/login')
        return
      }
      
      // Get the organization ID from the user's metadata
      const orgId = user.user_metadata?.org_id
      setOrgId(orgId)
      
      if (orgId) {
        const workflowsData = await getWorkflows(orgId)
        setWorkflows(workflowsData)
        setFilteredWorkflows(workflowsData)
      }
      
      setLoading(false)
    }
    
    getUser()
  }, [router, supabase])

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredWorkflows(workflows)
    } else {
      const filtered = workflows.filter(workflow => 
        workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (workflow.description && workflow.description.toLowerCase().includes(searchQuery.toLowerCase()))
      )
      setFilteredWorkflows(filtered)
    }
  }, [searchQuery, workflows])

  const handleCreateWorkflow = () => {
    setShowCreateModal(true)
  }

  const handleEditWorkflow = (workflow: Workflow) => {
    setSelectedWorkflow(workflow)
    setShowEditModal(true)
  }

  const handleDeleteWorkflow = (workflowId: string) => {
    if (confirm(`Are you sure you want to delete this workflow? This action cannot be undone.`)) {
      deleteWorkflow(workflowId)
        .then((success) => {
          if (success) {
            // Refresh the workflows list
            if (orgId) {
              getWorkflows(orgId).then(data => {
                setWorkflows(data)
                setFilteredWorkflows(data)
              })
            }
          } else {
            alert(`Failed to delete workflow. Please try again.`)
          }
        })
        .catch(err => {
          console.error('Error deleting workflow:', err)
          alert(`An error occurred while deleting the workflow.`)
        })
    }
  }

  const handleToggleWorkflowStatus = (workflowId: string, currentStatus: boolean) => {
    toggleWorkflowStatus(workflowId, !currentStatus)
      .then((success) => {
        if (success) {
          // Refresh the workflows list
          if (orgId) {
            getWorkflows(orgId).then(data => {
              setWorkflows(data)
              setFilteredWorkflows(data)
            })
          }
        } else {
          alert(`Failed to update workflow status. Please try again.`)
        }
      })
      .catch(err => {
        console.error('Error updating workflow status:', err)
        alert(`An error occurred while updating the workflow status.`)
      })
  }

  const handleWorkflowSaved = () => {
    setShowCreateModal(false)
    setShowEditModal(false)
    setSelectedWorkflow(null)
    
    // Refresh the workflows list
    if (orgId) {
      getWorkflows(orgId).then(data => {
        setWorkflows(data)
        setFilteredWorkflows(data)
      })
    }
  }

  const getTriggerLabel = (trigger: WorkflowTrigger | undefined) => {
    if (!trigger) return 'Unknown'
    
    switch (trigger.event) {
      case 'pre_registration':
        return 'Pre-registration'
      case 'check_in':
        return 'Check-in'
      case 'check_out':
        return 'Check-out'
      case 'visit_approved':
        return 'Visit Approved'
      case 'visit_rejected':
        return 'Visit Rejected'
      case 'visit_cancelled':
        return 'Visit Cancelled'
      case 'scheduled':
        return 'Scheduled'
      default:
        return trigger.event
    }
  }

  const getActionCount = (actions: WorkflowAction[] | undefined) => {
    if (!actions || !Array.isArray(actions)) return 0
    return actions.length
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Workflows</h1>
          <p className="text-gray-600 mt-1">
            Manage automated workflows for your organization
          </p>
        </div>
        <div>
          <button
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onClick={handleCreateWorkflow}
          >
            <PlusCircle className="h-5 w-5 mr-2" />
            Add Workflow
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <h2 className="font-semibold">Workflows</h2>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search workflows..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              {searchQuery && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    onClick={() => setSearchQuery('')}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trigger
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredWorkflows.length > 0 ? (
                filteredWorkflows.map((workflow) => (
                  <tr key={workflow.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                            <WorkflowIcon className="h-5 w-5" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{workflow.name}</div>
                          <div className="text-xs text-gray-500">{workflow.description || "No description"}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                        {getTriggerLabel(workflow.trigger)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{getActionCount(workflow.actions)} action(s)</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleToggleWorkflowStatus(workflow.id, workflow.is_active)}
                        className={`inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded ${
                          workflow.is_active
                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                            : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                        }`}
                      >
                        {workflow.is_active ? (
                          <>
                            <Play className="h-3 w-3 mr-1" />
                            Active
                          </>
                        ) : (
                          <>
                            <Pause className="h-3 w-3 mr-1" />
                            Inactive
                          </>
                        )}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        className="text-blue-600 hover:text-blue-900 mr-3"
                        onClick={() => handleEditWorkflow(workflow)}
                      >
                        <Pencil className="h-4 w-4" />
                      </button>
                      <button
                        className="text-red-600 hover:text-red-900"
                        onClick={() => handleDeleteWorkflow(workflow.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                    {searchQuery ? "No workflows found matching your search." : "No workflows found. Add your first workflow!"}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Workflow Modal */}
      {showCreateModal && orgId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">Add New Workflow</h2>
            <WorkflowForm 
              orgId={orgId} 
              onSuccess={handleWorkflowSaved} 
              onCancel={() => setShowCreateModal(false)} 
            />
          </div>
        </div>
      )}

      {/* Edit Workflow Modal */}
      {showEditModal && selectedWorkflow && orgId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">Edit Workflow: {selectedWorkflow.name}</h2>
            <WorkflowForm 
              orgId={orgId} 
              workflow={selectedWorkflow}
              onSuccess={handleWorkflowSaved} 
              onCancel={() => {
                setShowEditModal(false)
                setSelectedWorkflow(null)
              }} 
            />
          </div>
        </div>
      )}
    </div>
  )
}
