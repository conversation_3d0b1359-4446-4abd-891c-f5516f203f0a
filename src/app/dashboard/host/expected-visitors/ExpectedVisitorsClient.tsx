'use client';

import { useState, useEffect } from 'react';
import { createClientVisitService } from '@/lib/visit';
import { VisitRequest, VisitStatus } from '@/types/visit';
import Link from 'next/link';
import { format } from 'date-fns';

interface ExpectedVisitorsClientProps {
  userId: string;
}

export default function ExpectedVisitorsClient({ userId }: ExpectedVisitorsClientProps) {
  const [visits, setVisits] = useState<VisitRequest[]>([]);
  const [pendingApprovals, setPendingApprovals] = useState<VisitRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'upcoming' | 'pending'>('upcoming');
  
  const visitService = createClientVisitService();
  
  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        
        // Fetch upcoming visits where the user is the host
        const upcomingVisits = await visitService.getVisitRequests({
          host_id: userId,
          status: ['approved', 'pending'],
        });
        
        // Fetch pending approval requests for the user
        const pendingRequests = await visitService.getPendingApprovalRequests(userId);
        
        setVisits(upcomingVisits);
        setPendingApprovals(pendingRequests);
        setError(null);
      } catch (err) {
        console.error('Error fetching visit data:', err);
        setError('Failed to load visit data. Please try again later.');
      } finally {
        setLoading(false);
      }
    }
    
    fetchData();
  }, [userId]);
  
  const handleApprove = async (visitId: string) => {
    try {
      const success = await visitService.respondToVisitApprovalRequest({
        visit_id: visitId,
        approver_id: userId,
        status: 'approved',
      });
      
      if (success) {
        // Update the pending approvals list
        setPendingApprovals(prev => prev.filter(visit => visit.id !== visitId));
        
        // Refresh the visits list
        const upcomingVisits = await visitService.getVisitRequests({
          host_id: userId,
          status: ['approved', 'pending'],
        });
        setVisits(upcomingVisits);
      }
    } catch (err) {
      console.error('Error approving visit:', err);
      setError('Failed to approve visit. Please try again later.');
    }
  };
  
  const handleReject = async (visitId: string, notes?: string) => {
    try {
      const success = await visitService.respondToVisitApprovalRequest({
        visit_id: visitId,
        approver_id: userId,
        status: 'rejected',
        notes,
      });
      
      if (success) {
        // Update the pending approvals list
        setPendingApprovals(prev => prev.filter(visit => visit.id !== visitId));
      }
    } catch (err) {
      console.error('Error rejecting visit:', err);
      setError('Failed to reject visit. Please try again later.');
    }
  };
  
  const getStatusBadge = (status: VisitStatus) => {
    switch (status) {
      case 'pending':
        return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>;
      case 'approved':
        return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Approved</span>;
      case 'rejected':
        return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>;
      case 'checked_in':
        return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Checked In</span>;
      case 'checked_out':
        return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Checked Out</span>;
      case 'cancelled':
        return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Cancelled</span>;
      case 'no_show':
        return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">No Show</span>;
      default:
        return null;
    }
  };
  
  const formatDateTime = (dateTimeStr: string) => {
    try {
      return format(new Date(dateTimeStr), 'MMM d, yyyy h:mm a');
    } catch {
      return dateTimeStr;
    }
  };
  
  if (loading) {
    return <div className="flex justify-center items-center h-64">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>;
  }
  
  if (error) {
    return <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
      <strong className="font-bold">Error!</strong>
      <span className="block sm:inline"> {error}</span>
    </div>;
  }
  
  return (
    <div>
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('upcoming')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'upcoming'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Upcoming Visits
              {visits.length > 0 && (
                <span className="ml-2 py-0.5 px-2 text-xs rounded-full bg-blue-100 text-blue-800">
                  {visits.length}
                </span>
              )}
            </button>
            
            <button
              onClick={() => setActiveTab('pending')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'pending'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Pending Approvals
              {pendingApprovals.length > 0 && (
                <span className="ml-2 py-0.5 px-2 text-xs rounded-full bg-yellow-100 text-yellow-800">
                  {pendingApprovals.length}
                </span>
              )}
            </button>
          </nav>
        </div>
      </div>
      
      {activeTab === 'upcoming' && (
        <div>
          {visits.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">You have no upcoming visits.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Visitor
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date & Time
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Purpose
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {visits.map((visit) => (
                    <tr key={visit.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {visit.visitor_name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {visit.visitor_email}
                            </div>
                            {visit.visitor_company && (
                              <div className="text-sm text-gray-500">
                                {visit.visitor_company}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatDateTime(visit.start_time)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {visit.purpose}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {visit.location_name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(visit.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Link href={`/dashboard/visitors/visit/${visit.id}`} className="text-blue-600 hover:text-blue-900 mr-4">
                          View Details
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
      
      {activeTab === 'pending' && (
        <div>
          {pendingApprovals.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">You have no pending approval requests.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {pendingApprovals.map((visit) => (
                <div key={visit.id} className="bg-white shadow overflow-hidden sm:rounded-lg">
                  <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                    <div>
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Visit Request from {visit.visitor_name}
                      </h3>
                      <p className="mt-1 max-w-2xl text-sm text-gray-500">
                        {formatDateTime(visit.start_time)}
                      </p>
                    </div>
                    <div className="flex space-x-3">
                      <button
                        onClick={() => handleReject(visit.id)}
                        className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Reject
                      </button>
                      <button
                        onClick={() => handleApprove(visit.id)}
                        className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Approve
                      </button>
                    </div>
                  </div>
                  <div className="border-t border-gray-200">
                    <dl>
                      <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">
                          Visitor
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {visit.visitor_name} ({visit.visitor_email})
                          {visit.visitor_company && ` - ${visit.visitor_company}`}
                        </dd>
                      </div>
                      <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">
                          Purpose
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {visit.purpose}
                        </dd>
                      </div>
                      <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">
                          Location
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {visit.location_name}
                        </dd>
                      </div>
                      <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">
                          Date & Time
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {formatDateTime(visit.start_time)}
                          {visit.end_time && ` to ${formatDateTime(visit.end_time)}`}
                        </dd>
                      </div>
                      {visit.notes && (
                        <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                          <dt className="text-sm font-medium text-gray-500">
                            Notes
                          </dt>
                          <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {visit.notes}
                          </dd>
                        </div>
                      )}
                    </dl>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
