import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { createServerComponentClient } from '@/lib/supabase/server-component-client';
import ExpectedVisitorsClient from './ExpectedVisitorsClient';

export const metadata: Metadata = {
  title: 'Expected Visitors | Host Dashboard',
  description: 'View and manage your expected visitors',
};

export default async function ExpectedVisitorsPage() {
  const supabase = await createServerComponentClient();

  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    redirect('/login');
  }

  // Get user ID
  const userId = session.user.id;

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Expected Visitors</h1>
      <ExpectedVisitorsClient userId={userId} />
    </div>
  );
}
