'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { HostStatus, HostStatusData } from '@/types/host';
import { UserRole } from '@/types';

interface HostStatusPageClientProps {
  userId: string;
  orgId: string;
  userRole: UserRole;
}

export default function HostStatusPageClient({ userId, orgId }: HostStatusPageClientProps) {
  const supabase = React.useMemo(() => createClient(), []);
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hostStatus, setHostStatus] = useState<HostStatusData | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Get the current status
  useEffect(() => {
    const getStatus = async () => {
      setLoading(true);
      setError(null);

      try {
        const { data, error } = await supabase
          .from('host_status')
          .select('*')
          .eq('user_id', userId)
          .single();

        if (error) {
          throw new Error(error.message);
        }

        setHostStatus(data);
      } catch (err) {
        console.error('Error getting host status:', err);
        setError('Failed to load status. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    getStatus();
  }, [userId, supabase]);

  // Handle status change
  const handleStatusChange = async (newStatus: HostStatus) => {
    setIsUpdating(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('host_status')
        .upsert({ user_id: userId, status: newStatus, org_id: orgId })
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      setHostStatus(data);
    } catch (err) {
      console.error('Error updating host status:', err);
      setError('Failed to update status. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  if (!userId) {
    return (
      <div className="bg-white rounded-lg shadow p-4">
        <p>Please log in to manage your host status.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <div className="bg-white rounded-lg shadow p-4 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current Status</h2>

          {loading ? (
            <div className="flex items-center">
              <div className="h-4 w-4 mr-2 rounded-full animate-pulse bg-gray-300"></div>
              <span>Loading status...</span>
            </div>
          ) : error ? (
            <div className="text-red-600">{error}</div>
          ) : (
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className={`h-3 w-3 rounded-full mr-2 ${
                  hostStatus?.status === 'available' ? 'bg-green-500' :
                  hostStatus?.status === 'busy' ? 'bg-red-500' :
                  hostStatus?.status === 'out_of_office' ? 'bg-yellow-500' :
                  hostStatus?.status === 'in_meeting' ? 'bg-orange-500' :
                  hostStatus?.status === 'do_not_disturb' ? 'bg-purple-500' : 'bg-gray-500'
                }`}></div>
                <span className="capitalize">{hostStatus?.status?.replace('_', ' ') || 'Unknown'}</span>
              </div>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-xl font-semibold mb-4">Update Status</h2>
          <div className="space-y-2">
            {(['available', 'busy', 'out_of_office', 'in_meeting', 'do_not_disturb'] as HostStatus[]).map((status) => (
              <button
                key={status}
                onClick={() => handleStatusChange(status)}
                disabled={isUpdating}
                className={`w-full p-2 text-left rounded-lg border ${
                  hostStatus?.status === status
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-300 hover:bg-gray-50'
                } disabled:opacity-50`}
              >
                <div className="flex items-center">
                  <div className={`h-3 w-3 rounded-full mr-2 ${
                    status === 'available' ? 'bg-green-500' :
                    status === 'busy' ? 'bg-red-500' :
                    status === 'out_of_office' ? 'bg-yellow-500' :
                    status === 'in_meeting' ? 'bg-orange-500' :
                    status === 'do_not_disturb' ? 'bg-purple-500' : 'bg-gray-500'
                  }`}></div>
                  <span className="capitalize">{status.replace(/_/g, ' ')}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      <div>
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-xl font-semibold mb-4">Status History</h2>
          <p className="text-gray-500">Status history will be displayed here.</p>
        </div>
      </div>
    </div>
  );
}
