'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { HostStatus, HostStatusData, HostStatusDuration } from '@/types/host';
import { UserRole } from '@/types';

interface HostStatusPageClientProps {
  userId: string;
  orgId: string;
  userRole: UserRole;
}

export default function HostStatusPageClient({ userId, orgId, userRole }: HostStatusPageClientProps) {
  const supabase = React.useMemo(() => createClient(), []);
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hostStatus, setHostStatus] = useState<HostStatusData | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Get the current status
  useEffect(() => {
    const getStatus = async () => {
      setLoading(true);
      setError(null);

      try {
        const { data, error } = await supabase
          .from('host_status')
          .select('*')
          .eq('user_id', userId)
          .single();

        if (error) {
          throw new Error(error.message);
        }

        setHostStatus(data);
      } catch (err) {
        console.error('Error getting host status:', err);
        setError('Failed to load status. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    getStatus();
  }, [userId, supabase]);

  // Handle status change
  const handleStatusChange = async (newStatus: HostStatus) => {
    setIsUpdating(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('host_status')
        .upsert({ user_id: userId, status: newStatus, org_id: orgId })
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      setHostStatus(data);
    } catch (err) {
      console.error('Error updating host status:', err);
      setError('Failed to update status. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  if (!userId) {
    return (
      <div className="bg-white rounded-lg shadow p-4">
        <p>Please log in to manage your host status.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <div className="bg-white rounded-lg shadow p-4 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current Status</h2>

          {loading ? (
            <div className="flex items-center">
              <div className="h-4 w-4 mr-2 rounded-full animate-pulse bg-gray-300"></div>
              <span>Loading status...</span>
            </div>
          ) : error ? (
            <div className="text-red-600">{error}</div>
          ) : (
            <div className="p-4 bg-gray-50 rounded-lg">
              <HostStatusIndicator status={hostStatus} size="lg" />
            </div>
          )}
        </div>

        <HostStatusSelector
          userId={userId}
          onStatusChange={handleStatusChange}
        />
      </div>

      <div>
        <HostStatusHistoryList userId={userId} />
      </div>
    </div>
  );
}
