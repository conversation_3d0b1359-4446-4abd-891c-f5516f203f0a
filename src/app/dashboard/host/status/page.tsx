import { Suspense } from 'react';
import { HostStatusPageClient } from './HostStatusPageClient';

export const metadata = {
  title: 'Host Status | Visitor Management',
  description: 'Manage your host status and availability',
};

/**
 * Host Status Page
 * Allows hosts to manage their status and view their status history
 */
export default function HostStatusPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Host Status Management</h1>
      
      <Suspense fallback={<div>Loading...</div>}>
        <HostStatusPageClient />
      </Suspense>
    </div>
  );
}
