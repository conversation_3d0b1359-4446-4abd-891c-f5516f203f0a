import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { createServerComponentClient } from '@/lib/supabase/server-component-client';
import HostStatusPageClient from './HostStatusPageClient';

export const metadata = {
  title: 'Host Status | Visitor Management',
  description: 'Manage your host status and availability',
};

/**
 * Host Status Page
 * Allows hosts to manage their status and view their status history
 */
export default async function HostStatusPage() {
  const supabase = await createServerComponentClient();

  // Get the current user
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    redirect('/login');
  }

  // Get user data from the database
  const { data: userData } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', user.id)
    .single();

  if (!userData) {
    redirect('/login');
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Host Status Management</h1>

      <Suspense fallback={<div>Loading...</div>}>
        <HostStatusPageClient
          userId={userData.id}
          orgId={userData.org_id}
          userRole={userData.role}
        />
      </Suspense>
    </div>
  );
}
