import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import ReportingDashboardClient from './ReportingDashboardClient'

export default async function ReportingDashboardPage() {
  const supabase = await createServerComponentClient()
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login')
  }
  
  // Get user data
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', session.user.id)
    .single()
  
  if (userError || !userData) {
    console.error('Error fetching user data:', userError)
    redirect('/dashboard')
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Reporting Dashboard</h1>
        <p className="text-gray-600 mt-2">
          View visitor traffic, peak times, and visit duration metrics.
        </p>
      </div>
      
      <ReportingDashboardClient orgId={userData.org_id} />
    </div>
  )
}
