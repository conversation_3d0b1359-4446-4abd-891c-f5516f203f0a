'use client'

import { useState } from 'react'
import Visitor<PERSON>raffic<PERSON>hart from '@/components/reporting/VisitorTrafficChart'
import PeakTimeHeatmap from '@/components/reporting/PeakTimeHeatmap'
import VisitDurationChart from '@/components/reporting/VisitDurationChart'
import DataExporter from '@/components/reporting/DataExporter'
import CrossLocationComparison from '@/components/reporting/CrossLocationComparison'
import LocationVisitDurationComparison from '@/components/reporting/LocationVisitDurationComparison'

interface ReportingDashboardClientProps {
  orgId: string
}

export default function ReportingDashboardClient({ orgId }: ReportingDashboardClientProps) {
  const [activeTab, setActiveTab] = useState<'traffic' | 'peak-times' | 'duration' | 'cross-location' | 'location-duration' | 'export'>('traffic')
  
  return (
    <div>
      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8 overflow-x-auto pb-1">
          <button
            onClick={() => setActiveTab('traffic')}
            className={`
              py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
              ${
                activeTab === 'traffic'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            Visitor Traffic
          </button>
          <button
            onClick={() => setActiveTab('peak-times')}
            className={`
              py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
              ${
                activeTab === 'peak-times'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            Peak Times
          </button>
          <button
            onClick={() => setActiveTab('duration')}
            className={`
              py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
              ${
                activeTab === 'duration'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            Visit Duration
          </button>
          <button
            onClick={() => setActiveTab('cross-location')}
            className={`
              py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
              ${
                activeTab === 'cross-location'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            Location Comparison
          </button>
          <button
            onClick={() => setActiveTab('location-duration')}
            className={`
              py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
              ${
                activeTab === 'location-duration'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            Location Duration
          </button>
          <button
            onClick={() => setActiveTab('export')}
            className={`
              py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
              ${
                activeTab === 'export'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            Export Data
          </button>
        </nav>
      </div>
      
      {/* Tab content */}
      <div className="space-y-6">
        {activeTab === 'traffic' && (
          <div>
            <VisitorTrafficChart orgId={orgId} />
            
            <div className="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4">About Visitor Traffic</h2>
              <p className="text-gray-700 mb-4">
                The visitor traffic chart shows the number of visitors over time. You can view traffic data for different time periods (day, week, month, year) by clicking the buttons above the chart.
              </p>
              <p className="text-gray-700">
                Use this data to identify trends in visitor traffic and plan staffing and resources accordingly. For example, if you notice that Mondays are consistently busy, you might want to ensure you have adequate staff available on those days.
              </p>
            </div>
          </div>
        )}
        
        {activeTab === 'peak-times' && (
          <div>
            <PeakTimeHeatmap orgId={orgId} />
            
            <div className="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4">About Peak Times</h2>
              <p className="text-gray-700 mb-4">
                The peak times heatmap shows visitor traffic by day of the week and hour of the day. Darker colors indicate higher visitor traffic.
              </p>
              <p className="text-gray-700">
                Use this data to identify the busiest times of the week and adjust your staffing and resources accordingly. For example, if you notice that Tuesday afternoons are consistently busy, you might want to ensure you have adequate staff available during those times.
              </p>
            </div>
          </div>
        )}
        
        {activeTab === 'duration' && (
          <div>
            <VisitDurationChart orgId={orgId} />
            
            <div className="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4">About Visit Duration</h2>
              <p className="text-gray-700 mb-4">
                The visit duration chart shows the distribution of visit durations. The pie chart shows the percentage of visits that fall into each duration range, while the bar chart shows the actual count of visits in each range.
              </p>
              <p className="text-gray-700">
                Use this data to understand how long visitors typically stay and plan your resources accordingly. For example, if most visits are less than 30 minutes, you might want to optimize your check-in and check-out processes for quick visits.
              </p>
            </div>
          </div>
        )}
        
        {activeTab === 'cross-location' && (
          <div>
            <CrossLocationComparison orgId={orgId} />
            
            <div className="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4">About Location Comparison</h2>
              <p className="text-gray-700 mb-4">
                The location comparison chart shows visitor traffic across different locations. You can view the data as a bar chart or pie chart to understand the distribution of visitors across your locations.
              </p>
              <p className="text-gray-700">
                Use this data to identify which locations have the highest and lowest visitor traffic. This can help you allocate resources more effectively and identify locations that may need additional support or marketing efforts.
              </p>
            </div>
          </div>
        )}
        
        {activeTab === 'location-duration' && (
          <div>
            <LocationVisitDurationComparison orgId={orgId} />
            
            <div className="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4">About Location Visit Duration</h2>
              <p className="text-gray-700 mb-4">
                The location visit duration chart shows the average duration of visits at each location. This can help you understand how long visitors typically stay at different locations.
              </p>
              <p className="text-gray-700">
                Use this data to identify locations where visitors spend more or less time. Locations with shorter visit durations might need to be evaluated for potential improvements in visitor experience or services offered.
              </p>
            </div>
          </div>
        )}
        
        {activeTab === 'export' && (
          <div>
            <DataExporter orgId={orgId} />
            
            <div className="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4">About Data Export</h2>
              <p className="text-gray-700 mb-4">
                The data export tool allows you to export visitor traffic and visit duration data in CSV format. You can select the time range for the data you want to export.
              </p>
              <p className="text-gray-700">
                Use this feature to perform more detailed analysis in external tools like Microsoft Excel or Google Sheets. You can create custom charts, perform statistical analysis, or combine this data with other datasets.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
