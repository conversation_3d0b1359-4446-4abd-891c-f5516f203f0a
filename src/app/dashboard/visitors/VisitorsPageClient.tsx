'use client'

import { useState } from 'react'
import VisitorFilter, { FilterState } from '@/components/visitor/VisitorFilter'
import VisitorSearch, { SearchResult } from '@/components/visitor/VisitorSearch'
import VisitorList from '@/components/visitor/VisitorList'

interface VisitorsPageClientProps {
  orgId: string
}

export default function VisitorsPageClient({ orgId }: VisitorsPageClientProps) {
  const [filters, setFilters] = useState<FilterState>({
    status: null,
    dateRange: {
      from: null,
      to: null
    },
    hostId: null,
    locationId: null
  })
  
  const [searchResults, setSearchResults] = useState<SearchResult[] | null>(null)
  
  const handleFilterChange = (newFilters: FilterState) => {
    setFilters(newFilters)
    // Clear search results when filters change
    setSearchResults(null)
  }
  
  const handleSearch = (query: string, results: SearchResult[]) => {
    setSearchResults(results.length > 0 ? results : null)
  }
  
  return (
    <div>
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="w-full md:w-2/3">
          <VisitorSearch orgId={orgId} onSearch={handleSearch} />
        </div>
      </div>
      
      <VisitorFilter orgId={orgId} onFilterChange={handleFilterChange} />
      
      <VisitorList 
        orgId={orgId} 
        filters={filters} 
        searchResults={searchResults} 
      />
    </div>
  )
}
