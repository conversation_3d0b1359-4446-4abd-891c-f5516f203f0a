import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import VisitorCheckOutForm from '@/components/visitor/VisitorCheckOutForm'
import { VisitStatus } from '@/types'

interface VisitorCheckOutPageProps {
  params: {
    visitId: string
  }
}

export default async function VisitorCheckOutPage({ params }: VisitorCheckOutPageProps) {
  const { visitId } = params
  const supabase = createServerComponentClient()
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login')
  }
  
  // Get user data
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', session.user.id)
    .single()
  
  if (userError || !userData) {
    console.error('Error fetching user data:', userError)
    redirect('/dashboard')
  }
  
  // Verify the visit exists and belongs to the user's organization
  const { data: visitData, error: visitError } = await supabase
    .from('visits')
    .select('id, status')
    .eq('id', visitId)
    .eq('org_id', userData.org_id)
    .single()
  
  if (visitError || !visitData) {
    console.error('Error fetching visit data:', visitError)
    redirect('/dashboard/visitors')
  }
  
  // Check if the visit is in a valid state for check-out
  if (visitData.status !== VisitStatus.CHECKED_IN) {
    redirect('/dashboard/visitors')
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Check Out Visitor</h1>
      
      <VisitorCheckOutForm visitId={visitId} />
    </div>
  )
}
