import { redirect } from 'next/navigation'
import VisitorPreRegistrationForm from '@/components/visitor/VisitorPreRegistrationForm'
import { UserRole } from '@/types'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'

export default async function PreRegisterVisitorPage() {
  const supabase = createServerComponentClient()

  try {
    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      redirect('/login?redirect=/dashboard/visitors/pre-register')
    }

    // Get user data
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, org_id, role')
      .eq('id', session.user.id)
      .single()

    if (userError) {
      console.error('Error fetching user data:', userError)
      return (
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold mb-6">Pre-register a Visitor</h1>
          <div className="bg-red-50 p-4 rounded-md border border-red-200">
            <p className="text-red-700">Unable to load user data. Please try refreshing the page.</p>
            <p className="text-sm mt-2">Error: {userError.message}</p>
          </div>
        </div>
      )
    }

    // Get organization's locations
    const { data: locations, error: locationsError } = await supabase
      .from('locations')
      .select('id, name')
      .eq('org_id', userData.org_id)
      .order('name')

    if (locationsError) {
      console.error('Error fetching locations:', locationsError)
      return (
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold mb-6">Pre-register a Visitor</h1>
          <div className="bg-red-50 p-4 rounded-md border border-red-200">
            <p className="text-red-700">Unable to load locations. Please try refreshing the page.</p>
            <p className="text-sm mt-2">Error: {locationsError.message}</p>
          </div>
        </div>
      )
    }

    // If no locations exist, redirect to create location page (which we'll create later)
    if (!locations || locations.length === 0) {
      // For now, we'll just show a message
      return (
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold mb-6">Pre-register a Visitor</h1>
          <div className="bg-yellow-100 p-4 rounded-md text-yellow-700">
            <p>You need to create at least one location before you can pre-register visitors.</p>
          </div>
        </div>
      )
    }

    // Use the first location by default
    const defaultLocationId = locations[0].id

    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Pre-register a Visitor</h1>

        <VisitorPreRegistrationForm
          orgId={userData.org_id}
          locationId={defaultLocationId}
          // If user is a host, pre-select them as the host
          hostId={userData.role === UserRole.HOST ? userData.id : undefined}
        />
      </div>
    )
  } catch (error) {
    console.error('Unexpected error in PreRegisterVisitorPage:', error)
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Pre-register a Visitor</h1>
        <div className="bg-red-50 p-4 rounded-md border border-red-200">
          <p className="text-red-700">An unexpected error occurred. Please try refreshing the page.</p>
        </div>
      </div>
    )
  }
}
