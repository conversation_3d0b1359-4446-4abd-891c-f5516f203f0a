'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import BadgeTemplateEditor from '@/components/visitor/badge/BadgeTemplateEditor'

export default function NewBadgeTemplatePage() {
  const router = useRouter()
  const supabase = createClient()
  const [orgId, setOrgId] = useState<string | null>(null)
  
  // Get organization ID when component mounts
  useState(() => {
    const getOrgId = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        router.push('/login')
        return
      }
      
      const { data, error } = await supabase
        .from('users')
        .select('org_id')
        .eq('id', session.user.id)
        .single()
      
      if (error || !data) {
        console.error('Error fetching user data:', error)
        router.push('/dashboard')
        return
      }
      
      setOrgId(data.org_id)
    }
    
    getOrgId()
  })
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleSave = (templateId: string) => {
    router.push('/dashboard/visitors/badges')
  }
  
  const handleCancel = () => {
    router.push('/dashboard/visitors/badges')
  }
  
  if (!orgId) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-500">Loading...</div>
        </div>
      </div>
    )
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Create New Badge Template</h1>
      
      <BadgeTemplateEditor
        orgId={orgId}
        onSave={handleSave}
        onCancel={handleCancel}
      />
    </div>
  )
}
