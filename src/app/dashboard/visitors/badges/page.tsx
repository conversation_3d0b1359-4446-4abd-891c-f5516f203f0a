import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import { cookies } from 'next/headers'
import BadgeTemplateList from '@/components/visitor/badge/BadgeTemplateList'

export default async function BadgeTemplatesPage() {
  const supabase = createServerComponentClient()
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login')
  }
  
  // Get user data
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', session.user.id)
    .single()
  
  if (userError || !userData) {
    console.error('Error fetching user data:', userError)
    redirect('/dashboard')
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Badge Templates</h1>
      
      <BadgeTemplateList 
        orgId={userData.org_id} 
        onEdit={(templateId) => redirect(`/dashboard/visitors/badges/${templateId}/edit`)}
      />
    </div>
  )
}
