'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import BadgeTemplateEditor from '@/components/visitor/badge/BadgeTemplateEditor'

interface EditBadgeTemplatePageProps {
  params: {
    id: string
  }
}

export default function EditBadgeTemplatePage({ params }: EditBadgeTemplatePageProps) {
  const { id: templateId } = params
  const router = useRouter()
  const supabase = createClient()
  const [orgId, setOrgId] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Get organization ID when component mounts
  useEffect(() => {
    const getOrgId = async () => {
      setLoading(true)
      
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (!session) {
          router.push('/login')
          return
        }
        
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('org_id')
          .eq('id', session.user.id)
          .single()
        
        if (userError || !userData) {
          console.error('Error fetching user data:', userError)
          router.push('/dashboard')
          return
        }
        
        // Verify template belongs to user's organization
        const { data: templateData, error: templateError } = await supabase
          .from('badges')
          .select('id')
          .eq('id', templateId)
          .eq('org_id', userData.org_id)
          .single()
        
        if (templateError || !templateData) {
          console.error('Error fetching template data:', templateError)
          router.push('/dashboard/visitors/badges')
          return
        }
        
        setOrgId(userData.org_id)
      } catch (error) {
        console.error('Error:', error)
        setError('An error occurred. Please try again.')
      } finally {
        setLoading(false)
      }
    }
    
    getOrgId()
  }, [templateId, router, supabase])
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleSave = (savedTemplateId: string) => {
    router.push('/dashboard/visitors/badges')
  }
  
  const handleCancel = () => {
    router.push('/dashboard/visitors/badges')
  }
  
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-500">Loading...</div>
        </div>
      </div>
    )
  }
  
  if (error || !orgId) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-40">
          <div className="text-red-500">{error || 'An error occurred. Please try again.'}</div>
        </div>
      </div>
    )
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Edit Badge Template</h1>
      
      <BadgeTemplateEditor
        orgId={orgId}
        templateId={templateId}
        onSave={handleSave}
        onCancel={handleCancel}
      />
    </div>
  )
}
