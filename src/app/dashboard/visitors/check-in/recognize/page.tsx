'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { Visitor, Visit, VisitStatus, UserRole } from '@/types';
import { VisitorMatch } from '@/lib/visitor/recognition';
import ReturningVisitorForm from '@/components/visitor/ReturningVisitorForm';

export default function VisitorRecognitionPage() {
  const router = useRouter();
  const supabase = createClient();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [potentialMatches, setPotentialMatches] = useState<VisitorMatch[]>([]);
  const [hosts, setHosts] = useState<{ id: string; full_name: string }[]>([]);
  const [locations, setLocations] = useState<{ id: string; name: string }[]>([]);
  const [searchInfo, setSearchInfo] = useState({
    fullName: '',
    email: '',
    phone: '',
  });

  // Fetch hosts and locations when component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get current user's organization ID
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error('Not authenticated');

        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('org_id')
          .eq('id', user.id)
          .single();

        if (userError) throw userError;
        const orgId = userData.org_id;

        // Fetch hosts
        const { data: hostsData, error: hostsError } = await supabase
          .from('users')
          .select('id, full_name')
          .eq('org_id', orgId)
          .in('role', [UserRole.HOST, UserRole.ADMIN, UserRole.MANAGER])
          .order('full_name');

        if (hostsError) throw hostsError;
        setHosts(hostsData || []);

        // Fetch locations
        const { data: locationsData, error: locationsError } = await supabase
          .from('locations')
          .select('id, name')
          .eq('org_id', orgId)
          .order('name');

        if (locationsError) throw locationsError;
        setLocations(locationsData || []);

        // Get search parameters from URL
        const params = new URLSearchParams(window.location.search);
        const fullName = params.get('name') || '';
        const email = params.get('email') || '';
        const phone = params.get('phone') || '';

        setSearchInfo({
          fullName,
          email,
          phone,
        });

        // If we have search parameters, search for potential matches
        if (fullName || email || phone) {
          await searchVisitors(orgId, fullName, email, phone);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [supabase]);

  const searchVisitors = async (orgId: string, fullName: string, email: string, phone: string) => {
    try {
      // Search for visitors with similar information
      const { data: visitors, error: visitorsError } = await supabase
        .from('visitors')
        .select('*')
        .eq('org_id', orgId)
        .or(`email.ilike.%${email}%,phone.ilike.%${phone}%,full_name.ilike.%${fullName}%`)
        .order('created_at', { ascending: false });

      if (visitorsError) throw visitorsError;

      // Calculate match confidence for each visitor
      const matches: VisitorMatch[] = [];
      
      visitors?.forEach(visitor => {
        let confidence = 0;
        const matchedOn: string[] = [];
        
        // Email match (strongest)
        if (email && visitor.email.toLowerCase().includes(email.toLowerCase())) {
          confidence += 60;
          matchedOn.push('email');
        }
        
        // Phone match (strong)
        if (phone && visitor.phone.replace(/\D/g, '').includes(phone.replace(/\D/g, ''))) {
          confidence += 50;
          matchedOn.push('phone');
        }
        
        // Name match (weaker)
        if (fullName && visitor.full_name.toLowerCase().includes(fullName.toLowerCase())) {
          confidence += 40;
          matchedOn.push('name');
        }
        
        // Only include if there's some match
        if (confidence > 0) {
          matches.push({
            visitor: visitor as Visitor,
            confidence: Math.min(confidence, 100),
            matchedOn,
          });
        }
      });
      
      // Sort by confidence
      matches.sort((a, b) => b.confidence - a.confidence);
      
      setPotentialMatches(matches);
    } catch (error) {
      console.error('Error searching visitors:', error);
      setError('Failed to search for visitors. Please try again.');
    }
  };

  const handleSubmit = async (visitorId: string, visitData: Partial<Visit>) => {
    try {
      // Get current user's organization ID
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('org_id')
        .eq('id', user.id)
        .single();

      if (userError) throw userError;
      const orgId = userData.org_id;

      // Create visit
      const { error: visitError } = await supabase
        .from('visits')
        .insert({
          org_id: orgId,
          visitor_id: visitorId,
          location_id: visitData.location_id,
          host_id: visitData.host_id,
          purpose: visitData.purpose,
          status: VisitStatus.CHECKED_IN,
          check_in_time: new Date().toISOString(),
        });

      if (visitError) throw visitError;
    } catch (error) {
      console.error('Error creating visit:', error);
      throw new Error('Failed to check in visitor. Please try again.');
    }
  };

  const handleNewVisitor = () => {
    // Redirect to regular check-in page with pre-filled information
    const params = new URLSearchParams();
    if (searchInfo.fullName) params.append('name', searchInfo.fullName);
    if (searchInfo.email) params.append('email', searchInfo.email);
    if (searchInfo.phone) params.append('phone', searchInfo.phone);
    
    router.push(`/dashboard/visitors/check-in?${params.toString()}`);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="p-4 bg-red-100 text-red-700 rounded-md mb-4">
          {error}
        </div>
        <button
          onClick={() => router.push('/dashboard/visitors')}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Return to Visitors Dashboard
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">Visitor Recognition</h1>
      
      {potentialMatches.length === 0 ? (
        <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">No Matching Visitors Found</h2>
          <p className="mb-6 text-gray-600">
            We couldn&apos;t find any visitors matching your search criteria. Would you like to register as a new visitor?
          </p>
          <div className="flex gap-4">
            <button
              onClick={handleNewVisitor}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Register as New Visitor
            </button>
            <button
              onClick={() => router.push('/dashboard/visitors')}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Return to Dashboard
            </button>
          </div>
        </div>
      ) : (
        <ReturningVisitorForm
          potentialMatches={potentialMatches}
          hosts={hosts}
          locations={locations}
          onSubmit={handleSubmit}
          onNewVisitor={handleNewVisitor}
        />
      )}
    </div>
  );
}
