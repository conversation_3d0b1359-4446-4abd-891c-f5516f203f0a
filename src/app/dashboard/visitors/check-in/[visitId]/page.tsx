import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import VisitorCheckInForm from '@/components/visitor/VisitorCheckInForm'
import { VisitStatus } from '@/types'

interface VisitorCheckInPageProps {
  params: Promise<{
    visitId: string
  }>
}

export default async function VisitorCheckInByIdPage({ params }: VisitorCheckInPageProps) {
  const { visitId } = await params
  const supabase = await createServerComponentClient()
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login')
  }
  
  // Get user data
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', session.user.id)
    .single()
  
  if (userError || !userData) {
    console.error('Error fetching user data:', userError)
    redirect('/dashboard')
  }
  
  // Verify the visit exists and belongs to the user's organization
  const { data: visitData, error: visitError } = await supabase
    .from('visits')
    .select('id, location_id, status')
    .eq('id', visitId)
    .eq('org_id', userData.org_id)
    .single()
  
  if (visitError || !visitData) {
    console.error('Error fetching visit data:', visitError)
    redirect('/dashboard/visitors')
  }
  
  // Check if the visit is in a valid state for check-in
  if (visitData.status !== VisitStatus.SCHEDULED) {
    redirect('/dashboard/visitors')
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Check In Pre-registered Visitor</h1>
      
      <VisitorCheckInForm 
        orgId={userData.org_id} 
        locationId={visitData.location_id}
        visitId={visitId}
      />
    </div>
  )
}
