import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import { cookies } from 'next/headers'
import VisitorCheckInForm from '@/components/visitor/VisitorCheckInForm'
import VisitorSearchForm from '@/components/visitor/VisitorSearchForm'

export default async function VisitorCheckInPage() {
  const cookieStore = cookies()
  const supabase = createServerComponentClient()
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login')
  }
  
  // Get user data
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', session.user.id)
    .single()
  
  if (userError || !userData) {
    console.error('Error fetching user data:', userError)
    redirect('/dashboard')
  }
  
  // We no longer fetch a default location - the component will handle location selection
  // based on user permissions
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Visitor Check-In</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">New Visitor</h2>
          <VisitorCheckInForm 
            orgId={userData.org_id}
          />
        </div>
        
        <div>
          <VisitorSearchForm className="sticky top-8" />
        </div>
      </div>
    </div>
  )
}
