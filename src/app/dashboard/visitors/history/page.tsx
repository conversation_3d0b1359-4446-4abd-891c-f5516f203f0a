import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'
import Link from 'next/link'
import VisitorHistoryClient from './VisitorHistoryClient'

export default async function VisitorHistoryPage() {
  const supabase = createServerComponentClient()
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login')
  }
  
  // Get user data
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, org_id, role')
    .eq('id', session.user.id)
    .single()
  
  if (userError || !userData) {
    console.error('Error fetching user data:', userError)
    redirect('/dashboard')
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Visit History</h1>
        <div className="space-x-2">
          <Link 
            href="/dashboard/visitors" 
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Back to Visitors
          </Link>
          <Link 
            href="/dashboard/visitors/pre-register" 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Pre-register Visitor
          </Link>
        </div>
      </div>
      
      <VisitorHistoryClient orgId={userData.org_id} />
    </div>
  )
}
