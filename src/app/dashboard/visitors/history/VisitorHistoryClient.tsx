'use client'

import { useState } from 'react'
import VisitorFilter, { FilterState } from '@/components/visitor/VisitorFilter'
import VisitorSearch, { SearchResult } from '@/components/visitor/VisitorSearch'
import VisitorList from '@/components/visitor/VisitorList'
import { VisitStatus } from '@/types'

interface VisitorHistoryClientProps {
  orgId: string
}

export default function VisitorHistoryClient({ orgId }: VisitorHistoryClientProps) {
  // Initialize with completed visits filter
  const [filters, setFilters] = useState<FilterState>({
    status: [VisitStatus.CHECKED_OUT, VisitStatus.CANCELLED, VisitStatus.NO_SHOW],
    dateRange: {
      from: null,
      to: null
    },
    hostId: null,
    locationId: null
  })
  
  const [searchResults, setSearchResults] = useState<SearchResult[] | null>(null)
  
  const handleFilterChange = (newFilters: FilterState) => {
    setFilters(newFilters)
    // Clear search results when filters change
    setSearchResults(null)
  }
  
  const handleSearch = (query: string, results: SearchResult[]) => {
    setSearchResults(results.length > 0 ? results : null)
  }
  
  return (
    <div>
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="w-full md:w-2/3">
          <VisitorSearch orgId={orgId} onSearch={handleSearch} />
        </div>
      </div>
      
      <VisitorFilter orgId={orgId} onFilterChange={handleFilterChange} />
      
      <VisitorList 
        orgId={orgId} 
        filters={filters} 
        searchResults={searchResults} 
      />
      
      <div className="mt-8 bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Export Options</h2>
        <div className="flex flex-wrap gap-4">
          <button 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            onClick={() => alert('Export to CSV functionality will be implemented soon')}
          >
            Export to CSV
          </button>
          <button 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            onClick={() => alert('Export to PDF functionality will be implemented soon')}
          >
            Export to PDF
          </button>
          <button 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            onClick={() => alert('Print report functionality will be implemented soon')}
          >
            Print Report
          </button>
        </div>
        <p className="mt-4 text-sm text-gray-500">
          Export options allow you to download or print visitor history data for reporting and record-keeping purposes.
        </p>
      </div>
    </div>
  )
}
