'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import BadgeGenerator from '@/components/visitor/badge/BadgeGenerator'

export default function VisitBadgePage() {
  const params = useParams()
  const visitId = params.id as string
  const router = useRouter()
  const supabase = createClient()
  const [orgId, setOrgId] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Get organization ID and verify visit when component mounts
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (!session) {
          router.push('/login')
          return
        }
        
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('org_id')
          .eq('id', session.user.id)
          .single()
        
        if (userError || !userData) {
          console.error('Error fetching user data:', userError)
          router.push('/dashboard')
          return
        }
        
        // Verify visit belongs to user's organization
        const { data: visitData, error: visitError } = await supabase
          .from('visits')
          .select('id')
          .eq('id', visitId)
          .eq('org_id', userData.org_id)
          .single()
        
        if (visitError || !visitData) {
          console.error('Error fetching visit data:', visitError)
          router.push('/dashboard/visitors')
          return
        }
        
        setOrgId(userData.org_id)
      } catch (error) {
        console.error('Error:', error)
        setError('An error occurred. Please try again.')
      } finally {
        setLoading(false)
      }
    }
    
    loadData()
  }, [visitId, router, supabase])
  
  const handlePrint = () => {
    // After printing, go back to the visitors page
    router.push('/dashboard/visitors')
  }
  
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-500">Loading...</div>
        </div>
      </div>
    )
  }
  
  if (error || !orgId) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-40">
          <div className="text-red-500">{error || 'An error occurred. Please try again.'}</div>
        </div>
      </div>
    )
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Visitor Badge</h1>
      
      <BadgeGenerator
        orgId={orgId}
        visitId={visitId}
        onPrint={handlePrint}
      />
    </div>
  )
}
