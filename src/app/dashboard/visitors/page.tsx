import { redirect } from 'next/navigation'
import Link from 'next/link'
import { VisitStatus } from '@/types'
import VisitorsPageClient from './VisitorsPageClient'
import { createServerComponentClient } from '@/lib/supabase/server-component-client'

export default async function VisitorsPage() {
  const supabase = await createServerComponentClient()

  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect('/login?redirect=/dashboard/visitors')
  }

  try {
    // Get user data
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, org_id, role')
      .eq('id', session.user.id)
      .single()

    if (userError) {
      console.error('Error fetching user data:', userError)
      // Don't redirect, just show a placeholder or error message
      return (
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold mb-6">Visitor Management</h1>
          <div className="bg-red-50 p-4 rounded-md border border-red-200">
            <p className="text-red-700">Unable to load visitor data. Please try refreshing the page.</p>
            <p className="text-sm mt-2">Error: {userError.message}</p>
          </div>
        </div>
      )
    }

    // Get statistics
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayISOString = today.toISOString()

    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
    const monthStartISOString = monthStart.toISOString()

    // Today's visitors
    const { count: todayCount, error: todayError } = await supabase
      .from('visits')
      .select('*', { count: 'exact', head: true })
      .eq('org_id', userData.org_id)
      .gte('scheduled_time', todayISOString)
      .lt('scheduled_time', new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString())

    // Currently checked in
    const { count: checkedInCount, error: checkedInError } = await supabase
      .from('visits')
      .select('*', { count: 'exact', head: true })
      .eq('org_id', userData.org_id)
      .eq('status', VisitStatus.CHECKED_IN)

    // Upcoming scheduled
    const { count: scheduledCount, error: scheduledError } = await supabase
      .from('visits')
      .select('*', { count: 'exact', head: true })
      .eq('org_id', userData.org_id)
      .eq('status', VisitStatus.SCHEDULED)
      .gte('scheduled_time', new Date().toISOString())

    // Total this month
    const { count: monthCount, error: monthError } = await supabase
      .from('visits')
      .select('*', { count: 'exact', head: true })
      .eq('org_id', userData.org_id)
      .gte('scheduled_time', monthStartISOString)
      .lt('scheduled_time', new Date(today.getFullYear(), today.getMonth() + 1, 1).toISOString())

    if (todayError || checkedInError || scheduledError || monthError) {
      console.error('Error fetching statistics:', {
        todayError, checkedInError, scheduledError, monthError
      })
    }

    const stats = {
      todayCount: todayCount || 0,
      checkedInCount: checkedInCount || 0,
      scheduledCount: scheduledCount || 0,
      monthCount: monthCount || 0
    }

    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Visitor Management</h1>
          <div className="space-x-2">
            <Link
              href="/dashboard/visitors/pre-register"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Pre-register Visitor
            </Link>
            <Link
              href="/dashboard/visitors/check-in"
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              Check-in Visitor
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.todayCount}</div>
            <div className="text-sm text-gray-500">Today&apos;s Visitors</div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="text-2xl font-bold text-green-600">{stats.checkedInCount}</div>
            <div className="text-sm text-gray-500">Currently Checked In</div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="text-2xl font-bold text-purple-600">{stats.scheduledCount}</div>
            <div className="text-sm text-gray-500">Upcoming Scheduled</div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="text-2xl font-bold text-gray-600">{stats.monthCount}</div>
            <div className="text-sm text-gray-500">Total This Month</div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="md:col-span-3">
            <VisitorsPageClient orgId={userData.org_id} />
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Quick Links</h2>
            <ul className="space-y-2">
              <li>
                <Link href="/dashboard/visitors/all" className="text-blue-600 hover:underline">
                  View All Visitors
                </Link>
              </li>
              <li>
                <Link href="/dashboard/visitors/history" className="text-blue-600 hover:underline">
                  Visit History
                </Link>
              </li>
              <li>
                <Link href="/dashboard/visitors/agreements" className="text-blue-600 hover:underline">
                  Manage Agreements
                </Link>
              </li>
              <li>
                <Link href="/dashboard/visitors/badges" className="text-blue-600 hover:underline">
                  Manage Badge Templates
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error('Unexpected error in VisitorsPage:', error)
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Visitor Management</h1>
        <div className="bg-red-50 p-4 rounded-md border border-red-200">
          <p className="text-red-700">An unexpected error occurred. Please try refreshing the page.</p>
        </div>
      </div>
    )
  }
}
