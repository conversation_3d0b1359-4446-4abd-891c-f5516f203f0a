'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import SubscriptionInfo from '@/components/subscription/SubscriptionInfo'
import PlanSelector from '@/components/subscription/PlanSelector'
import CheckoutButton from '@/components/subscription/CheckoutButton'
import { getAllPlans } from '@/lib/stripe/plans'
import { Loader2, CreditCard } from 'lucide-react'

import { Subscription } from '@/types'

export default function BillingPage() {
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [orgId, setOrgId] = useState<string | null>(null)
  const [selectedPlanId, setSelectedPlanId] = useState('professional')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const router = useRouter()
  const supabase = createClient()
  
  // Get all subscription plans
  const plans = getAllPlans()
  
  useEffect(() => {
    const getSubscriptionData = async () => {
      setIsLoading(true)
      
      try {
        // Check if the user is authenticated
        const { data: { user }, error: authError } = await supabase.auth.getUser()
        
        if (authError || !user) {
          // Redirect to login if not authenticated
          router.push('/login')
          return
        }
        
        // Get the organization ID
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('org_id')
          .eq('id', user.id)
          .single()
        
        if (userError || !userData) {
          setError('Error fetching user data')
          setIsLoading(false)
          return
        }
        
        setOrgId(userData.org_id)
        
        // Get the subscription data
        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('org_id', userData.org_id)
          .single()
        
        if (subscriptionError) {
          // No subscription found, but that's okay
          setSubscription(null)
        } else {
          setSubscription(subscriptionData)
        }
      } catch (error) {
        console.error('Error fetching subscription data:', error)
        setError('An error occurred while fetching subscription data')
      } finally {
        setIsLoading(false)
      }
    }
    
    getSubscriptionData()
  }, [router, supabase])
  
  // Handle plan selection
  const handleSelectPlan = (planId: string) => {
    setSelectedPlanId(planId)
  }
  
  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="w-full max-w-4xl text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
          <p className="mt-4 text-gray-600">Loading subscription information...</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="flex-1 p-6 overflow-auto">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Billing & Subscription</h2>
          <div className="bg-blue-50 p-3 rounded-full">
            <CreditCard className="h-6 w-6 text-blue-600" />
          </div>
        </div>
        
        {error && (
          <div className="mb-6 p-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
            {error}
          </div>
        )}
        
        {subscription ? (
          // Display current subscription
          <SubscriptionInfo subscription={subscription} />
        ) : (
          // Display plan selection
          <div className="space-y-8">
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-xl font-semibold mb-4">Choose a Subscription Plan</h2>
              <p className="text-gray-600 mb-6">
                Select a plan that best fits your organization&apos;s needs.
              </p>
              
              <PlanSelector
                plans={plans}
                selectedPlanId={selectedPlanId}
                onSelectPlan={handleSelectPlan}
              />
              
              {orgId && (
                <div className="mt-8">
                  <CheckoutButton
                    planId={selectedPlanId}
                    orgId={orgId}
                    buttonText={selectedPlanId === 'enterprise' ? 'Contact Sales' : 'Subscribe Now'}
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
