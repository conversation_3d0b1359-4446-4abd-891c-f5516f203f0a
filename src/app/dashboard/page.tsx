'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { clearSupabaseCookies } from '@/lib/supabase/cookies'
import {
  ArrowUpRight,
  Calendar,
  CheckCircle,
  UserPlus,
  Users,
  XCircle
} from 'lucide-react'

export default function DashboardPage() {
  // Using any type for simplicity in this demo
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const getUser = async () => {
      try {
        console.log('Dashboard: Attempting to get user...')

        // First, let's check what cookies we have
        const cookies = document.cookie
        console.log('Dashboard: Current cookies:', cookies.split(';').filter(c => c.includes('sb-')))

        // Try to get the session first to see if it exists
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        console.log('Dashboard: Session check result:', {
          hasSession: !!session,
          sessionError,
          userId: session?.user?.id,
          expiresAt: session?.expires_at
        })

        if (sessionError) {
          console.error('Dashboard: Session error:', sessionError)

          // Check if the error is related to stale cookies
          const errorMessage = sessionError.message || String(sessionError)
          if (errorMessage.includes('stale cookie') || errorMessage.includes('decode to a UTF-8 string')) {
            console.log('Dashboard: Stale cookie error detected, clearing cookies and redirecting to login')
            clearSupabaseCookies()
            setTimeout(() => {
              router.push('/login?bypass_auth=true')
            }, 500)
            return
          }
        }

        // Now try to get the user
        const { data: { user }, error: userError } = await supabase.auth.getUser()
        console.log('Dashboard: User check result:', {
          hasUser: !!user,
          userError,
          userId: user?.id,
          email: user?.email
        })

        if (userError) {
          console.error('Dashboard: User error:', userError)

          // Check if the error is related to stale cookies
          const errorMessage = userError.message || String(userError)
          if (errorMessage.includes('stale cookie') || errorMessage.includes('decode to a UTF-8 string')) {
            console.log('Dashboard: Stale cookie error detected in getUser, clearing cookies and redirecting to login')
            clearSupabaseCookies()
            setTimeout(() => {
              router.push('/login?bypass_auth=true')
            }, 500)
            return
          }
        }

        setUser(user)

        if (!user) {
          console.log('Dashboard: No user found, redirecting to login')
          router.push('/login')
        } else {
          console.log('Dashboard: User authenticated successfully:', user.email)
        }
      } catch (error) {
        console.error('Dashboard: Unexpected error:', error)

        // Check if the error is related to stale cookies
        const errorMessage = error instanceof Error ? error.message : String(error)
        if (errorMessage.includes('stale cookie') || errorMessage.includes('decode to a UTF-8 string')) {
          console.log('Dashboard: Stale cookie error detected in catch block, clearing cookies and redirecting to login')
          clearSupabaseCookies()
          setTimeout(() => {
            router.push('/login?bypass_auth=true')
          }, 500)
        } else {
          router.push('/login')
        }
      } finally {
        setLoading(false)
      }
    }

    getUser()
  }, [router, supabase])

  // This would be fetched from the database in a real application
  const stats = [
    { label: 'Total Visitors Today', value: 24, icon: Users },
    { label: 'Checked In', value: 18, icon: CheckCircle },
    { label: 'Checked Out', value: 12, icon: XCircle },
    { label: 'Expected Visitors', value: 8, icon: Calendar },
  ]

  // This would be fetched from the database in a real application
  const recentVisitors = [
    { id: 1, name: 'John Smith', company: 'Acme Inc.', host: 'Sarah Johnson', status: 'checked-in', time: '09:30 AM' },
    { id: 2, name: 'Emily Davis', company: 'Tech Solutions', host: 'Michael Brown', status: 'checked-out', time: '10:15 AM' },
    { id: 3, name: 'Robert Wilson', company: 'Global Corp', host: 'Jessica Lee', status: 'checked-in', time: '11:00 AM' },
    { id: 4, name: 'Amanda Miller', company: 'Innovate LLC', host: 'David Chen', status: 'expected', time: '01:30 PM' },
    { id: 5, name: 'James Taylor', company: 'Summit Group', host: 'Lisa Wang', status: 'expected', time: '02:45 PM' },
  ]

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 py-4 px-6 flex items-center justify-between">
        <h1 className="text-xl font-semibold">Dashboard</h1>
        <div className="flex items-center space-x-4">
          <div className="relative">
            <button className="flex items-center space-x-1 text-sm">
              <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white">
                {user?.user_metadata?.full_name ? user.user_metadata.full_name.split(' ').map((n: string) => n[0]).join('').toUpperCase() : 'U'}
              </div>
            </button>
          </div>
        </div>
      </header>

      {/* Page content */}
      <main className="p-6 overflow-auto">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Overview</h2>
            <div className="flex space-x-2">
              <Link
                href="/dashboard/visitors/pre-register"
                className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors flex items-center"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Pre-register Visitor
              </Link>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <div key={index} className="bg-white p-6 rounded-lg shadow-sm border">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                      <p className="text-3xl font-bold mt-1">{stat.value}</p>
                    </div>
                    <div className="bg-blue-50 p-3 rounded-full">
                      <Icon className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {/* Recent Visitors */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="px-6 py-4 border-b flex items-center justify-between">
              <h3 className="font-semibold">Recent Visitors</h3>
              <Link href="/dashboard/visitors" className="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                View all <ArrowUpRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <th className="px-6 py-3">Visitor</th>
                    <th className="px-6 py-3">Company</th>
                    <th className="px-6 py-3">Host</th>
                    <th className="px-6 py-3">Status</th>
                    <th className="px-6 py-3">Time</th>
                    <th className="px-6 py-3">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {recentVisitors.map((visitor) => (
                    <tr key={visitor.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium">{visitor.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-600">
                        {visitor.company}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-600">
                        {visitor.host}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          visitor.status === 'checked-in' 
                            ? 'bg-green-100 text-green-800' 
                            : visitor.status === 'checked-out' 
                            ? 'bg-gray-100 text-gray-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {visitor.status === 'checked-in' 
                            ? 'Checked In' 
                            : visitor.status === 'checked-out' 
                            ? 'Checked Out' 
                            : 'Expected'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-600">
                        {visitor.time}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Link href="#" className="text-blue-600 hover:text-blue-800">
                          View Details
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
