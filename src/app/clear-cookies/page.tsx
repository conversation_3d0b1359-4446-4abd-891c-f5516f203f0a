'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { clearSupabaseCookies, hasSupabaseCookies, getSupabaseCookies } from '@/lib/supabase/cookies'
import { Trash2, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react'

export default function ClearCookiesPage() {
  const [isClearing, setIsClearing] = useState(false)
  const [cleared, setCleared] = useState(false)
  const [cookiesBefore, setCookiesBefore] = useState<Record<string, string>>({})
  const [cookiesAfter, setCookiesAfter] = useState<Record<string, string>>({})
  const router = useRouter()

  const handleClearCookies = async () => {
    setIsClearing(true)
    
    // Get cookies before clearing
    const before = getSupabaseCookies()
    setCookiesBefore(before)
    
    // Clear the cookies
    clearSupabaseCookies()
    
    // Wait a moment for clearing to take effect
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Get cookies after clearing
    const after = getSupabaseCookies()
    setCookiesAfter(after)
    
    setIsClearing(false)
    setCleared(true)
  }

  const handleGoToLogin = () => {
    router.push('/login?bypass_auth=true')
  }

  const hasCookies = hasSupabaseCookies()

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100">
            <Trash2 className="h-6 w-6 text-red-600" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Clear Session Cookies
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            This page helps resolve login issues caused by stale session cookies
          </p>
        </div>

        <div className="bg-white shadow rounded-lg p-6 space-y-6">
          {/* Cookie Status */}
          <div className="flex items-center space-x-3">
            {hasCookies ? (
              <>
                <AlertCircle className="h-5 w-5 text-yellow-500" />
                <span className="text-sm text-gray-700">
                  Supabase cookies detected ({Object.keys(getSupabaseCookies()).length} cookies)
                </span>
              </>
            ) : (
              <>
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm text-gray-700">
                  No Supabase cookies found
                </span>
              </>
            )}
          </div>

          {/* Clear Cookies Button */}
          <button
            onClick={handleClearCookies}
            disabled={isClearing || (!hasCookies && !cleared)}
            className="w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isClearing ? (
              <>
                <RefreshCw className="animate-spin -ml-1 mr-3 h-4 w-4" />
                Clearing cookies...
              </>
            ) : (
              <>
                <Trash2 className="-ml-1 mr-3 h-4 w-4" />
                Clear All Supabase Cookies
              </>
            )}
          </button>

          {/* Results */}
          {cleared && (
            <div className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">
                      Cookies cleared successfully!
                    </h3>
                    <div className="mt-2 text-sm text-green-700">
                      <p>Cleared {Object.keys(cookiesBefore).length} cookies</p>
                      <p>Remaining: {Object.keys(cookiesAfter).length} cookies</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Cookie Details */}
              {Object.keys(cookiesBefore).length > 0 && (
                <details className="text-xs">
                  <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                    Show cleared cookies details
                  </summary>
                  <div className="mt-2 bg-gray-50 p-3 rounded border">
                    <h4 className="font-medium text-gray-700 mb-2">Cookies before clearing:</h4>
                    <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                      {JSON.stringify(cookiesBefore, null, 2)}
                    </pre>
                    {Object.keys(cookiesAfter).length > 0 && (
                      <>
                        <h4 className="font-medium text-gray-700 mb-2 mt-4">Cookies after clearing:</h4>
                        <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                          {JSON.stringify(cookiesAfter, null, 2)}
                        </pre>
                      </>
                    )}
                  </div>
                </details>
              )}
            </div>
          )}

          {/* Go to Login Button */}
          <button
            onClick={handleGoToLogin}
            className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Go to Login Page
          </button>

          {/* Instructions */}
          <div className="text-xs text-gray-500 space-y-2">
            <p><strong>When to use this:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-2">
              <li>Getting "stale cookie data" errors</li>
              <li>Login page keeps retrying session verification</li>
              <li>Stuck in login loops</li>
              <li>Session not establishing after successful login</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
