'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/client'
import { Eye, EyeOff, Loader2 } from 'lucide-react'
import { ensureUserExistsClient } from '@/lib/auth/client-utils'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [redirecting, setRedirecting] = useState(false)
  const [sessionChecked, setSessionChecked] = useState(false)
  const supabase = createClient()
  const isRedirectingRef = useRef(false)

  // Run once on component mount to check for session
  useEffect(() => {
    // Check for URL parameters
    const urlParams = new URLSearchParams(window.location.search)

    // Special flag from middleware to clear redirect attempts
    if (urlParams.has('clear_redirect')) {
      console.log('Clear redirect flag detected, removing redirect attempts')
      localStorage.removeItem('auth_redirect_attempt')
    }

    // Check for redirect count parameter
    if (urlParams.has('_rc')) {
      console.log('Redirect count detected:', urlParams.get('_rc'))
      // If we're being redirected multiple times, there might be an issue with the session
      const redirectCount = parseInt(urlParams.get('_rc') || '0', 10)
      if (redirectCount > 1) {
        console.log('Multiple redirects detected, clearing auth data')
        localStorage.removeItem('auth_redirect_attempt')
        // Clear any Supabase cookies
        document.cookie = "sb-access-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"
        document.cookie = "sb-refresh-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"
      }
    }

    // Check for error parameter
    const errorParam = urlParams.get('error')
    if (errorParam) {
      setError(decodeURIComponent(errorParam))
    }

    // Check if we're already redirecting from another attempt
    const redirectAttempt = localStorage.getItem('auth_redirect_attempt')
    const now = new Date().getTime()

    // If there's a recent redirect attempt, don't try again
    if (redirectAttempt) {
      const timestamp = parseInt(redirectAttempt, 10)
      const timeSinceRedirect = now - timestamp

      // If a redirect was attempted in the last 3 seconds, don't try again
      if (timeSinceRedirect < 3000) {
        console.log('Recent redirect attempt detected, not redirecting again')
        localStorage.removeItem('auth_redirect_attempt')
        setSessionChecked(true)
        setError('Redirect loop detected. Try clearing your auth data with the button below.')
        return
      } else {
        // Clean up old redirect attempts
        localStorage.removeItem('auth_redirect_attempt')
      }
    }

    // Skip session check if it's already been done
    if (sessionChecked || isRedirectingRef.current) return;

    // Check if already logged in
    const checkSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        setSessionChecked(true)

        if (session) {
          console.log('Login page: Session found, redirecting to dashboard')
          isRedirectingRef.current = true
          setRedirecting(true)

          // Set redirect attempt timestamp
          localStorage.setItem('auth_redirect_attempt', now.toString())

          // Force a direct navigation to dashboard
          if (window) {
            window.location.replace('/dashboard')
          }
        } else {
          console.log('Login page: No active session')
        }
      } catch (error) {
        console.error('Error checking session:', error)
        setSessionChecked(true)
      }
    }

    // Always check session on login page load
    checkSession()
  }, [supabase, sessionChecked])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isRedirectingRef.current) return // Prevent multiple submissions

    setIsLoading(true)
    setError(null)

    try {
      // Sign in with email and password
      const { error, data } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error('Login error details:', error)
        throw error
      }

      console.log('Login successful, session established:', !!data.session)

      if (data.session) {
        // Set redirecting state
        setRedirecting(true)
        isRedirectingRef.current = true

        // The auth-helpers-nextjs package handles cookie management automatically

        // Wait for the session to be established
        try {
          console.log('Waiting for session to be established...')

          // Wait a moment for the session to be fully established
          await new Promise(resolve => setTimeout(resolve, 2000))

          // Get the current session
          const { data: { session: currentSession }, error: sessionError } = await supabase.auth.getSession()

          if (sessionError) {
            console.error('Error getting session after login:', sessionError)
            throw new Error(`Error getting session: ${sessionError.message}`)
          }

          if (!currentSession) {
            console.error('No session available after login')
            throw new Error('No session available after login')
          }

          console.log('Session established after login, user ID:', currentSession.user.id)

          console.log('Session established, checking if user exists in public.users table...')

          // Check if the user exists in the public.users table
          try {
            const result = await ensureUserExistsClient()
            console.log('User check result:', result)

            // If the API returns an error about organization name being required,
            // redirect to signup page to complete registration
            if (result.error && result.error.includes('Organization name is required')) {
              console.log('User needs to complete registration with an organization')
              window.location.href = '/signup?complete_registration=true'
              return
            }
          } catch (error: unknown) {
            console.error('Error checking if user exists:', error)

            // If the error is about organization name being required,
            // redirect to signup page to complete registration
            if (error instanceof Error && error.message.includes('Organization name is required')) {
              console.log('User needs to complete registration with an organization')
              window.location.href = '/signup?complete_registration=true'
              return
            }

            // Continue anyway for other errors - we don't want to block the login flow
          }
        } catch (sessionError) {
          console.error('Error establishing session:', sessionError)
          // Continue anyway - we don't want to block the login flow
        }

        // Set redirect attempt timestamp
        localStorage.setItem('auth_redirect_attempt', new Date().getTime().toString())

        // Wait a moment for cookies to be properly set
        await new Promise(resolve => setTimeout(resolve, 100))

        // Force a full page reload to the dashboard
        window.location.replace('/dashboard')
      } else {
        // If no session, show error
        throw new Error('Failed to establish session')
      }

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred during login'
      setError(errorMessage)
      setRedirecting(false)
      isRedirectingRef.current = false
      setIsLoading(false)
    }
  }

  // Add a function to help clear auth data if the user gets stuck in a redirect loop
  const clearAuthCookies = async () => {
    // Sign out using Supabase auth
    await supabase.auth.signOut()
    // Clear localStorage
    localStorage.clear()

    // Reload the page with the bypass parameter to ensure we skip middleware
    window.location.href = '/login?bypass_auth=true';
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="w-full max-w-md">
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold">VisitFlow</h1>
          <p className="mt-2 text-gray-600">Sign in to your account</p>
        </div>

        <div className="bg-white p-8 rounded-lg shadow-sm border">
          {error && (
            <div className="mb-4 p-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
              {error}
              <div className="mt-2">
                <button
                  onClick={clearAuthCookies}
                  className="text-blue-600 underline"
                >
                  Clear auth data and try again
                </button>
              </div>
            </div>
          )}

          {redirecting && (
            <div className="mb-4 p-4 text-sm text-blue-800 rounded-lg bg-blue-50" role="alert">
              Redirecting to dashboard...
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <div className="flex items-center justify-between mb-1">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <Link
                  href="/forgot-password"
                  className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
                >
                  Forgot password?
                </Link>
              </div>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading || redirecting}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <Loader2 className="animate-spin h-5 w-5 mr-2" />
                  Signing in...
                </span>
              ) : redirecting ? (
                <span className="flex items-center justify-center">
                  <Loader2 className="animate-spin h-5 w-5 mr-2" />
                  Redirecting...
                </span>
              ) : (
                'Sign in'
              )}
            </button>
          </form>

          <div className="mt-6 text-center text-sm">
            <p className="text-gray-600">
              Don&apos;t have an account?{' '}
              <Link href="/signup" className="text-blue-600 hover:text-blue-500 font-medium">
                Sign up
              </Link>
            </p>
            <p className="mt-2">
              <Link href="/auth-debug" className="text-sm text-gray-500 hover:text-gray-700">
                Debug authentication status
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
