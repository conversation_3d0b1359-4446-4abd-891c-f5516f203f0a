'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'

import { Eye, EyeOff, Loader2 } from 'lucide-react'




export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [redirecting, setRedirecting] = useState(false)
  const [sessionChecked, setSessionChecked] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const supabase = createClient()
  const isRedirectingRef = useRef(false)

  // Run once on component mount to check for session
  useEffect(() => {
    // Check if cookies were cleared by middleware
    if (searchParams.has('cookies_cleared')) {
      console.log('Cookies were cleared by middleware due to stale data')
      // Clear the parameter from URL to clean it up
      router.replace('/login')
      setSessionChecked(true)
      return
    }

    // Check for bypass_auth parameter - if present, skip session check and clear URL
    if (searchParams.has('bypass_auth')) {
      console.log('Bypass auth parameter detected, skipping session check')
      // Clear the bypass parameter from URL to clean it up
      router.replace('/login')
      setSessionChecked(true)
      return
    }

    // Special flag from middleware to clear redirect attempts
    if (searchParams.has('clear_redirect')) {
      console.log('Clear redirect flag detected, removing redirect attempts')
      localStorage.removeItem('auth_redirect_attempt')
    }

    // Check for redirect count parameter
    if (searchParams.has('_rc')) {
      console.log('Redirect count detected:', searchParams.get('_rc'))
      // If we're being redirected multiple times, there might be an issue with the session
      const redirectCount = parseInt(searchParams.get('_rc') || '0', 10)
      if (redirectCount >= 1) {
        console.log('Redirect detected, this might be a timing issue')
        // Clear the recent_login cookie to prevent middleware bypass
        document.cookie = "recent_login=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"

        if (redirectCount > 1) {
          console.log('Multiple redirects detected, clearing auth data')
          localStorage.removeItem('auth_redirect_attempt')
          // Clear any Supabase cookies
          document.cookie = "sb-access-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"
          document.cookie = "sb-refresh-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"
        }
      }
    }

    // Check for error parameter
    const errorParam = searchParams.get('error')
    if (errorParam) {
      setError(decodeURIComponent(errorParam))
    }

    // Check if we're already redirecting from another attempt
    const redirectAttempt = localStorage.getItem('auth_redirect_attempt')
    const now = new Date().getTime()

    // If there's a recent redirect attempt, don't try again
    if (redirectAttempt) {
      const timestamp = parseInt(redirectAttempt, 10)
      const timeSinceRedirect = now - timestamp

      // If a redirect was attempted in the last 5 seconds, don't try again
      if (timeSinceRedirect < 5000) {
        console.log('Recent redirect attempt detected, not redirecting again')
        localStorage.removeItem('auth_redirect_attempt')
        setSessionChecked(true)
        setError('Redirect loop detected. Please wait a moment and try refreshing the page, or use the button below to clear auth data.')
        return
      } else {
        // Clean up old redirect attempts
        localStorage.removeItem('auth_redirect_attempt')
      }
    }

    // Skip session check if it's already been done
    if (sessionChecked || isRedirectingRef.current) return;

    // Check if already logged in
    const checkSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        setSessionChecked(true)

        if (session) {
          console.log('Login page: Session found, redirecting to dashboard')
          isRedirectingRef.current = true
          setRedirecting(true)

          // Set redirect attempt timestamp
          localStorage.setItem('auth_redirect_attempt', now.toString())

          // Add a small delay to ensure session is fully established
          await new Promise(resolve => setTimeout(resolve, 200))

          // Use Next.js router for navigation
          router.push('/dashboard')
        } else {
          console.log('Login page: No active session')
        }
      } catch (error) {
        console.error('Error checking session:', error)
        setSessionChecked(true)
      }
    }

    // Always check session on login page load
    checkSession()
  }, [supabase, sessionChecked, router, searchParams])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isRedirectingRef.current) return // Prevent multiple submissions

    setIsLoading(true)
    setError(null)

    try {
      // Sign in with email and password
      const { error, data } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error('Login error details:', error)
        throw error
      }

      console.log('Login successful, session established:', !!data.session)
      console.log('Session details:', {
        hasSession: !!data.session,
        userId: data.session?.user?.id,
        email: data.session?.user?.email,
        expiresAt: data.session?.expires_at
      })

      if (data.session) {
        // Set redirecting state
        setRedirecting(true)
        isRedirectingRef.current = true

        console.log('Session established after login, user ID:', data.session.user.id)

        // Check if the user exists in the public.users table
        // Call the API directly to avoid creating additional Supabase clients
        try {
          console.log('Checking if user exists in public.users table...')

          const userId = data.session.user.id
          const email = data.session.user.email
          const fullName = data.session.user.user_metadata?.full_name || data.session.user.user_metadata?.name || ''

          const response = await fetch('/api/auth/ensure-user', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              userId,
              email,
              fullName
            }),
          })

          if (!response.ok) {
            const error = await response.json()
            console.error('API error response:', error)

            // If the error is about organization name being required, it means this is a new user
            if (error.error && error.error.includes('Organization name is required')) {
              console.log('User needs to complete registration with an organization')
              router.push('/signup?complete_registration=true')
              return
            }

            throw new Error(error.error || 'Failed to ensure user exists')
          }

          const result = await response.json()
          console.log('User check result:', result)
        } catch (error: unknown) {
          console.error('Error checking if user exists:', error)

          // If the error is about organization name being required,
          // redirect to signup page to complete registration
          if (error instanceof Error && error.message.includes('Organization name is required')) {
            console.log('User needs to complete registration with an organization')
            router.push('/signup?complete_registration=true')
            return
          }

          // Continue anyway for other errors - we don't want to block the login flow
        }

        // Use the session from the login response directly
        // The login response already contains a valid session, no need to re-fetch it
        console.log('Using session from login response')
        const sessionToUse = data.session

        console.log('Session established successfully:', {
          userId: sessionToUse.user.id,
          email: sessionToUse.user.email,
          expiresAt: sessionToUse.expires_at
        })

        console.log('Login successful, redirecting to dashboard')

        // Use Next.js router for navigation instead of window.location
        // This ensures proper session handling
        router.push('/dashboard')
      } else {
        // This should never happen since we check for data.session earlier
        console.error('No session found in login response')
        throw new Error('Login succeeded but no session was returned. Please try again.')
      }

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred during login'
      setError(errorMessage)
      setRedirecting(false)
      isRedirectingRef.current = false
      setIsLoading(false)
    }
  }

  // Add a function to help clear auth data if the user gets stuck in a redirect loop
  const clearAuthCookies = async () => {
    try {
      console.log('Clearing all authentication data...')

      // Sign out using Supabase auth
      await supabase.auth.signOut()

      // Clear localStorage
      localStorage.clear()

      // Clear all possible Supabase cookies more thoroughly
      const cookiesToClear = [
        'sb-access-token',
        'sb-refresh-token',
        'supabase-auth-token',
        'supabase.auth.token',
        'recent_login'
      ]

      cookiesToClear.forEach(cookieName => {
        // Clear for root path
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
        // Clear for current domain
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${typeof window !== 'undefined' ? window.location.hostname : 'localhost'};`
        // Clear without domain specification
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC;`
      })

      console.log('Authentication data cleared successfully')

      // Wait a moment for cookies to be cleared
      await new Promise(resolve => setTimeout(resolve, 500))

      // Use Next.js router to navigate with the bypass parameter
      router.push('/login?bypass_auth=true')
    } catch (error) {
      console.error('Error clearing auth data:', error)
      // Force navigation anyway
      router.push('/login?bypass_auth=true')
    }
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="w-full max-w-md">
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold">VisitFlow</h1>
          <p className="mt-2 text-gray-600">Sign in to your account</p>
        </div>

        <div className="bg-white p-8 rounded-lg shadow-sm border">
          {error && (
            <div className="mb-4 p-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
              {error}
              <div className="mt-2">
                <button
                  onClick={clearAuthCookies}
                  className="text-blue-600 underline"
                >
                  Clear auth data and try again
                </button>
              </div>
            </div>
          )}

          {redirecting && (
            <div className="mb-4 p-4 text-sm text-blue-800 rounded-lg bg-blue-50" role="alert">
              Redirecting to dashboard...
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <div className="flex items-center justify-between mb-1">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <Link
                  href="/forgot-password"
                  className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
                >
                  Forgot password?
                </Link>
              </div>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading || redirecting}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <Loader2 className="animate-spin h-5 w-5 mr-2" />
                  Signing in...
                </span>
              ) : redirecting ? (
                <span className="flex items-center justify-center">
                  <Loader2 className="animate-spin h-5 w-5 mr-2" />
                  Redirecting...
                </span>
              ) : (
                'Sign in'
              )}
            </button>
          </form>

          <div className="mt-6 text-center text-sm">
            <p className="text-gray-600">
              Don&apos;t have an account?{' '}
              <Link href="/signup" className="text-blue-600 hover:text-blue-500 font-medium">
                Sign up
              </Link>
            </p>
            <p className="mt-2">
              <Link href="/auth-debug" className="text-sm text-gray-500 hover:text-gray-700">
                Debug authentication status
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
