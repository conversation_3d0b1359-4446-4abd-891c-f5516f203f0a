'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

export default function CookieTestPage() {
  const [cookies, setCookies] = useState<string>('')
  const [sessionInfo, setSessionInfo] = useState<any>(null) // eslint-disable-line @typescript-eslint/no-explicit-any
  const [logs, setLogs] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const supabase = createClient()

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    const logMessage = `${timestamp}: ${message}`
    setLogs(prev => [...prev, logMessage])
    console.log(logMessage)
  }

  const refreshCookies = () => {
    setCookies(document.cookie)
  }

  const checkSession = async () => {
    setLoading(true)
    addLog('Checking current session...')
    
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        addLog(`❌ Session error: ${error.message}`)
        setSessionInfo(null)
      } else if (session) {
        addLog(`✅ Session found: ${session.user.email}`)
        setSessionInfo(session)
      } else {
        addLog('ℹ️ No session found')
        setSessionInfo(null)
      }
    } catch (err) {
      addLog(`❌ Exception checking session: ${err}`)
      setSessionInfo(null)
    } finally {
      setLoading(false)
    }
  }

  const clearAllCookies = () => {
    addLog('Clearing all Supabase cookies...')
    
    const cookiesToClear = [
      'sb-access-token',
      'sb-refresh-token', 
      'supabase-auth-token',
      'supabase.auth.token',
      'recent_login'
    ]
    
    cookiesToClear.forEach(cookieName => {
      // Clear for root path
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
      // Clear for current domain
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`
      // Clear without domain specification
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC;`
    })
    
    addLog('Cookies cleared')
    refreshCookies()
  }

  const signOut = async () => {
    addLog('Signing out...')
    try {
      await supabase.auth.signOut()
      addLog('✅ Signed out successfully')
      setSessionInfo(null)
    } catch (err) {
      addLog(`❌ Error signing out: ${err}`)
    }
  }

  useEffect(() => {
    refreshCookies()
    checkSession()
  }, [])

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Cookie & Session Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Current Cookies</h2>
          <div className="bg-gray-100 p-4 rounded font-mono text-sm max-h-40 overflow-y-auto mb-4">
            {cookies || 'No cookies found'}
          </div>
          <button
            onClick={refreshCookies}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 mr-2"
          >
            Refresh
          </button>
          <button
            onClick={clearAllCookies}
            className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
          >
            Clear All
          </button>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Session Status</h2>
          {loading ? (
            <p>Loading...</p>
          ) : sessionInfo ? (
            <div className="space-y-2">
              <p className="text-green-600">✅ Session Active</p>
              <p><strong>Email:</strong> {sessionInfo.user.email}</p>
              <p><strong>User ID:</strong> {sessionInfo.user.id}</p>
              <p><strong>Expires:</strong> {new Date(sessionInfo.expires_at * 1000).toLocaleString()}</p>
            </div>
          ) : (
            <p className="text-red-600">❌ No Active Session</p>
          )}
          
          <div className="mt-4 space-x-2">
            <button
              onClick={checkSession}
              disabled={loading}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
            >
              Check Session
            </button>
            {sessionInfo && (
              <button
                onClick={signOut}
                className="px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700"
              >
                Sign Out
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="mt-6 bg-white p-6 rounded-lg shadow">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Debug Logs</h2>
          <button
            onClick={() => setLogs([])}
            className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
          >
            Clear Logs
          </button>
        </div>
        
        <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-60 overflow-y-auto">
          {logs.length === 0 ? (
            <p>No logs yet...</p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="mb-1">{log}</div>
            ))
          )}
        </div>
      </div>
      
      <div className="mt-6 text-center space-x-4">
        <a
          href="/login"
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 inline-flex items-center"
        >
          Go to Login
        </a>
        <a
          href="/dashboard"
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 inline-flex items-center"
        >
          Try Dashboard
        </a>
      </div>
    </div>
  )
}
