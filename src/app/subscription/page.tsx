'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { getAllPlans } from '@/lib/stripe/plans'
import PlanSelector, { EnterprisePlanContact } from '@/components/subscription/PlanSelector'
import CheckoutButton from '@/components/subscription/CheckoutButton'
import { Loader2 } from 'lucide-react'

export default function SubscriptionPage() {
  const [selectedPlanId, setSelectedPlanId] = useState('professional') // Default to professional plan
  const [showEnterpriseForm, setShowEnterpriseForm] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [orgId, setOrgId] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  const router = useRouter()
  const searchParams = useSearchParams()
  const supabase = createClient()

  // Get all subscription plans
  const plans = getAllPlans()

  // Check for checkout success or canceled
  const checkoutSuccess = searchParams.get('checkout_success')
  const checkoutCanceled = searchParams.get('checkout_canceled')

  useEffect(() => {
    const checkAuth = async () => {
      setIsLoading(true)

      try {
        // Check if the user is authenticated
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError || !user) {
          // Redirect to login if not authenticated
          router.push('/login')
          return
        }

        // Get the organization ID
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('org_id')
          .eq('id', user.id)
          .single()

        if (userError || !userData) {
          // If the user doesn't have an organization, redirect to signup
          router.push('/signup')
          return
        }

        setOrgId(userData.org_id)

        // Check if the organization already has a subscription
        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('org_id', userData.org_id)
          .single()

        if (!subscriptionError && subscriptionData) {
          // If the organization already has a subscription, redirect to dashboard
          router.push('/dashboard')
          return
        }
      } catch (error) {
        console.error('Error checking auth:', error)
        setError('An error occurred while checking authentication')
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [router, supabase])

  // Handle plan selection
  const handleSelectPlan = (planId: string) => {
    setSelectedPlanId(planId)

    // If enterprise plan is selected, show the contact form
    if (planId === 'enterprise') {
      setShowEnterpriseForm(true)
    } else {
      setShowEnterpriseForm(false)
    }
  }

  // Handle enterprise form cancel
  const handleEnterpriseCancel = () => {
    setShowEnterpriseForm(false)
    setSelectedPlanId('professional') // Reset to professional plan
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="w-full max-w-4xl text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="w-full max-w-4xl">
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold">Choose Your Plan</h1>
          <p className="mt-2 text-gray-600">Select the plan that best fits your organization&apos;s needs</p>
        </div>

        {error && (
          <div className="mb-6 p-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
            {error}
          </div>
        )}

        {checkoutSuccess && (
          <div className="mb-6 p-4 text-sm text-green-800 rounded-lg bg-green-50" role="alert">
            Your subscription has been successfully set up! You will be redirected to the dashboard shortly.
          </div>
        )}

        {checkoutCanceled && (
          <div className="mb-6 p-4 text-sm text-yellow-800 rounded-lg bg-yellow-50" role="alert">
            Your checkout was canceled. You can try again when you&apos;re ready.
          </div>
        )}

        {showEnterpriseForm ? (
          <EnterprisePlanContact onCancel={handleEnterpriseCancel} />
        ) : (
          <div className="space-y-8">
            <PlanSelector
              plans={plans}
              selectedPlanId={selectedPlanId}
              onSelectPlan={handleSelectPlan}
            />

            {orgId && (
              <div className="mt-8">
                <CheckoutButton
                  planId={selectedPlanId}
                  orgId={orgId}
                  buttonText={selectedPlanId === 'enterprise' ? 'Contact Sales' : 'Subscribe Now'}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
