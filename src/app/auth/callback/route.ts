import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@/lib/supabase/route-handler'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  // No code means we can't do anything
  if (!code) {
    console.error('No code parameter provided to auth callback')
    return NextResponse.redirect(new URL('/login?error=No%20authentication%20code%20provided', request.url))
  }

  console.log('Auth callback processing code exchange')

  try {
    // Use the route handler client
    const supabase = await createRouteHandlerClient()

    // Exchange the code for a session
    const { error } = await supabase.auth.exchangeCodeForSession(code)

    if (error) {
      console.error('Error exchanging code for session:', error.message)
      return NextResponse.redirect(new URL(`/login?error=${encodeURIComponent(error.message)}`, request.url))
    }

    // Verify that the session was created
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      console.error('Failed to establish session after code exchange')
      return NextResponse.redirect(new URL('/login?error=Failed%20to%20establish%20session', request.url))
    }

    console.log('Auth callback successful, redirecting to dashboard')
    return NextResponse.redirect(new URL('/dashboard', request.url))

  } catch (error) {
    console.error('Auth callback error:', error)
    return NextResponse.redirect(new URL('/login?error=Authentication%20failed', request.url))
  }
}
