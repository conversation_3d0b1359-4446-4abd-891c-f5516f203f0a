'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useSupabaseSession } from '@/hooks/useSupabaseSession'

export default function AuthDebugPage() {
  const [cookies, setCookies] = useState<string>('')
  // Error state removed as it was unused
  const supabase = createClient()

  // Use our simplified session hook
  const { session: sessionInfo, loading } = useSupabaseSession()

  // Update cookies display when needed
  const refreshCookies = () => {
    setCookies(document.cookie)
  }

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    window.location.reload()
  }

  const handleGoToDashboard = () => {
    window.location.href = '/dashboard'
  }

  const handleClearCookies = () => {
    // Clear all Supabase-related cookies
    document.cookie = "sb-access-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "sb-refresh-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    localStorage.clear(); // Clear all localStorage items to be thorough
    window.location.reload()
  }

  const handleRefreshCookies = () => {
    refreshCookies()
  }

  // Initialize cookies on component mount
  useEffect(() => {
    refreshCookies()
  }, [])

  return (
    <div className="container mx-auto p-6 max-w-3xl">
      <h1 className="text-3xl font-bold mb-6">Authentication Debug</h1>

      {loading ? (
        <p>Loading authentication status...</p>
      ) : error ? (
        <div className="bg-red-50 p-4 mb-6 rounded-md border border-red-200">
          <h2 className="text-lg font-semibold text-red-700">Error</h2>
          <p className="text-red-600">{error}</p>
        </div>
      ) : (
        <>
          <div className="bg-blue-50 p-6 mb-6 rounded-md border border-blue-200">
            <h2 className="text-xl font-semibold mb-4">Session Status</h2>
            <div className="mb-2">
              <span className="font-medium">Session exists:</span> {sessionInfo ? 'Yes' : 'No'}
            </div>

            {sessionInfo && (
              <>
                <div className="mb-2">
                  <span className="font-medium">User ID:</span> {sessionInfo.user?.id}
                </div>
                <div className="mb-2">
                  <span className="font-medium">Email:</span> {sessionInfo.user?.email}
                </div>
                <div className="mb-2">
                  <span className="font-medium">Expires at:</span> {new Date(sessionInfo.expires_at * 1000).toLocaleString()}
                </div>
              </>
            )}
          </div>

          <div className="bg-gray-50 p-6 mb-6 rounded-md border border-gray-200">
            <h2 className="text-xl font-semibold mb-4">Cookies</h2>
            <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-40 text-sm">
              {cookies || 'No cookies found'}
            </pre>
          </div>

          <div className="flex flex-wrap gap-4 mt-6">
            {sessionInfo && (
              <button
                onClick={handleGoToDashboard}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Go to Dashboard
              </button>
            )}

            {sessionInfo && (
              <button
                onClick={handleSignOut}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Sign Out
              </button>
            )}

            <button
              onClick={handleClearCookies}
              className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
            >
              Clear Auth Data
            </button>

            <button
              onClick={handleRefreshCookies}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Refresh Cookies
            </button>

            <a
              href="/login"
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 inline-flex items-center"
            >
              Go to Login
            </a>
          </div>
        </>
      )}
    </div>
  )
}