'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import Link from 'next/link'

export default function AuthTestPage() {
  const [session, setSession] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [logs, setLogs] = useState<string[]>([])
  const supabase = createClient()

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `${timestamp}: ${message}`])
    console.log(message)
  }

  useEffect(() => {
    const checkAuth = async () => {
      addLog('Starting auth check...')
      
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          addLog(`Session error: ${error.message}`)
        } else if (session) {
          addLog(`Session found: User ID ${session.user.id}`)
          setSession(session)
        } else {
          addLog('No session found')
        }
      } catch (err) {
        addLog(`Exception during session check: ${err}`)
      } finally {
        setLoading(false)
        addLog('Auth check completed')
      }
    }

    checkAuth()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      addLog(`Auth state changed: ${event}`)
      setSession(session)
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase])

  const testDashboardAccess = () => {
    addLog('Testing dashboard access...')
    window.location.href = '/dashboard'
  }

  const clearAuthData = async () => {
    addLog('Clearing auth data...')
    await supabase.auth.signOut()
    localStorage.clear()
    addLog('Auth data cleared')
    window.location.reload()
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Authentication Test Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Session Status</h2>
          {loading ? (
            <p>Loading...</p>
          ) : session ? (
            <div className="space-y-2">
              <p className="text-green-600">✅ Authenticated</p>
              <p><strong>User ID:</strong> {session.user.id}</p>
              <p><strong>Email:</strong> {session.user.email}</p>
              <p><strong>Session expires:</strong> {new Date(session.expires_at * 1000).toLocaleString()}</p>
            </div>
          ) : (
            <p className="text-red-600">❌ Not authenticated</p>
          )}
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Actions</h2>
          <div className="space-y-3">
            <Link 
              href="/login"
              className="block w-full px-4 py-2 bg-blue-600 text-white rounded text-center hover:bg-blue-700"
            >
              Go to Login
            </Link>
            
            <button
              onClick={testDashboardAccess}
              className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Test Dashboard Access
            </button>
            
            <button
              onClick={clearAuthData}
              className="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Clear Auth Data
            </button>
          </div>
        </div>
      </div>

      <div className="mt-8 bg-gray-100 p-6 rounded-lg">
        <h2 className="text-lg font-semibold mb-4">Debug Logs</h2>
        <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
          {logs.length === 0 ? (
            <p>No logs yet...</p>
          ) : (
            logs.map((log, index) => (
              <div key={index}>{log}</div>
            ))
          )}
        </div>
        <button
          onClick={() => setLogs([])}
          className="mt-2 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
        >
          Clear Logs
        </button>
      </div>
    </div>
  )
}
