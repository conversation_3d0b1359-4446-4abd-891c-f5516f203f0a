'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { ensureUserExistsClient } from '@/lib/auth/client-utils'

export default function TestUserApiPage() {
  const [loading, setLoading] = useState(false)
  const [logs, setLogs] = useState<string[]>([])
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    const logMessage = `${timestamp}: ${message}`
    setLogs(prev => [...prev, logMessage])
    console.log(logMessage)
  }

  const testUserApi = async () => {
    setLoading(true)
    setError(null)
    setLogs([])

    try {
      addLog('Starting user API test...')
      
      // Check if user is logged in
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        addLog('❌ No session found - please log in first')
        setError('Please log in first')
        return
      }
      
      addLog(`✅ Session found for user: ${session.user.email}`)
      addLog(`User ID: ${session.user.id}`)
      
      // Test 1: Call ensureUserExistsClient without organization name (for existing users)
      addLog('Test 1: Calling ensureUserExistsClient without organization name...')
      try {
        const result1 = await ensureUserExistsClient(undefined, session)
        addLog(`✅ Test 1 successful: ${JSON.stringify(result1)}`)
      } catch (err) {
        addLog(`❌ Test 1 failed: ${err}`)
        
        // If it fails because organization name is required, that means this is a new user
        if (err instanceof Error && err.message.includes('Organization name is required')) {
          addLog('This appears to be a new user who needs to complete registration')
          
          // Test 2: Call with organization name for new users
          addLog('Test 2: Calling ensureUserExistsClient with organization name...')
          try {
            const result2 = await ensureUserExistsClient('Test Organization', session)
            addLog(`✅ Test 2 successful: ${JSON.stringify(result2)}`)
          } catch (err2) {
            addLog(`❌ Test 2 failed: ${err2}`)
          }
        }
      }
      
      // Test 3: Call without passing session (should fetch it internally)
      addLog('Test 3: Calling ensureUserExistsClient without session parameter...')
      try {
        const result3 = await ensureUserExistsClient()
        addLog(`✅ Test 3 successful: ${JSON.stringify(result3)}`)
      } catch (err3) {
        addLog(`❌ Test 3 failed: ${err3}`)
      }
      
      addLog('All tests completed!')
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      addLog(`❌ Test failed: ${errorMessage}`)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const clearLogs = () => {
    setLogs([])
    setError(null)
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Test User API</h1>
      
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-lg font-semibold mb-4">API Test</h2>
        
        <div className="space-y-4">
          <p className="text-gray-600">
            This page tests the ensureUserExistsClient function to verify it works correctly
            for both existing and new users.
          </p>
          
          <button
            onClick={testUserApi}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Testing...' : 'Test User API'}
          </button>
          
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Test Results</h2>
          <button
            onClick={clearLogs}
            className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
          >
            Clear
          </button>
        </div>
        
        <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
          {logs.length === 0 ? (
            <p>No test results yet...</p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="mb-1">{log}</div>
            ))
          )}
        </div>
      </div>
      
      <div className="mt-6 text-center space-x-4">
        <a
          href="/login"
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 inline-flex items-center"
        >
          Go to Login
        </a>
        <a
          href="/auth-test"
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 inline-flex items-center"
        >
          Auth Test
        </a>
      </div>
    </div>
  )
}
