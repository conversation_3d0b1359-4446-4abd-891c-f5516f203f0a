import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase/admin';

/**
 * POST /api/auth/ensure-user
 * Ensures that a user record exists in the public.users table
 * This can be called from client-side code after authentication
 */
export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client with the service role key to bypass RLS
    const supabaseAdmin = createAdminClient();

    // Get the request body
    const body = await request.json().catch(() => ({}));
    const { organizationName, userId, email, fullName } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log('Processing request for user ID:', userId, 'and organization:', organizationName);

    // Check if the user exists in the public.users table
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, org_id, role, full_name, email')
      .eq('id', userId)
      .single();

    if (!userError && userData) {
      console.log('User already exists in public.users table:', userData);
      // User already exists, return their data
      return NextResponse.json({
        exists: true,
        created: false,
        user: userData
      });
    }

    // User doesn't exist, so we need an organization name to create them
    if (!organizationName) {
      return NextResponse.json(
        { error: 'Organization name is required' },
        { status: 400 }
      );
    }

    console.log('User does not exist in public.users table, creating new records...');

    // Create a new organization
    console.log('Creating organization:', organizationName);
    const { data: orgData, error: orgError } = await supabaseAdmin
      .from('organizations')
      .insert([{
        name: organizationName,
      }])
      .select('id')
      .single();

    if (orgError) {
      console.error('Error creating organization:', orgError);
      return NextResponse.json(
        { error: `Failed to create organization: ${orgError.message}` },
        { status: 500 }
      );
    }

    console.log('Organization created with ID:', orgData.id);

    // Create a new user record
    console.log('Creating new user record...');
    const newUser = {
      id: userId,
      org_id: orgData.id,
      full_name: fullName || '',
      email: email || '',
      role: 'admin', // Default role for new users
    };

    console.log('New user data:', newUser);

    const { data: createdUser, error: createError } = await supabaseAdmin
      .from('users')
      .insert([newUser])
      .select('*')
      .single();

    if (createError) {
      console.error('Error creating user:', createError);
      return NextResponse.json(
        { error: `Failed to create user: ${createError.message}` },
        { status: 500 }
      );
    }

    console.log('User created successfully, creating user_organizations record');

    // Create user_organizations record for RLS policies
    const { error: userOrgError } = await supabaseAdmin
      .from('user_organizations')
      .insert([{
        user_id: userId,
        organization_id: orgData.id,
        role: 'admin'
      }]);

    if (userOrgError) {
      console.error('Error creating user_organizations record:', userOrgError);
      // Continue anyway - the user record was created
    } else {
      console.log('user_organizations record created successfully');
    }

    return NextResponse.json({
      exists: false,
      created: true,
      user: createdUser,
      organization: {
        id: orgData.id,
        name: organizationName
      }
    });
  } catch (error) {
    console.error('Error ensuring user exists:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
