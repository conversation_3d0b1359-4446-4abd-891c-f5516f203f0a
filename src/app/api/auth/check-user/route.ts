import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase/route-handler';

/**
 * GET /api/auth/check-user
 * Checks if the current user exists in the public.users table
 * If not, creates a new user record
 */
export async function GET() {
  try {
    const supabase = await createRouteHandlerClient();

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if the user exists in the public.users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, org_id, role')
      .eq('id', session.user.id)
      .single();

    if (userError) {
      // User doesn't exist - they need to complete registration
      return NextResponse.json(
        { error: 'User not found. Please complete registration.' },
        { status: 404 }
      );
    }

    // User exists
    return NextResponse.json({
      exists: true,
      created: false,
      user: userData
    });
  } catch (error) {
    console.error('Error checking user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
