import { NextRequest, NextResponse } from 'next/server';
import { VisitService } from '@/lib/visit';

/**
 * POST /api/visit/approval/respond
 * Respond to a visit approval request
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.visit_id || !body.approver_id || !body.status) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Validate status
    if (body.status !== 'approved' && body.status !== 'rejected') {
      return NextResponse.json(
        { error: 'Invalid status value. Must be "approved" or "rejected"' },
        { status: 400 }
      );
    }
    
    const visitService = new VisitService();
    const success = await visitService.respondToVisitApprovalRequest({
      visit_id: body.visit_id,
      approver_id: body.approver_id,
      status: body.status,
      notes: body.notes
    });
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to respond to visit approval request' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error responding to visit approval request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
