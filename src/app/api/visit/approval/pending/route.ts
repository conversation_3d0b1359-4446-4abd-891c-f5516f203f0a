import { NextRequest, NextResponse } from 'next/server';
import { createVisitService } from '@/lib/visit/server';

/**
 * GET /api/visit/approval/pending
 * Get pending approval requests for a user
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const userId = searchParams.get('user_id');
    if (!userId) {
      return NextResponse.json(
        { error: 'Missing user_id parameter' },
        { status: 400 }
      );
    }
    
    // Parse pagination parameters
    const limit = searchParams.has('limit') 
      ? parseInt(searchParams.get('limit') as string, 10) 
      : 10;
    
    const offset = searchParams.has('offset') 
      ? parseInt(searchParams.get('offset') as string, 10) 
      : 0;
    
    const visitService = await createVisitService();
    const pendingRequests = await visitService.getPendingApprovalRequests(userId, limit, offset);
    
    return NextResponse.json(pendingRequests);
  } catch (error) {
    console.error('Error getting pending approval requests:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
