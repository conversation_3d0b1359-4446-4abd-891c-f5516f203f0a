import { NextRequest, NextResponse } from 'next/server';
import { createVisitService } from '@/lib/visit/server';

/**
 * POST /api/visit/approval
 * Create a visit approval request
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.visit_id || !body.approver_id) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    const visitService = await createVisitService();
    const approval = await visitService.createVisitApprovalRequest({
      visit_id: body.visit_id,
      approver_id: body.approver_id,
      notes: body.notes
    });
    
    if (!approval) {
      return NextResponse.json(
        { error: 'Failed to create visit approval request' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(approval);
  } catch (error) {
    console.error('Error creating visit approval request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
