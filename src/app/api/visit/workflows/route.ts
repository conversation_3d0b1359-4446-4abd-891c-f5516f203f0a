import { NextRequest, NextResponse } from 'next/server';
import { VisitService } from '@/lib/visit';

/**
 * GET /api/visit/workflows
 * Get approval workflows for an organization
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const organizationId = searchParams.get('organization_id');
    if (!organizationId) {
      return NextResponse.json(
        { error: 'Missing organization_id parameter' },
        { status: 400 }
      );
    }
    
    const visitService = new VisitService();
    const workflows = await visitService.getApprovalWorkflows(organizationId);
    
    return NextResponse.json(workflows);
  } catch (error) {
    console.error('Error getting approval workflows:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
