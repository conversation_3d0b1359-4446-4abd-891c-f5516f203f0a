import { NextRequest, NextResponse } from 'next/server';
import { VisitService } from '@/lib/visit';

/**
 * POST /api/visit/workflow
 * Create an approval workflow
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.organization_id || !body.name) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    const visitService = new VisitService();
    const workflow = await visitService.createApprovalWorkflow(
      body.organization_id,
      body.name,
      body.description
    );
    
    if (!workflow) {
      return NextResponse.json(
        { error: 'Failed to create approval workflow' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(workflow);
  } catch (error) {
    console.error('Error creating approval workflow:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
