import { NextRequest, NextResponse } from 'next/server';
import { VisitService } from '@/lib/visit';

/**
 * POST /api/visit/workflow/step
 * Add a step to an approval workflow
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.workflow_id || !body.step_number || !body.approver_type || !body.approver_id) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Validate approver_type
    if (!['user', 'role', 'department'].includes(body.approver_type)) {
      return NextResponse.json(
        { error: 'Invalid approver_type. Must be "user", "role", or "department"' },
        { status: 400 }
      );
    }
    
    const visitService = new VisitService();
    const step = await visitService.addApprovalWorkflowStep(
      body.workflow_id,
      body.step_number,
      body.approver_type,
      body.approver_id,
      body.is_required !== undefined ? body.is_required : true
    );
    
    if (!step) {
      return NextResponse.json(
        { error: 'Failed to add approval workflow step' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(step);
  } catch (error) {
    console.error('Error adding approval workflow step:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
