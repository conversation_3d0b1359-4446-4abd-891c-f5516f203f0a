import { NextRequest, NextResponse } from 'next/server';
import { VisitService } from '@/lib/visit';
import { VisitStatus } from '@/types/visit';

/**
 * PATCH /api/visit/[id]/status
 * Update a visit request status
 */
export async function PATCH(
  request: NextRequest
) {
  try {
    // Extract ID from URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const id = pathSegments[pathSegments.length - 2]; // -2 because last segment is 'status'
    
    if (!id) {
      return NextResponse.json(
        { error: 'Missing visit ID' },
        { status: 400 }
      );
    }
    
    const body = await request.json();
    
    if (!body.status) {
      return NextResponse.json(
        { error: 'Missing status parameter' },
        { status: 400 }
      );
    }
    
    // Validate status
    const validStatuses: VisitStatus[] = [
      'pending', 'approved', 'rejected', 'checked_in', 'checked_out', 'cancelled', 'no_show'
    ];
    
    if (!validStatuses.includes(body.status)) {
      return NextResponse.json(
        { error: 'Invalid status value' },
        { status: 400 }
      );
    }
    
    const visitService = new VisitService();
    const success = await visitService.updateVisitStatus(id, body.status, body.notes);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update visit status' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating visit status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
