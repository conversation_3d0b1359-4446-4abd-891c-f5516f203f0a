import { NextRequest, NextResponse } from 'next/server';
import { VisitService } from '@/lib/visit';

/**
 * GET /api/visit/[id]/approvals
 * Get approvals for a visit
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Missing visit ID' },
        { status: 400 }
      );
    }
    
    const visitService = new VisitService();
    const approvals = await visitService.getVisitApprovals(id);
    
    return NextResponse.json(approvals);
  } catch (error) {
    console.error('Error getting visit approvals:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
