import { NextRequest, NextResponse } from 'next/server';
import { createVisitService } from '@/lib/visit/server';

/**
 * GET /api/visit/[id]/approvals
 * Get approvals for a visit
 */
export async function GET(
  request: NextRequest
) {
  try {
    // Extract ID from URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const id = pathSegments[pathSegments.length - 2]; // -2 because last segment is 'approvals'
    
    if (!id) {
      return NextResponse.json(
        { error: 'Missing visit ID' },
        { status: 400 }
      );
    }
    
    const visitService = await createVisitService();
    const approvals = await visitService.getVisitApprovals(id);
    
    return NextResponse.json(approvals);
  } catch (error) {
    console.error('Error getting visit approvals:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
