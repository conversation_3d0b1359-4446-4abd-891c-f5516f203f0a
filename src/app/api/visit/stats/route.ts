import { NextRequest, NextResponse } from 'next/server';
import { VisitService } from '@/lib/visit';

/**
 * GET /api/visit/stats
 * Get visit statistics for a host
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const hostId = searchParams.get('host_id');
    if (!hostId) {
      return NextResponse.json(
        { error: 'Missing host_id parameter' },
        { status: 400 }
      );
    }
    
    const visitService = new VisitService();
    const stats = await visitService.getVisitStats(hostId);
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error getting visit stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
