import { NextRequest, NextResponse } from 'next/server';
import { VisitService } from '@/lib/visit';
import { VisitRequestFilter, VisitStatus } from '@/types/visit';

/**
 * GET /api/visit
 * Get visit requests based on filters
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse filter parameters
    const filter: VisitRequestFilter = {};
    
    if (searchParams.has('host_id')) {
      filter.host_id = searchParams.get('host_id') as string;
    }
    
    if (searchParams.has('visitor_id')) {
      filter.visitor_id = searchParams.get('visitor_id') as string;
    }
    
    if (searchParams.has('location_id')) {
      filter.location_id = searchParams.get('location_id') as string;
    }
    
    // Handle status (can be single or multiple)
    const statusValues = searchParams.getAll('status');
    if (statusValues.length === 1) {
      filter.status = statusValues[0] as VisitStatus;
    } else if (statusValues.length > 1) {
      filter.status = statusValues as unknown as VisitStatus[];
    }
    
    if (searchParams.has('start_date')) {
      filter.start_date = searchParams.get('start_date') as string;
    }
    
    if (searchParams.has('end_date')) {
      filter.end_date = searchParams.get('end_date') as string;
    }
    
    if (searchParams.has('search')) {
      filter.search = searchParams.get('search') as string;
    }
    
    // Parse pagination parameters
    const limit = searchParams.has('limit') 
      ? parseInt(searchParams.get('limit') as string, 10) 
      : 10;
    
    const offset = searchParams.has('offset') 
      ? parseInt(searchParams.get('offset') as string, 10) 
      : 0;
    
    const visitService = new VisitService();
    const visits = await visitService.getVisitRequests(filter, limit, offset);
    
    return NextResponse.json(visits);
  } catch (error) {
    console.error('Error getting visit requests:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/visit
 * Create a new visit request
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.visitor_id || !body.host_id || !body.location_id || !body.purpose || !body.start_time) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // TODO: Implement visit creation logic
    // This would typically involve:
    // 1. Creating the visit record
    // 2. Handling approval workflow if required
    // 3. Sending notifications
    
    return NextResponse.json(
      { error: 'Not implemented yet' },
      { status: 501 }
    );
  } catch (error) {
    console.error('Error creating visit request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
