import { NextRequest, NextResponse } from 'next/server'
import { createAPIClient } from '@/lib/supabase/server'
import { stripe } from '@/lib/stripe/server'
import <PERSON><PERSON> from 'stripe'

export async function POST(request: NextRequest) {
  const body = await request.text()
  const signature = request.headers.get('stripe-signature') || ''

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error(`Webhook signature verification failed: ${errorMessage}`)
    return NextResponse.json(
      { error: 'Webhook signature verification failed' },
      { status: 400 }
    )
  }

  try {
    // Use the API client to avoid cookies() issues
    const supabase = createAPIClient()

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session

        // Get the metadata from the session
        const { orgId, userId, planId: metadataPlanId } = session.metadata || {}

        if (!orgId || !userId) {
          console.error('Missing metadata in checkout session')
          return NextResponse.json(
            { error: 'Missing metadata in checkout session' },
            { status: 400 }
          )
        }

        // For subscription mode checkouts
        if (session.mode === 'subscription' && session.subscription) {
          // Get the subscription details
          const subscription = await stripe.subscriptions.retrieve(
            session.subscription as string
          )

          // Process the subscription

          // Determine the plan ID based on our price mapping
          // This would require a reverse lookup from price ID to plan ID
          // For now, we'll use a simple approach
          const planId = metadataPlanId || 'basic' // Default to basic if not specified

          // Insert or update the subscription in the database
          const { error: subscriptionError } = await supabase
            .from('subscriptions')
            .upsert({
              org_id: orgId,
              stripe_subscription_id: subscription.id,
              stripe_customer_id: session.customer as string,
              plan_id: planId,
              status: subscription.status,
              current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            })

          if (subscriptionError) {
            console.error('Error updating subscription:', subscriptionError)
            return NextResponse.json(
              { error: 'Error updating subscription' },
              { status: 500 }
            )
          }
        }

        // For setup mode checkouts (enterprise plan)
        if (session.mode === 'setup' && metadataPlanId === 'enterprise') {
          // For enterprise plans, we'll create a placeholder subscription
          // that will be updated when the actual subscription is created
          const { error: subscriptionError } = await supabase
            .from('subscriptions')
            .upsert({
              org_id: orgId,
              stripe_subscription_id: 'pending_enterprise',
              stripe_customer_id: session.customer as string,
              plan_id: 'enterprise',
              status: 'pending',
              current_period_end: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000 // 30 days from now
              ).toISOString(),
            })

          if (subscriptionError) {
            console.error('Error creating enterprise subscription:', subscriptionError)
            return NextResponse.json(
              { error: 'Error creating enterprise subscription' },
              { status: 500 }
            )
          }
        }

        break
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription

        // Update the subscription in the database
        const { error: subscriptionError } = await supabase
          .from('subscriptions')
          .update({
            status: subscription.status,
            current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
          })
          .eq('stripe_subscription_id', subscription.id)

        if (subscriptionError) {
          console.error('Error updating subscription:', subscriptionError)
          return NextResponse.json(
            { error: 'Error updating subscription' },
            { status: 500 }
          )
        }

        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription

        // Update the subscription status in the database
        const { error: subscriptionError } = await supabase
          .from('subscriptions')
          .update({
            status: 'canceled',
          })
          .eq('stripe_subscription_id', subscription.id)

        if (subscriptionError) {
          console.error('Error updating subscription:', subscriptionError)
          return NextResponse.json(
            { error: 'Error updating subscription' },
            { status: 500 }
          )
        }

        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return NextResponse.json(
      { error: 'Error processing webhook' },
      { status: 500 }
    )
  }
}

// Disable body parsing, we need the raw body for webhook signature verification
export const config = {
  api: {
    bodyParser: false,
  },
}
