import { NextRequest, NextResponse } from 'next/server'
import { createAPIClient } from '@/lib/supabase/server'
import { createBillingPortalSession } from '@/lib/stripe/server'

export async function POST(request: NextRequest) {
  try {
    // Extract the authorization header from the request
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'No authorization header provided' },
        { status: 401 }
      )
    }

    // The header should be in the format 'Bearer <token>'
    const token = authHeader.split(' ')[1]
    if (!token) {
      return NextResponse.json(
        { error: 'Invalid authorization header format' },
        { status: 401 }
      )
    }

    // Use the API client to avoid cookies() issues
    const supabase = createAPIClient()

    // Get the current user from the token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get the request body
    const { customerId } = await request.json()

    if (!customerId) {
      return NextResponse.json(
        { error: 'Missing customer ID' },
        { status: 400 }
      )
    }

    // Set up return URL
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const returnUrl = `${baseUrl}/dashboard`

    // Create a billing portal session
    const session = await createBillingPortalSession({
      customerId,
      returnUrl,
    })

    // Return the portal URL
    return NextResponse.json({ url: session.url })
  } catch (error) {
    console.error('Stripe billing portal error:', error)
    return NextResponse.json(
      { error: 'Failed to create billing portal session' },
      { status: 500 }
    )
  }
}
