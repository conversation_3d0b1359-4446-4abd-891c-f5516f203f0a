import { NextRequest, NextResponse } from 'next/server'
import { createAPIClient } from '@/lib/supabase/server'
import { createCustomer, createCheckoutSession, createEnterpriseCheckoutSession } from '@/lib/stripe/server'
import { STRIPE_PRICE_IDS } from '@/lib/stripe/plans'

export async function POST(request: NextRequest) {
  try {
    // Extract the authorization header from the request
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'No authorization header provided' },
        { status: 401 }
      )
    }

    // The header should be in the format 'Bearer <token>'
    const token = authHeader.split(' ')[1]
    if (!token) {
      return NextResponse.json(
        { error: 'Invalid authorization header format' },
        { status: 401 }
      )
    }

    // Use the API client to avoid cookies() issues
    const supabase = createAPIClient()

    // Get the current user from the token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get the request body
    const { planId, orgId } = await request.json()

    if (!planId || !orgId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Get user details from the database
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()

    if (userError || !userData) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get organization details from the database
    const { data: orgData, error: orgError } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', orgId)
      .single()

    if (orgError || !orgData) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      )
    }

    // Create a Stripe customer if one doesn't exist
    let customerId = ''

    // Check if the organization already has a Stripe customer ID
    const { data: subscriptionData } = await supabase
      .from('subscriptions')
      .select('stripe_customer_id')
      .eq('org_id', orgId)
      .single()

    if (subscriptionData?.stripe_customer_id) {
      customerId = subscriptionData.stripe_customer_id
    } else {
      // Create a new Stripe customer
      const customer = await createCustomer({
        email: userData.email,
        name: orgData.name,
        orgId: orgId,
      })

      customerId = customer.id
    }

    // Set up success and cancel URLs
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const successUrl = `${baseUrl}/dashboard?checkout_success=true`
    const cancelUrl = `${baseUrl}/signup?checkout_canceled=true`

    // Create a checkout session based on the plan
    let session

    if (planId === 'enterprise') {
      // For enterprise plans, create a custom checkout session
      session = await createEnterpriseCheckoutSession({
        customerId,
        orgId,
        userId: user.id,
        successUrl,
        cancelUrl,
      })
    } else {
      // For standard plans, get the price ID and create a checkout session
      const priceId = STRIPE_PRICE_IDS[planId]

      if (!priceId) {
        return NextResponse.json(
          { error: 'Invalid plan ID' },
          { status: 400 }
        )
      }

      // Validate that we have a proper price ID (not the placeholder)
      if (priceId.includes('placeholder') || !priceId.startsWith('price_')) {
        console.error('Invalid Stripe price ID:', priceId);
        console.error('Environment variables:', {
          NEXT_PUBLIC_STRIPE_PRICE_BASIC: process.env.NEXT_PUBLIC_STRIPE_PRICE_BASIC,
          NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL: process.env.NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL,
          NEXT_PUBLIC_STRIPE_PRICE_BUSINESS: process.env.NEXT_PUBLIC_STRIPE_PRICE_BUSINESS,
        });

        return NextResponse.json({
          error: 'Stripe price ID not properly configured',
          details: `Please set up Stripe price IDs in your environment variables or update the plans.ts file. Current price ID: ${priceId}. See docs/stripe-setup.md for instructions.`
        }, { status: 500 })
      }

      session = await createCheckoutSession({
        customerId,
        priceId,
        orgId,
        userId: user.id,
        successUrl,
        cancelUrl,
      })
    }

    // Return the checkout URL
    return NextResponse.json({ url: session.url })
  } catch (error) {
    console.error('Stripe checkout error:', error)

    // Check if it's a Stripe error about using a literal price instead of a price ID
    if (error instanceof Error &&
        error.message.includes('price parameter should be the ID of a price object')) {
      return NextResponse.json({
        error: 'Invalid Stripe price ID',
        details: 'You need to use a Stripe price ID (e.g., price_1234567890) instead of a literal price. See docs/stripe-setup.md for instructions on setting up Stripe price IDs.'
      }, { status: 400 })
    }

    // Check if it's a Stripe API error
    if (error instanceof Error && error.message.includes('Stripe API Error')) {
      console.error('Stripe API Error details:', error)
      return NextResponse.json({
        error: 'Stripe API Error',
        details: `There was an error communicating with Stripe: ${error.message}. Please check your Stripe configuration.`
      }, { status: 500 })
    }

    // Log environment variables for debugging
    console.error('Environment variables during error:', {
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ? 'Set' : 'Not set',
      STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY ? 'Set' : 'Not set',
      NEXT_PUBLIC_STRIPE_PRICE_BASIC: process.env.NEXT_PUBLIC_STRIPE_PRICE_BASIC,
      NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL: process.env.NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL,
      NEXT_PUBLIC_STRIPE_PRICE_BUSINESS: process.env.NEXT_PUBLIC_STRIPE_PRICE_BUSINESS,
    })

    return NextResponse.json(
      { error: 'Failed to create checkout session', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
