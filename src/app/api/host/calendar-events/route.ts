import { NextRequest, NextResponse } from 'next/server';
import { createServerHostService } from '@/lib/host/server';

/**
 * GET /api/host/calendar-events
 * Get calendar events for a host
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (startDateParam) {
      startDate = new Date(startDateParam);
      if (isNaN(startDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid startDate parameter' },
          { status: 400 }
        );
      }
    }

    if (endDateParam) {
      endDate = new Date(endDateParam);
      if (isNaN(endDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid endDate parameter' },
          { status: 400 }
        );
      }
    }

    const hostService = await createServerHostService();
    const events = await hostService.getCalendarEvents(userId, startDate, endDate);

    return NextResponse.json(events);
  } catch (error) {
    console.error('Error getting calendar events:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
