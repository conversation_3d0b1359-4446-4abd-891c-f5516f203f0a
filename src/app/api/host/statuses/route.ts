import { NextRequest, NextResponse } from 'next/server';
import { createServerHostService } from '@/lib/host/server';

/**
 * POST /api/host/statuses
 * Get the statuses of multiple hosts
 */
export async function POST(request: NextRequest) {
  try {
    const { userIds } = await request.json();

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { error: 'Missing or invalid userIds parameter' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const statuses = await hostService.getMultipleHostStatuses(userIds);

    return NextResponse.json(statuses);
  } catch (error) {
    console.error('Error getting multiple host statuses:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
