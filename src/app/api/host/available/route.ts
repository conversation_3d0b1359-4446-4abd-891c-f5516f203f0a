import { NextRequest, NextResponse } from 'next/server';
import { createServerHostService } from '@/lib/host/server';

/**
 * GET /api/host/available
 * Check if a host is available
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const available = await hostService.isHostAvailable(userId);

    return NextResponse.json({ available });
  } catch (error) {
    console.error('Error checking host availability:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
