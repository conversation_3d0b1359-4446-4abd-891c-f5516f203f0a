import { NextRequest, NextResponse } from 'next/server';
import { createServerHostService } from '@/lib/host/server';
import { HostStatus, HostStatusDuration } from '@/types/host';

/**
 * POST /api/host/status
 * Set the status of a host
 */
export async function POST(request: NextRequest) {
  try {
    const { userId, status, message, duration } = await request.json();

    if (!userId || !status) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const result = await hostService.setHostStatus(
      userId,
      status as HostStatus,
      message,
      duration as HostStatusDuration
    );

    if (!result) {
      return NextResponse.json(
        { error: 'Failed to set host status' },
        { status: 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error setting host status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/host/status
 * Clear the status of a host
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const success = await hostService.clearHostStatus(userId);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to clear host status' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error clearing host status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/host/status
 * Get the status of a host
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const status = await hostService.getHostStatus(userId);

    return NextResponse.json(status);
  } catch (error) {
    console.error('Error getting host status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
