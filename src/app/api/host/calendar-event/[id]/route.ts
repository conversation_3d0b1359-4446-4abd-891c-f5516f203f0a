import { NextRequest, NextResponse } from 'next/server';
import { createServerHostService } from '@/lib/host/server';
import { CalendarEvent } from '@/types/host';

type Params = { id: string }

/**
 * PATCH /api/host/calendar-event/[id]
 * Update a calendar event
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const eventId = params.id;
    const updates = await request.json();

    if (!eventId) {
      return NextResponse.json(
        { error: 'Missing event ID' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const result = await hostService.updateCalendarEvent(
      eventId,
      updates as Partial<Pick<CalendarEvent, 'title' | 'description' | 'location' | 'start_time' | 'end_time' | 'is_all_day' | 'status'>>
    );

    if (!result) {
      return NextResponse.json(
        { error: 'Failed to update calendar event' },
        { status: 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating calendar event:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/host/calendar-event/[id]
 * Delete a calendar event
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const eventId = params.id;

    if (!eventId) {
      return NextResponse.json(
        { error: 'Missing event ID' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const success = await hostService.deleteCalendarEvent(eventId);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete calendar event' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting calendar event:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
