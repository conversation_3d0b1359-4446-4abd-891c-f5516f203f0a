import { NextRequest, NextResponse } from 'next/server';
import { createServerHostService } from '@/lib/host/server';
import { CalendarEvent } from '@/types/host';

/**
 * POST /api/host/calendar-event
 * Add a calendar event
 */
export async function POST(request: NextRequest) {
  try {
    const eventData = await request.json();

    if (!eventData.user_id || !eventData.calendar_integration_id || !eventData.external_event_id || !eventData.title || !eventData.start_time || !eventData.end_time) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const result = await hostService.addCalendarEvent(
      eventData as Omit<CalendarEvent, 'id' | 'created_at' | 'updated_at'>
    );

    if (!result) {
      return NextResponse.json(
        { error: 'Failed to add calendar event' },
        { status: 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error adding calendar event:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
