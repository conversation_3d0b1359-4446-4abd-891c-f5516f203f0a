import { NextRequest, NextResponse } from 'next/server';
import { createServerHostService } from '@/lib/host/server';
import { CalendarIntegration } from '@/types/host';

/**
 * PATCH /api/host/calendar-integration/[id]
 * Update a calendar integration
 */
export async function PATCH(
  request: NextRequest
) {
  try {
    // Extract ID from URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const integrationId = pathSegments[pathSegments.length - 1];
    const updates = await request.json();

    if (!integrationId) {
      return NextResponse.json(
        { error: 'Missing integration ID' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const result = await hostService.updateCalendarIntegration(
      integrationId,
      updates as Partial<Pick<CalendarIntegration, 'is_active' | 'sync_frequency'>>
    );

    if (!result) {
      return NextResponse.json(
        { error: 'Failed to update calendar integration' },
        { status: 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating calendar integration:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/host/calendar-integration/[id]
 * Delete a calendar integration
 */
export async function DELETE(
  request: NextRequest
) {
  try {
    // Extract ID from URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const integrationId = pathSegments[pathSegments.length - 1];

    if (!integrationId) {
      return NextResponse.json(
        { error: 'Missing integration ID' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const success = await hostService.deleteCalendarIntegration(integrationId);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete calendar integration' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting calendar integration:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
