import { NextRequest, NextResponse } from 'next/server';
import { createServerHostService } from '@/lib/host/server';
import { CalendarIntegration } from '@/types/host';

/**
 * POST /api/host/calendar-integration
 * Add a calendar integration for a host
 */
export async function POST(request: NextRequest) {
  try {
    const { userId, provider, syncFrequency } = await request.json();

    if (!userId || !provider) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const result = await hostService.addCalendarIntegration(
      userId,
      provider as CalendarIntegration['provider'],
      syncFrequency as CalendarIntegration['sync_frequency']
    );

    if (!result) {
      return NextResponse.json(
        { error: 'Failed to add calendar integration' },
        { status: 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error adding calendar integration:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/host/calendar-integration
 * Get calendar integrations for a host
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    const hostService = await createServerHostService();
    const integrations = await hostService.getCalendarIntegrations(userId);

    return NextResponse.json(integrations);
  } catch (error) {
    console.error('Error getting calendar integrations:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
