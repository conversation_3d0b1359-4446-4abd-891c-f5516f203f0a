'use client'

import { useState, useEffect } from 'react'
import { LocationHierarchy } from '@/types/admin'
import { getLocationHierarchy } from '@/lib/admin/client'
import { ChevronDown, ChevronRight, Building, MapPin } from 'lucide-react'

interface LocationSelectorProps {
  orgId: string
  selectedLocationId?: string
  onLocationSelect: (locationId: string) => void
  className?: string
  filterLocations?: (locations: LocationHierarchy[]) => LocationHierarchy[]
}

export default function LocationSelector({
  orgId,
  selectedLocationId,
  onLocationSelect,
  className = '',
  filterLocations
}: LocationSelectorProps) {
  const [locations, setLocations] = useState<LocationHierarchy[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedLocations, setExpandedLocations] = useState<Record<string, boolean>>({})
  
  // Fetch location hierarchy
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        let locationHierarchy = await getLocationHierarchy(orgId)
        
        // Apply filtering if provided
        if (filterLocations) {
          locationHierarchy = filterLocations(locationHierarchy)
        }
        
        setLocations(locationHierarchy)
        setLoading(false)
      } catch (err) {
        console.error('Error fetching locations:', err)
        setError('Failed to load locations')
        setLoading(false)
      }
    }
    
    fetchLocations()
  }, [orgId, filterLocations])
  
  const toggleExpand = (locationId: string) => {
    setExpandedLocations(prev => ({
      ...prev,
      [locationId]: !prev[locationId]
    }))
  }
  
  const renderLocationTree = (locations: LocationHierarchy[], level = 0) => {
    return locations.map(location => {
      const hasChildren = location.children && location.children.length > 0
      const isExpanded = expandedLocations[location.id] || false
      const isSelected = selectedLocationId === location.id
      
      return (
        <div key={location.id} className="location-tree-item">
          <div 
            className={`
              flex items-center py-2 px-2 rounded-md cursor-pointer
              ${isSelected ? 'bg-blue-100 text-blue-800' : 'hover:bg-gray-100'}
              ${level > 0 ? 'ml-' + (level * 4) : ''}
            `}
            onClick={() => onLocationSelect(location.id)}
          >
            {hasChildren ? (
              <button 
                className="mr-1 p-1 rounded-full hover:bg-gray-200 focus:outline-none"
                onClick={(e) => {
                  e.stopPropagation()
                  toggleExpand(location.id)
                }}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-gray-500" />
                )}
              </button>
            ) : (
              <span className="w-6"></span>
            )}
            
            {location.location_type === 'headquarters' ? (
              <Building className="h-4 w-4 mr-2 text-blue-600" />
            ) : (
              <MapPin className="h-4 w-4 mr-2 text-gray-600" />
            )}
            
            <span className={`text-sm ${isSelected ? 'font-medium' : ''}`}>
              {location.name}
            </span>
            
            {location.group && (
              <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-gray-200 text-gray-700">
                {location.group.name}
              </span>
            )}
          </div>
          
          {hasChildren && isExpanded && (
            <div className="location-children">
              {renderLocationTree(location.children || [], level + 1)}
            </div>
          )}
        </div>
      )
    })
  }
  
  if (loading) {
    return (
      <div className={`p-4 border rounded-md ${className}`}>
        <div className="animate-pulse flex space-x-4">
          <div className="flex-1 space-y-4 py-1">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className={`p-4 border rounded-md bg-red-50 text-red-700 ${className}`}>
        {error}
      </div>
    )
  }
  
  return (
    <div className={`border rounded-md overflow-hidden ${className}`}>
      <div className="p-3 bg-gray-50 border-b">
        <h3 className="font-medium text-gray-700">Select Location</h3>
      </div>
      <div className="p-2 max-h-80 overflow-y-auto">
        {locations.length > 0 ? (
          renderLocationTree(locations)
        ) : (
          <div className="p-4 text-center text-gray-500">
            No locations found
          </div>
        )}
      </div>
    </div>
  )
}
