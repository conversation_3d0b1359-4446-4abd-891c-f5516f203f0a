'use client'

import { useState, useEffect } from 'react'
import { Location } from '@/types/admin'
import { getLocations } from '@/lib/admin/client'
import LocationSelector from './LocationSelector'
import { Building, MapPin } from 'lucide-react'

interface LocationBasedDashboardProps {
  orgId: string
  children: React.ReactNode
  onLocationChange?: (locationId: string | null) => void
  initialLocationId?: string
  showAllOption?: boolean
}

export default function LocationBasedDashboard({
  orgId,
  children,
  onLocationChange,
  initialLocationId,
  showAllOption = true
}: LocationBasedDashboardProps) {
  const [selectedLocationId, setSelectedLocationId] = useState<string | null>(initialLocationId || null)
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  const [showSelector, setShowSelector] = useState(false)
  const [locations, setLocations] = useState<Location[]>([])
  const [loading, setLoading] = useState(true)
  
  // Fetch locations
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const locationsData = await getLocations(orgId)
        setLocations(locationsData)
        
        // If initialLocationId is provided, find the location
        if (initialLocationId) {
          const location = locationsData.find(loc => loc.id === initialLocationId)
          if (location) {
            setSelectedLocation(location)
          }
        }
        
        setLoading(false)
      } catch (err) {
        console.error('Error fetching locations:', err)
        setLoading(false)
      }
    }
    
    fetchLocations()
  }, [orgId, initialLocationId])
  
  // Update selected location when selectedLocationId changes
  useEffect(() => {
    if (selectedLocationId) {
      const location = locations.find(loc => loc.id === selectedLocationId)
      setSelectedLocation(location || null)
    } else {
      setSelectedLocation(null)
    }
    
    // Notify parent component of location change
    if (onLocationChange) {
      onLocationChange(selectedLocationId)
    }
  }, [selectedLocationId, locations, onLocationChange])
  
  const handleLocationSelect = (locationId: string) => {
    setSelectedLocationId(locationId)
    setShowSelector(false)
  }
  
  const handleSelectAll = () => {
    setSelectedLocationId(null)
    setShowSelector(false)
  }
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative">
          <button
            onClick={() => setShowSelector(!showSelector)}
            className="flex items-center space-x-2 px-4 py-2 border rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {selectedLocation ? (
              <>
                {selectedLocation.location_type === 'headquarters' ? (
                  <Building className="h-4 w-4 text-blue-600" />
                ) : (
                  <MapPin className="h-4 w-4 text-gray-600" />
                )}
                <span>{selectedLocation.name}</span>
              </>
            ) : (
              <>
                <Building className="h-4 w-4 text-gray-600" />
                <span>All Locations</span>
              </>
            )}
          </button>
          
          {showSelector && (
            <div className="absolute z-10 mt-1 w-64">
              <LocationSelector
                orgId={orgId}
                selectedLocationId={selectedLocationId || undefined}
                onLocationSelect={handleLocationSelect}
              />
              
              {showAllOption && (
                <button
                  onClick={handleSelectAll}
                  className="mt-2 w-full flex items-center space-x-2 px-4 py-2 border rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <Building className="h-4 w-4 text-gray-600" />
                  <span>All Locations</span>
                </button>
              )}
            </div>
          )}
        </div>
        
        {selectedLocation && (
          <div className="text-sm text-gray-500">
            {selectedLocation.location_type === 'headquarters' ? 'Headquarters' : 
             selectedLocation.location_type === 'branch' ? 'Branch Office' :
             selectedLocation.location_type === 'facility' ? 'Facility' :
             selectedLocation.location_type === 'office' ? 'Office' : 'Location'}
            
            {selectedLocation.city && (
              <span className="ml-2">
                • {selectedLocation.city}
                {selectedLocation.country && `, ${selectedLocation.country}`}
              </span>
            )}
          </div>
        )}
      </div>
      
      <div className="location-based-content">
        {loading ? (
          <div className="flex justify-center items-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <span className="ml-2">Loading location data...</span>
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  )
}
