"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { HostStatusHistory } from '@/types/host';
import { createClientHostService } from '@/lib/host/client';

interface HostStatusHistoryProps {
  userId: string;
  limit?: number;
  className?: string;
}

/**
 * Component that displays the status history of a host
 */
export function HostStatusHistoryList({
  userId,
  limit = 10,
  className = '',
}: HostStatusHistoryProps) {
  const [history, setHistory] = useState<HostStatusHistory[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(true);

  // Use useMemo to ensure hostService is only created once
  const hostService = React.useMemo(() => createClientHostService(), []);

  const loadHistory = useCallback(async (pageNum: number) => {
    setLoading(true);
    setError(null);

    try {
      const offset = pageNum * limit;
      const historyItems = await hostService.getHostStatusHistory(userId, limit, offset);

      if (pageNum === 0) {
        setHistory(historyItems);
      } else {
        setHistory(prev => [...prev, ...historyItems]);
      }

      setHasMore(historyItems.length === limit);
    } catch (err) {
      console.error('Error loading host status history:', err);
      setError('Failed to load status history. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [userId, limit, hostService]);

  useEffect(() => {
    loadHistory(0);
  }, [userId, loadHistory]);

  const handleLoadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    loadHistory(nextPage);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'available':
        return 'Available';
      case 'busy':
        return 'Busy';
      case 'out_of_office':
        return 'Out of Office';
      case 'in_meeting':
        return 'In a Meeting';
      case 'do_not_disturb':
        return 'Do Not Disturb';
      default:
        return status;
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'busy':
        return 'bg-yellow-100 text-yellow-800';
      case 'out_of_office':
        return 'bg-gray-100 text-gray-800';
      case 'in_meeting':
        return 'bg-red-100 text-red-800';
      case 'do_not_disturb':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get duration text
  const getDurationText = (item: HostStatusHistory) => {
    const { duration } = item;

    switch (duration.type) {
      case 'indefinite':
        return 'Indefinite';
      case 'until_time':
        return duration.endTime ? `Until ${formatDate(duration.endTime)}` : 'Indefinite';
      case 'all_day':
        return duration.endDate ? `All day on ${duration.endDate}` : 'Indefinite';
      default:
        return 'Indefinite';
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-4 border-b">
        <h3 className="text-lg font-medium">Status History</h3>
      </div>

      {error && (
        <div className="p-4 bg-red-100 text-red-700">
          {error}
        </div>
      )}

      {history.length === 0 && !loading ? (
        <div className="p-4 text-center text-gray-500">
          No status history found.
        </div>
      ) : (
        <ul className="divide-y">
          {history.map((item) => (
            <li key={item.id} className="p-4">
              <div className="flex items-center justify-between mb-1">
                <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(item.status)}`}>
                  {getStatusLabel(item.status)}
                </span>
                <span className="text-sm text-gray-500">
                  {formatDate(item.created_at)}
                </span>
              </div>

              {item.message && (
                <p className="text-sm text-gray-700 mb-1">{item.message}</p>
              )}

              <div className="flex justify-between text-xs text-gray-500">
                <span>{getDurationText(item)}</span>
                <span>Ended: {formatDate(item.ended_at)}</span>
              </div>
            </li>
          ))}
        </ul>
      )}

      {loading && (
        <div className="p-4 text-center">
          <div className="inline-block h-6 w-6 animate-spin rounded-full border-2 border-solid border-blue-600 border-r-transparent"></div>
          <span className="ml-2">Loading...</span>
        </div>
      )}

      {hasMore && !loading && (
        <div className="p-4 text-center">
          <button
            onClick={handleLoadMore}
            className="px-4 py-2 bg-gray-100 text-gray-800 rounded hover:bg-gray-200"
          >
            Load More
          </button>
        </div>
      )}
    </div>
  );
}
