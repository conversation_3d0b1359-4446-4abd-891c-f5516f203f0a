"use client";

import React from 'react';
import { HostStatusData } from '@/types/host';

interface HostStatusIndicatorProps {
  status: HostStatusData | null;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

/**
 * Component that displays the current status of a host
 */
export function HostStatusIndicator({
  status,
  size = 'md',
  showLabel = true,
  className = '',
}: HostStatusIndicatorProps) {
  // If no status is set, default to available
  const currentStatus = status?.status || 'available';

  // Get the appropriate color for the status
  const getStatusColor = () => {
    switch (currentStatus) {
      case 'available':
        return 'bg-green-500';
      case 'busy':
        return 'bg-yellow-500';
      case 'out_of_office':
        return 'bg-gray-500';
      case 'in_meeting':
        return 'bg-red-500';
      case 'do_not_disturb':
        return 'bg-red-600';
      default:
        return 'bg-green-500';
    }
  };

  // Get the appropriate size for the indicator
  const getIndicatorSize = () => {
    switch (size) {
      case 'sm':
        return 'w-2 h-2';
      case 'md':
        return 'w-3 h-3';
      case 'lg':
        return 'w-4 h-4';
      default:
        return 'w-3 h-3';
    }
  };

  // Get the label for the status
  const getStatusLabel = () => {
    switch (currentStatus) {
      case 'available':
        return 'Available';
      case 'busy':
        return 'Busy';
      case 'out_of_office':
        return 'Out of Office';
      case 'in_meeting':
        return 'In a Meeting';
      case 'do_not_disturb':
        return 'Do Not Disturb';
      default:
        return 'Available';
    }
  };

  // Get the appropriate text size for the label
  const getLabelSize = () => {
    switch (size) {
      case 'sm':
        return 'text-xs';
      case 'md':
        return 'text-sm';
      case 'lg':
        return 'text-base';
      default:
        return 'text-sm';
    }
  };

  return (
    <div className={`flex items-center ${className}`}>
      <div
        className={`${getIndicatorSize()} ${getStatusColor()} rounded-full mr-2`}
        title={getStatusLabel()}
      />
      {showLabel && (
        <span className={`${getLabelSize()} font-medium`}>
          {getStatusLabel()}
          {status?.message && (
            <span className="ml-1 text-gray-500">({status.message})</span>
          )}
        </span>
      )}
    </div>
  );
}
