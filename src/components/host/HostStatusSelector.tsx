"use client";

import React, { useState } from 'react';
import { HostStatus, HostStatusDuration } from '@/types/host';
import { createClientHostService } from '@/lib/host/client';

interface HostStatusSelectorProps {
  userId: string;
  onStatusChange?: () => void;
  className?: string;
}

/**
 * Component that allows a host to set their status
 */
export function HostStatusSelector({
  userId,
  onStatusChange,
  className = '',
}: HostStatusSelectorProps) {
  const [status, setStatus] = useState<HostStatus>('available');
  const [message, setMessage] = useState<string>('');
  const [durationType, setDurationType] = useState<HostStatusDuration['type']>('indefinite');
  const [endTime, setEndTime] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Use useMemo to ensure hostService is only created once
  const hostService = React.useMemo(() => createClientHostService(), []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const duration: HostStatusDuration = {
        type: durationType,
      };

      if (durationType === 'until_time' && endTime) {
        duration.endTime = endTime;
      }

      if (durationType === 'all_day' && endDate) {
        duration.endDate = endDate;
      }

      await hostService.setHostStatus(userId, status, message || undefined, duration);

      if (onStatusChange) {
        onStatusChange();
      }
    } catch (err) {
      console.error('Error setting host status:', err);
      setError('Failed to set status. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClearStatus = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      await hostService.clearHostStatus(userId);

      // Reset form
      setStatus('available');
      setMessage('');
      setDurationType('indefinite');
      setEndTime('');
      setEndDate('');

      if (onStatusChange) {
        onStatusChange();
      }
    } catch (err) {
      console.error('Error clearing host status:', err);
      setError('Failed to clear status. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={`p-4 bg-white rounded-lg shadow ${className}`}>
      <h3 className="text-lg font-medium mb-4">Set Your Status</h3>

      {error && (
        <div className="mb-4 p-2 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            Status
          </label>
          <select
            value={status}
            onChange={(e) => setStatus(e.target.value as HostStatus)}
            className="w-full p-2 border rounded"
            disabled={isSubmitting}
          >
            <option value="available">Available</option>
            <option value="busy">Busy</option>
            <option value="out_of_office">Out of Office</option>
            <option value="in_meeting">In a Meeting</option>
            <option value="do_not_disturb">Do Not Disturb</option>
          </select>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            Message (Optional)
          </label>
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Add a status message"
            className="w-full p-2 border rounded"
            disabled={isSubmitting}
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            Duration
          </label>
          <select
            value={durationType}
            onChange={(e) => setDurationType(e.target.value as HostStatusDuration['type'])}
            className="w-full p-2 border rounded"
            disabled={isSubmitting}
          >
            <option value="indefinite">Indefinite</option>
            <option value="until_time">Until a specific time</option>
            <option value="all_day">All day</option>
          </select>
        </div>

        {durationType === 'until_time' && (
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">
              Until
            </label>
            <input
              type="datetime-local"
              value={endTime}
              onChange={(e) => setEndTime(e.target.value)}
              className="w-full p-2 border rounded"
              disabled={isSubmitting}
              required
            />
          </div>
        )}

        {durationType === 'all_day' && (
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">
              Date
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full p-2 border rounded"
              disabled={isSubmitting}
              required
            />
          </div>
        )}

        <div className="flex space-x-2">
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Set Status'}
          </button>

          <button
            type="button"
            onClick={handleClearStatus}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 disabled:opacity-50"
            disabled={isSubmitting}
          >
            Clear Status
          </button>
        </div>
      </form>
    </div>
  );
}
