'use client'

import { useState, useEffect } from 'react'
import { VisitStatus, Visit, Visitor } from '@/types'
import { createClient } from '@/lib/supabase/client'
import Image from 'next/image'

interface VisitorCheckOutFormProps {
  visitId: string
}

export default function VisitorCheckOutForm({ visitId }: VisitorCheckOutFormProps) {
  const supabase = createClient()
  
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [visit, setVisit] = useState<Visit | null>(null)
  const [visitor, setVisitor] = useState<Visitor | null>(null)
  const [host, setHost] = useState<{ full_name: string } | null>(null)
  const [location, setLocation] = useState<{ name: string } | null>(null)
  const [checkInDuration, setCheckInDuration] = useState<string>('')
  
  // Fetch visit data
  const fetchVisitData = async () => {
    try {
      // Fetch visit data
      const { data: visitData, error: visitError } = await supabase
        .from('visits')
        .select('*')
        .eq('id', visitId)
        .single()

      if (visitError) throw visitError
      setVisit(visitData as Visit)
      
      // Calculate check-in duration
      if (visitData.check_in_time) {
        const checkInTime = new Date(visitData.check_in_time)
        const now = new Date()
        const durationMs = now.getTime() - checkInTime.getTime()
        
        // Format duration
        const hours = Math.floor(durationMs / (1000 * 60 * 60))
        const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60))
        setCheckInDuration(`${hours}h ${minutes}m`)
      }

      // Fetch visitor data
      const { data: visitorData, error: visitorError } = await supabase
        .from('visitors')
        .select('*')
        .eq('id', visitData.visitor_id)
        .single()

      if (visitorError) throw visitorError
      setVisitor(visitorData as Visitor)
      
      // Fetch host data
      const { data: hostData, error: hostError } = await supabase
        .from('users')
        .select('full_name')
        .eq('id', visitData.host_id)
        .single()

      if (hostError) throw hostError
      setHost(hostData)
      
      // Fetch location data
      const { data: locationData, error: locationError } = await supabase
        .from('locations')
        .select('name')
        .eq('id', visitData.location_id)
        .single()

      if (locationError) throw locationError
      setLocation(locationData)
    } catch (error) {
      console.error('Error fetching visit data:', error)
      setError('Failed to load visit data. Please try again.')
    }
  }

  // Load data when component mounts
  useEffect(() => {
    fetchVisitData()
  }, [])

  const handleCheckOut = async () => {
    setLoading(true)
    setError(null)
    
    try {
      if (!visit) {
        throw new Error('Visit data not found')
      }
      
      // Update visit status to checked out
      const { error: updateError } = await supabase
        .from('visits')
        .update({
          status: VisitStatus.CHECKED_OUT,
          check_out_time: new Date().toISOString(),
        })
        .eq('id', visitId)

      if (updateError) throw updateError
      
      setSuccess(true)
    } catch (error: unknown) {
      console.error('Error checking out visitor:', error)
      setError(error instanceof Error ? error.message : 'Failed to check out visitor. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (!visit || !visitor) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-500">
            {error || 'Loading visit data...'}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Check Out Visitor</h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
          Visitor successfully checked out!
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="md:col-span-1">
          <div className="aspect-square bg-gray-100 rounded-md overflow-hidden relative">
            {visitor.photo_url ? (
              <Image
                src={visitor.photo_url}
                alt={visitor.full_name}
                fill
                style={{ objectFit: 'cover' }}
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-400">
                No photo
              </div>
            )}
          </div>
        </div>
        
        <div className="md:col-span-2 space-y-4">
          <div>
            <h3 className="text-xl font-semibold">{visitor.full_name}</h3>
            <p className="text-gray-600">{visitor.company}</p>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Email</p>
              <p>{visitor.email}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-500">Phone</p>
              <p>{visitor.phone}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-500">Host</p>
              <p>{host?.full_name || 'Unknown'}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-500">Location</p>
              <p>{location?.name || 'Unknown'}</p>
            </div>
          </div>
          
          <div>
            <p className="text-sm text-gray-500">Purpose of Visit</p>
            <p>{visit.purpose}</p>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Check-in Time</p>
              <p>{visit.check_in_time ? new Date(visit.check_in_time).toLocaleString() : 'N/A'}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-500">Duration</p>
              <p>{checkInDuration || 'N/A'}</p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end mt-6">
        <button
          onClick={handleCheckOut}
          disabled={loading || success}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
        >
          {loading ? 'Processing...' : success ? 'Checked Out' : 'Check Out Visitor'}
        </button>
      </div>
    </div>
  )
}
