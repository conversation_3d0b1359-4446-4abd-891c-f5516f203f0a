'use client'

import { useState, useEffect } from 'react'
import { UserRole, VisitStatus, Visitor, Visit } from '@/types'
import { createClient } from '@/lib/supabase/client'
import Image from 'next/image'
import LocationSelector from '@/components/location/LocationSelector'
import { getLocationPermissions } from '@/lib/admin/client'
import { LocationHierarchy } from '@/types/admin'

interface VisitorCheckInFormProps {
  orgId: string
  locationId?: string // Now optional to support location selection
  visitId?: string // Optional: If checking in a pre-registered visitor
  hostId?: string // Optional: If host is pre-selected
}

export default function VisitorCheckInForm({
  orgId,
  locationId,
  visitId,
  hostId,
}: VisitorCheckInFormProps) {
  const supabase = createClientComponentClient()
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const isPreRegistered = !!visitId
  const [preRegisteredVisit, setPreRegisteredVisit] = useState<Visit | null>(null)
  const [preRegisteredVisitor, setPreRegisteredVisitor] = useState<Visitor | null>(null)
  const [photoCapture, setPhotoCapture] = useState<boolean>(false)
  const [photoDataUrl, setPhotoDataUrl] = useState<string | null>(null)
  const [cameraStream, setCameraStream] = useState<MediaStream | null>(null)
  const [agreements, setAgreements] = useState<{ id: string; name: string; content: string; is_required: boolean }[]>([])
  const [signedAgreements, setSignedAgreements] = useState<Record<string, boolean>>({})
  const [selectedLocationId, setSelectedLocationId] = useState<string | undefined>(locationId)
  const [userLocations, setUserLocations] = useState<string[]>([])
  const [userPermissions, setUserPermissions] = useState<Record<string, string[]>>({})
  const [showLocationSelector, setShowLocationSelector] = useState<boolean>(!locationId)
  
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    company: '',
    purpose: '',
    hostId: hostId || '',
  })

  const [hosts, setHosts] = useState<{ id: string; full_name: string }[]>([])

  // Fetch hosts from the organization if hostId is not provided
  const fetchHosts = async () => {
    if (hostId) return

    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, full_name')
        .eq('org_id', orgId)
        .in('role', [UserRole.HOST, UserRole.ADMIN, UserRole.MANAGER])
        .order('full_name')

      if (error) throw error
      setHosts(data || [])
    } catch (error) {
      console.error('Error fetching hosts:', error)
      setError('Failed to load hosts. Please try again.')
    }
  }

  // Fetch agreements for the organization
  const fetchAgreements = async () => {
    try {
      const { data, error } = await supabase
        .from('agreements')
        .select('id, name, content, is_required')
        .eq('org_id', orgId)
        .order('name')

      if (error) throw error
      setAgreements(data || [])
      
      // Initialize signed agreements state
      const initialSignedState: Record<string, boolean> = {}
      data?.forEach(agreement => {
        initialSignedState[agreement.id] = false
      })
      setSignedAgreements(initialSignedState)
    } catch (error) {
      console.error('Error fetching agreements:', error)
      setError('Failed to load agreements. Please try again.')
    }
  }

  // Fetch pre-registered visit data if visitId is provided
  const fetchPreRegisteredVisit = async () => {
    if (!visitId) return

    try {
      // Fetch visit data
      const { data: visitData, error: visitError } = await supabase
        .from('visits')
        .select('*')
        .eq('id', visitId)
        .single()

      if (visitError) throw visitError
      setPreRegisteredVisit(visitData as Visit)

      // Fetch visitor data
      const { data: visitorData, error: visitorError } = await supabase
        .from('visitors')
        .select('*')
        .eq('id', visitData.visitor_id)
        .single()

      if (visitorError) throw visitorError
      setPreRegisteredVisitor(visitorData as Visitor)

      // Pre-fill form data
      setFormData({
        fullName: visitorData.full_name,
        email: visitorData.email,
        phone: visitorData.phone,
        company: visitorData.company,
        purpose: visitData.purpose,
        hostId: visitData.host_id,
      })
    } catch (error) {
      console.error('Error fetching pre-registered visit:', error)
      setError('Failed to load pre-registered visit data. Please try again.')
    }
  }

  // Fetch user's accessible locations
  const fetchUserLocations = async () => {
    try {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return
      
      // Get locations the user has access to
      const permissions = await getLocationPermissions(user.id)
      setUserPermissions(permissions)
      
      // Extract location IDs from permissions
      const locationIds = Object.keys(permissions)
      setUserLocations(locationIds)
      
      // If user only has access to one location and no locationId was provided,
      // automatically select that location
      if (locationIds.length === 1 && !locationId) {
        setSelectedLocationId(locationIds[0])
        setShowLocationSelector(false)
      }
    } catch (error) {
      console.error('Error fetching user locations:', error)
    }
  }

  // Load data when component mounts
  useEffect(() => {
    fetchHosts()
    fetchAgreements()
    fetchUserLocations()
    if (visitId) {
      fetchPreRegisteredVisit()
    }
    
    // Clean up camera stream when component unmounts
    return () => {
      if (cameraStream) {
        cameraStream.getTracks().forEach(track => track.stop())
      }
    }
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleAgreementSign = (agreementId: string) => {
    setSignedAgreements(prev => ({
      ...prev,
      [agreementId]: true
    }))
  }

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true })
      setCameraStream(stream)
      setPhotoCapture(true)
      
      // Display camera feed
      const videoElement = document.getElementById('camera-feed') as HTMLVideoElement
      if (videoElement) {
        videoElement.srcObject = stream
      }
    } catch (error) {
      console.error('Error accessing camera:', error)
      setError('Failed to access camera. Please check your permissions and try again.')
    }
  }

  const capturePhoto = () => {
    const videoElement = document.getElementById('camera-feed') as HTMLVideoElement
    const canvas = document.createElement('canvas')
    canvas.width = videoElement.videoWidth
    canvas.height = videoElement.videoHeight
    
    const context = canvas.getContext('2d')
    if (context) {
      context.drawImage(videoElement, 0, 0, canvas.width, canvas.height)
      const dataUrl = canvas.toDataURL('image/jpeg')
      setPhotoDataUrl(dataUrl)
    }
    
    // Stop camera stream
    if (cameraStream) {
      cameraStream.getTracks().forEach(track => track.stop())
      setCameraStream(null)
    }
    
    setPhotoCapture(false)
  }

  const retakePhoto = () => {
    setPhotoDataUrl(null)
    startCamera()
  }

  const uploadPhoto = async (visitorId: string): Promise<string | null> => {
    if (!photoDataUrl) return null
    
    try {
      // Convert data URL to blob
      const res = await fetch(photoDataUrl)
      const blob = await res.blob()
      
      // Upload to Supabase Storage
      const fileName = `visitor-photos/${visitorId}-${Date.now()}.jpg`
      const { error } = await supabase.storage
        .from('visitor-photos')
        .upload(fileName, blob, {
          contentType: 'image/jpeg',
          upsert: true
        })
      
      if (error) throw error
      
      // Get public URL
      const { data: urlData } = supabase.storage
        .from('visitor-photos')
        .getPublicUrl(fileName)
      
      return urlData.publicUrl
    } catch (error) {
      console.error('Error uploading photo:', error)
      return null
    }
  }

  const saveAgreements = async (visitId: string) => {
    const agreementsToSave = agreements
      .filter(agreement => signedAgreements[agreement.id])
      .map(agreement => ({
        visit_id: visitId,
        agreement_id: agreement.id,
        signed_at: new Date().toISOString(),
        signature: 'digital-signature' // In a real app, you'd capture an actual signature
      }))
    
    if (agreementsToSave.length > 0) {
      const { error } = await supabase
        .from('visit_agreements')
        .insert(agreementsToSave)
      
      if (error) throw error
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    
    try {
      // Validate form
      if (!formData.fullName || !formData.email || !formData.phone || !formData.company || !formData.purpose || !formData.hostId) {
        throw new Error('Please fill in all required fields')
      }
      
      // Validate location selection
      if (!selectedLocationId) {
        throw new Error('Please select a location')
      }
      
      // Check if all required agreements are signed
      const requiredAgreements = agreements.filter(a => a.is_required)
      const allRequiredSigned = requiredAgreements.every(a => signedAgreements[a.id])
      
      if (!allRequiredSigned) {
        throw new Error('Please sign all required agreements')
      }

      let visitorId: string
      let visitId: string

      if (isPreRegistered && preRegisteredVisit && preRegisteredVisitor) {
        // Update existing visitor with photo if taken
        visitorId = preRegisteredVisitor.id
        visitId = preRegisteredVisit.id
        
        if (photoDataUrl) {
          const photoUrl = await uploadPhoto(visitorId)
          
          if (photoUrl) {
            await supabase
              .from('visitors')
              .update({ photo_url: photoUrl })
              .eq('id', visitorId)
          }
        }
        
        // Update visit status to checked in
        await supabase
          .from('visits')
          .update({
            status: VisitStatus.CHECKED_IN,
            check_in_time: new Date().toISOString(),
          })
          .eq('id', visitId)
      } else {
        // Create new visitor
        const { data: visitorData, error: visitorError } = await supabase
          .from('visitors')
          .insert({
            org_id: orgId,
            full_name: formData.fullName,
            email: formData.email,
            phone: formData.phone,
            company: formData.company,
          })
          .select('id')
          .single()

        if (visitorError) throw visitorError
        visitorId = visitorData.id
        
        // Upload photo if taken
        let photoUrl = null
        if (photoDataUrl) {
          photoUrl = await uploadPhoto(visitorId)
          
          if (photoUrl) {
            await supabase
              .from('visitors')
              .update({ photo_url: photoUrl })
              .eq('id', visitorId)
          }
        }
        
        // Create visit
        const { data: visitData, error: visitError } = await supabase
          .from('visits')
          .insert({
            org_id: orgId,
            location_id: selectedLocationId,
            host_id: formData.hostId,
            visitor_id: visitorId,
            purpose: formData.purpose,
            status: VisitStatus.CHECKED_IN,
            check_in_time: new Date().toISOString(),
          })
          .select('id')
          .single()

        if (visitError) throw visitError
        visitId = visitData.id
      }
      
      // Save signed agreements
      await saveAgreements(visitId)

      setSuccess(true)
      
      // Generate badge
      window.open(`/dashboard/visitors/visit/${visitId}/badge`, '_blank')
      
      // Reset form for new visitors
      if (!isPreRegistered) {
        setFormData({
          fullName: '',
          email: '',
          phone: '',
          company: '',
          purpose: '',
          hostId: hostId || '',
        })
        setPhotoDataUrl(null)
        
        // Reset signed agreements
        const resetSignedState: Record<string, boolean> = {}
        agreements.forEach(agreement => {
          resetSignedState[agreement.id] = false
        })
        setSignedAgreements(resetSignedState)
      }
    } catch (error: unknown) {
      console.error('Error checking in visitor:', error)
      setError(error instanceof Error ? error.message : 'Failed to check in visitor. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleLocationSelect = (locId: string) => {
    setSelectedLocationId(locId)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">
        {isPreRegistered ? 'Check In Pre-registered Visitor' : 'Check In Visitor'}
      </h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
          Visitor successfully checked in!
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        {showLocationSelector && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3">Select Location</h3>
            <LocationSelector 
              orgId={orgId}
              selectedLocationId={selectedLocationId}
              onLocationSelect={handleLocationSelect}
              className="mb-4"
              // Only show locations the user has access to
              filterLocations={(locations: LocationHierarchy[]) => {
                // If userLocations is empty, show all locations
                if (userLocations.length === 0) return locations;
                
                // Filter locations based on user permissions
                return locations.filter((location: LocationHierarchy) => 
                  userLocations.includes(location.id) && 
                  // Check if user has at least view permission
                  userPermissions[location.id]?.includes('view')
                );
              }}
            />
            {!selectedLocationId && (
              <p className="text-sm text-red-600">Please select a location to continue</p>
            )}
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Visitor Information</h3>
            
            <div>
              <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                Full Name *
              </label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
                readOnly={isPreRegistered}
              />
            </div>
            
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email *
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
                readOnly={isPreRegistered}
              />
            </div>
            
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                Phone *
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
                readOnly={isPreRegistered}
              />
            </div>
            
            <div>
              <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                Company *
              </label>
              <input
                type="text"
                id="company"
                name="company"
                value={formData.company}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
                readOnly={isPreRegistered}
              />
            </div>
            
            <div>
              <label htmlFor="purpose" className="block text-sm font-medium text-gray-700 mb-1">
                Purpose of Visit *
              </label>
              <textarea
                id="purpose"
                name="purpose"
                value={formData.purpose}
                onChange={handleChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
                readOnly={isPreRegistered}
              />
            </div>
            
            {!hostId && !isPreRegistered && (
              <div>
                <label htmlFor="hostId" className="block text-sm font-medium text-gray-700 mb-1">
                  Host *
                </label>
                <select
                  id="hostId"
                  name="hostId"
                  value={formData.hostId}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Select a host</option>
                  {hosts.map((host) => (
                    <option key={host.id} value={host.id}>
                      {host.full_name}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>
          
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Photo & Agreements</h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Visitor Photo
              </label>
              
              {photoCapture ? (
                <div className="space-y-2">
                  <div className="relative w-full h-64 bg-gray-100 rounded-md overflow-hidden">
                    <video 
                      id="camera-feed" 
                      className="absolute inset-0 w-full h-full object-cover"
                      autoPlay 
                      playsInline
                    />
                  </div>
                  <button
                    type="button"
                    onClick={capturePhoto}
                    className="px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    Capture Photo
                  </button>
                </div>
              ) : photoDataUrl ? (
                <div className="space-y-2">
                  <div className="relative w-full h-64 bg-gray-100 rounded-md overflow-hidden">
                    <Image
                      src={photoDataUrl}
                      alt="Captured photo"
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  </div>
                  <button
                    type="button"
                    onClick={retakePhoto}
                    className="px-3 py-1.5 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    Retake Photo
                  </button>
                </div>
              ) : (
                <button
                  type="button"
                  onClick={startCamera}
                  className="px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  Take Photo
                </button>
              )}
            </div>
            
            {agreements.length > 0 && (
              <div className="space-y-3">
                <h4 className="text-md font-medium">Agreements to Sign</h4>
                
                {agreements.map((agreement) => (
                  <div key={agreement.id} className="p-3 border border-gray-200 rounded-md">
                    <div className="flex justify-between items-center mb-2">
                      <h5 className="font-medium">
                        {agreement.name}
                        {agreement.is_required && <span className="text-red-500 ml-1">*</span>}
                      </h5>
                      {signedAgreements[agreement.id] ? (
                        <span className="text-sm text-green-600 font-medium">Signed</span>
                      ) : (
                        <button
                          type="button"
                          onClick={() => handleAgreementSign(agreement.id)}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          Sign Agreement
                        </button>
                      )}
                    </div>
                    <div className="text-sm text-gray-600 max-h-24 overflow-y-auto p-2 bg-gray-50 rounded">
                      {agreement.content}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        <div className="flex justify-end mt-6">
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
          >
            {loading ? 'Processing...' : 'Check In Visitor'}
          </button>
        </div>
      </form>
    </div>
  )
}
