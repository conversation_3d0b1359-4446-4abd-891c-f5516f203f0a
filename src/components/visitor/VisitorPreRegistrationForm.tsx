'use client'

import { useState, useEffect } from 'react'
import { UserRole, VisitStatus } from '@/types'
import { createClient } from '@/lib/supabase/client'

interface VisitorPreRegistrationFormProps {
  orgId: string
  locationId: string
  hostId?: string
}

export default function VisitorPreRegistrationForm({
  orgId,
  locationId,
  hostId,
}: VisitorPreRegistrationFormProps) {
  const supabase = createClient()
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    company: '',
    purpose: '',
    scheduledTime: '',
    hostId: hostId || '',
  })

  const [hosts, setHosts] = useState<{ id: string; full_name: string }[]>([])

  // Fetch hosts from the organization if hostId is not provided
  const fetchHosts = async () => {
    if (hostId) return

    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, full_name')
        .eq('org_id', orgId)
        .in('role', [UserRole.HOST, UserRole.ADMIN, UserRole.MANAGER])
        .order('full_name')

      if (error) throw error
      setHosts(data || [])
    } catch (error) {
      console.error('Error fetching hosts:', error)
      setError('Failed to load hosts. Please try again.')
    }
  }

  // Load hosts when component mounts
  useEffect(() => {
    fetchHosts()
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    
    try {
      // Validate form
      if (!formData.fullName || !formData.email || !formData.phone || !formData.company || !formData.purpose || !formData.scheduledTime || !formData.hostId) {
        throw new Error('Please fill in all required fields')
      }

      // Create visitor
      const { data: visitorData, error: visitorError } = await supabase
        .from('visitors')
        .insert({
          org_id: orgId,
          full_name: formData.fullName,
          email: formData.email,
          phone: formData.phone,
          company: formData.company,
        })
        .select('id')
        .single()

      if (visitorError) throw visitorError

      // Create visit
      const { error: visitError } = await supabase
        .from('visits')
        .insert({
          org_id: orgId,
          location_id: locationId,
          host_id: formData.hostId,
          visitor_id: visitorData.id,
          purpose: formData.purpose,
          scheduled_time: new Date(formData.scheduledTime).toISOString(),
          status: VisitStatus.SCHEDULED,
        })

      if (visitError) throw visitError

      setSuccess(true)
      // Reset form
      setFormData({
        fullName: '',
        email: '',
        phone: '',
        company: '',
        purpose: '',
        scheduledTime: '',
        hostId: hostId || '',
      })
    } catch (error: unknown) {
      console.error('Error registering visitor:', error)
      setError(error instanceof Error ? error.message : 'Failed to register visitor. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Pre-register a Visitor</h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
          Visitor successfully pre-registered!
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
              Full Name *
            </label>
            <input
              type="text"
              id="fullName"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
              Phone *
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          
          <div>
            <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
              Company *
            </label>
            <input
              type="text"
              id="company"
              name="company"
              value={formData.company}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          
          <div>
            <label htmlFor="scheduledTime" className="block text-sm font-medium text-gray-700 mb-1">
              Scheduled Time *
            </label>
            <input
              type="datetime-local"
              id="scheduledTime"
              name="scheduledTime"
              value={formData.scheduledTime}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          
          {!hostId && (
            <div>
              <label htmlFor="hostId" className="block text-sm font-medium text-gray-700 mb-1">
                Host *
              </label>
              <select
                id="hostId"
                name="hostId"
                value={formData.hostId}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Select a host</option>
                {hosts.map((host) => (
                  <option key={host.id} value={host.id}>
                    {host.full_name}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
        
        <div className="mb-4">
          <label htmlFor="purpose" className="block text-sm font-medium text-gray-700 mb-1">
            Purpose of Visit *
          </label>
          <textarea
            id="purpose"
            name="purpose"
            value={formData.purpose}
            onChange={handleChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? 'Submitting...' : 'Pre-register Visitor'}
          </button>
        </div>
      </form>
    </div>
  )
}
