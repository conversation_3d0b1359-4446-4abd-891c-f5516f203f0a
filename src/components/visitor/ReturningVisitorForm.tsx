'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Visitor, Visit, VisitStatus } from '@/types';
import { VisitorMatch } from '@/lib/visitor/recognition';

interface ReturningVisitorFormProps {
  potentialMatches: VisitorMatch[];
  hosts: { id: string; full_name: string }[];
  locations: { id: string; name: string }[];
  onSubmit: (visitorId: string, visitData: Partial<Visit>) => Promise<void>;
  onNewVisitor: () => void;
}

export default function ReturningVisitorForm({
  potentialMatches,
  hosts,
  locations,
  onSubmit,
  onNewVisitor
}: ReturningVisitorFormProps) {
  const router = useRouter();
  const [selectedVisitor, setSelectedVisitor] = useState<Visitor | null>(null);
  const [hostId, setHostId] = useState<string>('');
  const [locationId, setLocationId] = useState<string>('');
  const [purpose, setPurpose] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Set default location if only one is available
  useEffect(() => {
    if (locations.length === 1) {
      setLocationId(locations[0].id);
    }
  }, [locations]);

  const handleSelectVisitor = (visitor: Visitor) => {
    setSelectedVisitor(visitor);
    setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    if (!selectedVisitor) {
      setError('Please select a visitor');
      return;
    }
    
    if (!hostId) {
      setError('Please select a host');
      return;
    }
    
    if (!locationId) {
      setError('Please select a location');
      return;
    }
    
    if (!purpose) {
      setError('Please enter a purpose for the visit');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await onSubmit(selectedVisitor.id, {
        host_id: hostId,
        location_id: locationId,
        purpose,
        status: VisitStatus.CHECKED_IN,
        check_in_time: new Date().toISOString(),
      });
      
      setSuccess(true);
      
      // Redirect to visitor dashboard
      router.push('/dashboard/visitors');
    } catch (error: unknown) {
      console.error('Error checking in visitor:', error);
      setError(error instanceof Error ? error.message : 'Failed to check in visitor. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Welcome Back!</h2>
      <p className="mb-6 text-gray-600">
        We found potential matches for you. Please select your profile or create a new one.
      </p>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
          Visitor checked in successfully!
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-medium mb-4">Select Your Profile</h3>
          <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
            {potentialMatches.map(match => (
              <div
                key={match.visitor.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedVisitor?.id === match.visitor.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => handleSelectVisitor(match.visitor)}
              >
                <div className="flex items-center gap-4">
                  <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                    {match.visitor.photo_url ? (
                      <img 
                        src={match.visitor.photo_url} 
                        alt={match.visitor.full_name}
                        className="h-full w-full object-cover" 
                      />
                    ) : (
                      <span className="text-lg font-medium">
                        {match.visitor.full_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </span>
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{match.visitor.full_name}</h4>
                    <p className="text-sm text-gray-600">{match.visitor.email}</p>
                    <p className="text-sm text-gray-600">{match.visitor.company}</p>
                  </div>
                  <div className="ml-auto px-2 py-1 text-sm border rounded-full">
                    {Math.round(match.confidence)}% match
                  </div>
                </div>
                <div className="mt-2 flex flex-wrap gap-1">
                  {match.matchedOn.map(field => (
                    <span key={field} className="px-2 py-0.5 bg-gray-100 text-gray-700 rounded-full text-xs">
                      {field}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
          <button 
            type="button"
            className="w-full mt-4 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            onClick={onNewVisitor}
          >
            Not you? Create new visitor profile
          </button>
        </div>
        
        {selectedVisitor && (
          <div>
            <h3 className="text-lg font-medium mb-4">Quick Check-in</h3>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="hostId" className="block text-sm font-medium text-gray-700">
                  Host *
                </label>
                <select
                  id="hostId"
                  value={hostId}
                  onChange={(e) => setHostId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Select a host</option>
                  {hosts.map(host => (
                    <option key={host.id} value={host.id}>
                      {host.full_name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="space-y-2">
                <label htmlFor="locationId" className="block text-sm font-medium text-gray-700">
                  Location *
                </label>
                <select
                  id="locationId"
                  value={locationId}
                  onChange={(e) => setLocationId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Select a location</option>
                  {locations.map(location => (
                    <option key={location.id} value={location.id}>
                      {location.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="space-y-2">
                <label htmlFor="purpose" className="block text-sm font-medium text-gray-700">
                  Purpose of Visit *
                </label>
                <textarea
                  id="purpose"
                  value={purpose}
                  onChange={(e) => setPurpose(e.target.value)}
                  placeholder="Enter the purpose of your visit"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {isSubmitting ? 'Checking in...' : 'Quick Check-in'}
              </button>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
