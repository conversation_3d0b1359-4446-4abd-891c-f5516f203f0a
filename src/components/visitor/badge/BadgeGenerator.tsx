'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import Image from 'next/image'
import { BadgeField, BadgeFieldType, BadgeTemplateData, Visit, Visitor } from '@/types'
import { badgeTemplateToStyles, badgeFieldToStyles, generateFieldValue, generateQRCode, generateBarcode } from '@/lib/utils/badge'

interface BadgeGeneratorProps {
  orgId: string
  visitId: string
  templateId?: string // Optional: If not provided, use default template
  onPrint: () => void
}

export default function BadgeGenerator({
  orgId,
  visitId,
  templateId,
  onPrint,
}: BadgeGeneratorProps) {
  const supabase = createClient()
  
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [template, setTemplate] = useState<BadgeTemplateData | null>(null)
  const [visit, setVisit] = useState<Visit | null>(null)
  const [visitor, setVisitor] = useState<Visitor | null>(null)
  const [hostName, setHostName] = useState<string>('')
  const [orgName, setOrgName] = useState<string>('')
  const [locationName, setLocationName] = useState<string>('')
  
  // Load data when component mounts
  useEffect(() => {
    loadData()
  }, [templateId])
  
  const loadData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Load visit data
      const { data: visitData, error: visitError } = await supabase
        .from('visits')
        .select('*')
        .eq('id', visitId)
        .single()
      
      if (visitError) throw visitError
      setVisit(visitData)
      
      // Load visitor data
      const { data: visitorData, error: visitorError } = await supabase
        .from('visitors')
        .select('*')
        .eq('id', visitData.visitor_id)
        .single()
      
      if (visitorError) throw visitorError
      setVisitor(visitorData)
      
      // Load host data
      const { data: hostData, error: hostError } = await supabase
        .from('users')
        .select('full_name')
        .eq('id', visitData.host_id)
        .single()
      
      if (hostError) throw hostError
      setHostName(hostData.full_name)
      
      // Load organization data
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('name')
        .eq('id', orgId)
        .single()
      
      if (orgError) throw orgError
      setOrgName(orgData.name)
      
      // Load location data
      const { data: locationData, error: locationError } = await supabase
        .from('locations')
        .select('name')
        .eq('id', visitData.location_id)
        .single()
      
      if (locationError) {
        console.error('Error fetching location data:', locationError)
        setLocationName('Unknown Location')
      } else {
        setLocationName(locationData.name)
      }
      
      // Load template data
      let templateData
      
      if (templateId) {
        // Load specific template
        const { data, error } = await supabase
          .from('badges')
          .select('template')
          .eq('id', templateId)
          .eq('org_id', orgId)
          .single()
        
        if (error) throw error
        templateData = data.template
      } else {
        // Load default template
        const { data, error } = await supabase
          .from('badges')
          .select('template')
          .eq('org_id', orgId)
          .eq('template->isDefault', true)
          .single()
        
        if (error) {
          // If no default template, load the first template
          const { data: firstTemplate, error: firstTemplateError } = await supabase
            .from('badges')
            .select('template')
            .eq('org_id', orgId)
            .order('created_at', { ascending: true })
            .limit(1)
            .single()
          
          if (firstTemplateError) throw firstTemplateError
          templateData = firstTemplate.template
        } else {
          templateData = data.template
        }
      }
      
      setTemplate(templateData)
    } catch (error) {
      console.error('Error loading badge data:', error)
      setError('Failed to load badge data. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  const handlePrint = () => {
    // Trigger print dialog
    window.print()
    onPrint()
  }
  
  if (loading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-md">
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-500">Loading badge...</div>
        </div>
      </div>
    )
  }
  
  if (error || !template || !visit || !visitor) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-md">
        <div className="flex justify-center items-center h-40">
          <div className="text-red-500">
            {error || 'Failed to load badge data. Please try again.'}
          </div>
        </div>
      </div>
    )
  }
  
  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Visitor Badge</h2>
        <button
          onClick={handlePrint}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 print:hidden"
        >
          Print Badge
        </button>
      </div>
      
      <div className="flex justify-center mb-6">
        <div className="relative">
          <style>
            {badgeTemplateToStyles(template)}
            {template.fields.map(field => badgeFieldToStyles(field)).join('\n')}
            {`
              @media print {
                body * {
                  visibility: hidden;
                }
                .badge-container, .badge-container * {
                  visibility: visible;
                }
                .badge-container {
                  position: absolute;
                  left: 0;
                  top: 0;
                }
              }
            `}
          </style>
          
          <div id="badge-container" className="badge-container">
            {template.fields.map(field => (
              <div key={field.id} className={`field-${field.id}`}>
                {renderFieldContent(field, visitor, visit, hostName, orgName, locationName)}
              </div>
            ))}
          </div>
          
          <div className="mt-2 text-center text-sm text-gray-500 print:hidden">
            {template.width} mm × {template.height} mm
          </div>
        </div>
      </div>
      
      <div className="text-sm text-gray-500 mb-4 print:hidden">
        <p>Click the Print Badge button to print this badge.</p>
      </div>
    </div>
  )
}

function renderFieldContent(
  field: BadgeField,
  visitor: Visitor,
  visit: Visit,
  hostName: string,
  orgName: string,
  locationName: string
) {
  switch (field.type) {
    case BadgeFieldType.TEXT:
      return field.value
    
    case BadgeFieldType.VISITOR_NAME:
    case BadgeFieldType.VISITOR_COMPANY:
    case BadgeFieldType.VISITOR_EMAIL:
    case BadgeFieldType.HOST_NAME:
    case BadgeFieldType.VISIT_DATE:
    case BadgeFieldType.VISIT_PURPOSE:
    case BadgeFieldType.ORGANIZATION_NAME:
    case BadgeFieldType.LOCATION_NAME:
      return generateFieldValue(field, visitor, visit, hostName, orgName, locationName)
    
    case BadgeFieldType.VISITOR_PHOTO:
      return visitor.photo_url ? (
        <Image
          src={visitor.photo_url}
          alt={visitor.full_name}
          fill
          style={{ objectFit: 'cover' }}
        />
      ) : (
        <div className="w-full h-full bg-gray-200 flex items-center justify-center text-gray-500 text-xs">
          No Photo
        </div>
      )
    
    case BadgeFieldType.QR_CODE:
      const qrData = `VISITOR:${visitor.id}|VISIT:${visit.id}|NAME:${visitor.full_name}`
      return (
        <Image
          src={generateQRCode(qrData)}
          alt="QR Code"
          fill
          style={{ objectFit: 'contain' }}
        />
      )
    
    case BadgeFieldType.BARCODE:
      const barcodeData = `V${visitor.id}${visit.id.substring(0, 8)}`
      return (
        <Image
          src={generateBarcode(barcodeData)}
          alt="Barcode"
          fill
          style={{ objectFit: 'contain' }}
        />
      )
    
    case BadgeFieldType.ORGANIZATION_LOGO:
      // In a real app, you would fetch the organization logo
      return (
        <div className="w-full h-full flex items-center justify-center text-gray-500 text-xs">
          Organization Logo
        </div>
      )
    
    default:
      return null
  }
}
