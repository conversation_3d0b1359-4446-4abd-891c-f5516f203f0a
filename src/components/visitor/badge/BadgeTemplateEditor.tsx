'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { BadgeField, BadgeFieldType, BadgeTemplateData } from '@/types'
import { createBadgeField, createEmptyBadgeTemplate, getBadgeFieldTypeName, badgeTemplateToStyles, badgeFieldToStyles } from '@/lib/utils/badge'
import { v4 as uuidv4 } from 'uuid'

interface BadgeTemplateEditorProps {
  orgId: string
  templateId?: string // If editing an existing template
  onSave: (templateId: string) => void
  onCancel: () => void
}

export default function BadgeTemplateEditor({
  orgId,
  templateId,
  onSave,
  onCancel,
}: BadgeTemplateEditorProps) {
  const supabase = createClient()
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [template, setTemplate] = useState<BadgeTemplateData>(createEmptyBadgeTemplate())
  const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null)
  const [draggedField, setDraggedField] = useState<{ id: string; startX: number; startY: number; fieldX: number; fieldY: number } | null>(null)
  const [resizingField, setResizingField] = useState<{ id: string; startX: number; startY: number; startWidth: number; startHeight: number } | null>(null)
  
  // Load template data if editing an existing template
  useEffect(() => {
    if (templateId) {
      loadTemplate()
    }
  }, [templateId])
  
  const loadTemplate = async () => {
    if (!templateId) return
    
    setLoading(true)
    setError(null)
    
    try {
      const { data, error } = await supabase
        .from('badges')
        .select('*')
        .eq('id', templateId)
        .eq('org_id', orgId)
        .single()
      
      if (error) throw error
      
      if (data && data.template) {
        setTemplate(data.template as BadgeTemplateData)
      }
    } catch (error) {
      console.error('Error loading badge template:', error)
      setError('Failed to load badge template. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  const handleSave = async () => {
    setLoading(true)
    setError(null)
    
    try {
      if (!template.name) {
        throw new Error('Please enter a template name')
      }
      
      if (templateId) {
        // Update existing template
        const { error } = await supabase
          .from('badges')
          .update({
            name: template.name,
            template: template,
            updated_at: new Date().toISOString(),
          })
          .eq('id', templateId)
        
        if (error) throw error
        
        onSave(templateId)
      } else {
        // Create new template
        const newTemplateId = uuidv4()
        
        const { error } = await supabase
          .from('badges')
          .insert({
            id: newTemplateId,
            org_id: orgId,
            name: template.name,
            template: template,
          })
        
        if (error) throw error
        
        onSave(newTemplateId)
      }
    } catch (error: unknown) {
      console.error('Error saving badge template:', error)
      setError(
        error instanceof Error 
          ? error.message 
          : 'Failed to save badge template. Please try again.'
      )
    } finally {
      setLoading(false)
    }
  }
  
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTemplate(prev => ({ ...prev, name: e.target.value }))
  }
  
  const handleWidthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const width = parseFloat(e.target.value)
    if (!isNaN(width) && width > 0) {
      setTemplate(prev => ({ ...prev, width }))
    }
  }
  
  const handleHeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const height = parseFloat(e.target.value)
    if (!isNaN(height) && height > 0) {
      setTemplate(prev => ({ ...prev, height }))
    }
  }
  
  const handleBackgroundColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTemplate(prev => ({ ...prev, backgroundColor: e.target.value }))
  }
  
  const handleBorderColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTemplate(prev => ({ ...prev, borderColor: e.target.value }))
  }
  
  const handleBorderWidthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const borderWidth = parseFloat(e.target.value)
    if (!isNaN(borderWidth) && borderWidth >= 0) {
      setTemplate(prev => ({ ...prev, borderWidth }))
    }
  }
  
  const handleIsDefaultChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTemplate(prev => ({ ...prev, isDefault: e.target.checked }))
  }
  
  const addField = (type: BadgeFieldType) => {
    const newField = createBadgeField(type)
    setTemplate(prev => ({
      ...prev,
      fields: [...prev.fields, newField]
    }))
    setSelectedFieldId(newField.id)
  }
  
  const removeField = (id: string) => {
    setTemplate(prev => ({
      ...prev,
      fields: prev.fields.filter(field => field.id !== id)
    }))
    
    if (selectedFieldId === id) {
      setSelectedFieldId(null)
    }
  }
  
  const updateField = (id: string, updates: Partial<BadgeField>) => {
    setTemplate(prev => ({
      ...prev,
      fields: prev.fields.map(field => 
        field.id === id ? { ...field, ...updates } : field
      )
    }))
  }
  
  const handleFieldMouseDown = (e: React.MouseEvent, fieldId: string) => {
    e.preventDefault()
    
    const field = template.fields.find(f => f.id === fieldId)
    if (!field) return
    
    setSelectedFieldId(fieldId)
    
    // Start dragging
    setDraggedField({
      id: fieldId,
      startX: e.clientX,
      startY: e.clientY,
      fieldX: field.x,
      fieldY: field.y
    })
    
    // Add event listeners for dragging
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }
  
  const handleResizeMouseDown = (e: React.MouseEvent, fieldId: string) => {
    e.preventDefault()
    e.stopPropagation()
    
    const field = template.fields.find(f => f.id === fieldId)
    if (!field) return
    
    // Start resizing
    setResizingField({
      id: fieldId,
      startX: e.clientX,
      startY: e.clientY,
      startWidth: field.width,
      startHeight: field.height
    })
    
    // Add event listeners for resizing
    document.addEventListener('mousemove', handleResizeMouseMove)
    document.addEventListener('mouseup', handleResizeMouseUp)
  }
  
  const handleMouseMove = (e: MouseEvent) => {
    if (!draggedField) return
    
    const badgeContainer = document.getElementById('badge-container')
    if (!badgeContainer) return
    
    const rect = badgeContainer.getBoundingClientRect()
    
    // Calculate movement as percentage of badge size
    const deltaXPercent = ((e.clientX - draggedField.startX) / rect.width) * 100
    const deltaYPercent = ((e.clientY - draggedField.startY) / rect.height) * 100
    
    // Update field position
    const newX = Math.max(0, Math.min(100 - 5, draggedField.fieldX + deltaXPercent))
    const newY = Math.max(0, Math.min(100 - 5, draggedField.fieldY + deltaYPercent))
    
    updateField(draggedField.id, { x: newX, y: newY })
  }
  
  const handleResizeMouseMove = (e: MouseEvent) => {
    if (!resizingField) return
    
    const badgeContainer = document.getElementById('badge-container')
    if (!badgeContainer) return
    
    const rect = badgeContainer.getBoundingClientRect()
    
    // Calculate size change as percentage of badge size
    const deltaWidthPercent = ((e.clientX - resizingField.startX) / rect.width) * 100
    const deltaHeightPercent = ((e.clientY - resizingField.startY) / rect.height) * 100
    
    // Update field size
    const newWidth = Math.max(5, Math.min(100, resizingField.startWidth + deltaWidthPercent))
    const newHeight = Math.max(5, Math.min(100, resizingField.startHeight + deltaHeightPercent))
    
    updateField(resizingField.id, { width: newWidth, height: newHeight })
  }
  
  const handleMouseUp = () => {
    setDraggedField(null)
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  const handleResizeMouseUp = () => {
    setResizingField(null)
    document.removeEventListener('mousemove', handleResizeMouseMove)
    document.removeEventListener('mouseup', handleResizeMouseUp)
  }
  
  const selectedField = selectedFieldId 
    ? template.fields.find(field => field.id === selectedFieldId) 
    : null
  
  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">
        {templateId ? 'Edit Badge Template' : 'Create Badge Template'}
      </h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Template Properties */}
        <div className="lg:col-span-1 space-y-4">
          <div className="p-4 border border-gray-200 rounded-md">
            <h3 className="text-lg font-semibold mb-4">Template Properties</h3>
            
            <div className="space-y-3">
              <div>
                <label htmlFor="templateName" className="block text-sm font-medium text-gray-700 mb-1">
                  Template Name *
                </label>
                <input
                  type="text"
                  id="templateName"
                  value={template.name}
                  onChange={handleNameChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label htmlFor="templateWidth" className="block text-sm font-medium text-gray-700 mb-1">
                    Width (mm)
                  </label>
                  <input
                    type="number"
                    id="templateWidth"
                    value={template.width}
                    onChange={handleWidthChange}
                    min="10"
                    step="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="templateHeight" className="block text-sm font-medium text-gray-700 mb-1">
                    Height (mm)
                  </label>
                  <input
                    type="number"
                    id="templateHeight"
                    value={template.height}
                    onChange={handleHeightChange}
                    min="10"
                    step="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="backgroundColor" className="block text-sm font-medium text-gray-700 mb-1">
                  Background Color
                </label>
                <div className="flex items-center">
                  <input
                    type="color"
                    id="backgroundColor"
                    value={template.backgroundColor}
                    onChange={handleBackgroundColorChange}
                    className="w-10 h-10 border border-gray-300 rounded-md mr-2"
                  />
                  <input
                    type="text"
                    value={template.backgroundColor}
                    onChange={handleBackgroundColorChange}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="borderColor" className="block text-sm font-medium text-gray-700 mb-1">
                  Border Color
                </label>
                <div className="flex items-center">
                  <input
                    type="color"
                    id="borderColor"
                    value={template.borderColor || '#cccccc'}
                    onChange={handleBorderColorChange}
                    className="w-10 h-10 border border-gray-300 rounded-md mr-2"
                  />
                  <input
                    type="text"
                    value={template.borderColor || '#cccccc'}
                    onChange={handleBorderColorChange}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="borderWidth" className="block text-sm font-medium text-gray-700 mb-1">
                  Border Width (px)
                </label>
                <input
                  type="number"
                  id="borderWidth"
                  value={template.borderWidth || 0}
                  onChange={handleBorderWidthChange}
                  min="0"
                  step="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isDefault"
                  checked={template.isDefault || false}
                  onChange={handleIsDefaultChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="isDefault" className="ml-2 block text-sm text-gray-700">
                  Set as default template
                </label>
              </div>
            </div>
          </div>
          
          <div className="p-4 border border-gray-200 rounded-md">
            <h3 className="text-lg font-semibold mb-4">Add Fields</h3>
            
            <div className="grid grid-cols-2 gap-2">
              {Object.values(BadgeFieldType).map(type => (
                <button
                  key={type}
                  type="button"
                  onClick={() => addField(type)}
                  className="px-3 py-2 bg-gray-100 text-gray-800 text-sm rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {getBadgeFieldTypeName(type)}
                </button>
              ))}
            </div>
          </div>
          
          {selectedField && (
            <div className="p-4 border border-gray-200 rounded-md">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Field Properties</h3>
                <button
                  type="button"
                  onClick={() => removeField(selectedField.id)}
                  className="px-2 py-1 bg-red-100 text-red-700 text-sm rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  Remove
                </button>
              </div>
              
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Field Type
                  </label>
                  <div className="px-3 py-2 border border-gray-300 bg-gray-50 rounded-md">
                    {getBadgeFieldTypeName(selectedField.type)}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label htmlFor="fieldX" className="block text-sm font-medium text-gray-700 mb-1">
                      X Position (%)
                    </label>
                    <input
                      type="number"
                      id="fieldX"
                      value={selectedField.x}
                      onChange={(e) => updateField(selectedField.id, { x: parseFloat(e.target.value) })}
                      min="0"
                      max="100"
                      step="0.1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="fieldY" className="block text-sm font-medium text-gray-700 mb-1">
                      Y Position (%)
                    </label>
                    <input
                      type="number"
                      id="fieldY"
                      value={selectedField.y}
                      onChange={(e) => updateField(selectedField.id, { y: parseFloat(e.target.value) })}
                      min="0"
                      max="100"
                      step="0.1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label htmlFor="fieldWidth" className="block text-sm font-medium text-gray-700 mb-1">
                      Width (%)
                    </label>
                    <input
                      type="number"
                      id="fieldWidth"
                      value={selectedField.width}
                      onChange={(e) => updateField(selectedField.id, { width: parseFloat(e.target.value) })}
                      min="1"
                      max="100"
                      step="0.1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="fieldHeight" className="block text-sm font-medium text-gray-700 mb-1">
                      Height (%)
                    </label>
                    <input
                      type="number"
                      id="fieldHeight"
                      value={selectedField.height}
                      onChange={(e) => updateField(selectedField.id, { height: parseFloat(e.target.value) })}
                      min="1"
                      max="100"
                      step="0.1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
                
                {(selectedField.type === BadgeFieldType.TEXT ||
                  selectedField.type === BadgeFieldType.VISITOR_NAME ||
                  selectedField.type === BadgeFieldType.VISITOR_COMPANY ||
                  selectedField.type === BadgeFieldType.VISITOR_EMAIL ||
                  selectedField.type === BadgeFieldType.HOST_NAME ||
                  selectedField.type === BadgeFieldType.VISIT_DATE ||
                  selectedField.type === BadgeFieldType.VISIT_PURPOSE ||
                  selectedField.type === BadgeFieldType.ORGANIZATION_NAME) && (
                  <>
                    {selectedField.type === BadgeFieldType.TEXT && (
                      <div>
                        <label htmlFor="fieldValue" className="block text-sm font-medium text-gray-700 mb-1">
                          Text
                        </label>
                        <input
                          type="text"
                          id="fieldValue"
                          value={selectedField.value || ''}
                          onChange={(e) => updateField(selectedField.id, { value: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    )}
                    
                    <div>
                      <label htmlFor="fontSize" className="block text-sm font-medium text-gray-700 mb-1">
                        Font Size (px)
                      </label>
                      <input
                        type="number"
                        id="fontSize"
                        value={selectedField.fontSize || 14}
                        onChange={(e) => updateField(selectedField.id, { fontSize: parseFloat(e.target.value) })}
                        min="8"
                        max="72"
                        step="1"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="fontColor" className="block text-sm font-medium text-gray-700 mb-1">
                        Font Color
                      </label>
                      <div className="flex items-center">
                        <input
                          type="color"
                          id="fontColor"
                          value={selectedField.fontColor || '#000000'}
                          onChange={(e) => updateField(selectedField.id, { fontColor: e.target.value })}
                          className="w-10 h-10 border border-gray-300 rounded-md mr-2"
                        />
                        <input
                          type="text"
                          value={selectedField.fontColor || '#000000'}
                          onChange={(e) => updateField(selectedField.id, { fontColor: e.target.value })}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="fontWeight" className="block text-sm font-medium text-gray-700 mb-1">
                        Font Weight
                      </label>
                      <select
                        id="fontWeight"
                        value={selectedField.fontWeight || 'normal'}
                        onChange={(e) => updateField(selectedField.id, { fontWeight: e.target.value as 'normal' | 'bold' })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="normal">Normal</option>
                        <option value="bold">Bold</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="alignment" className="block text-sm font-medium text-gray-700 mb-1">
                        Alignment
                      </label>
                      <select
                        id="alignment"
                        value={selectedField.alignment || 'left'}
                        onChange={(e) => updateField(selectedField.id, { alignment: e.target.value as 'left' | 'center' | 'right' })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="left">Left</option>
                        <option value="center">Center</option>
                        <option value="right">Right</option>
                      </select>
                    </div>
                  </>
                )}
                
                <div>
                  <label htmlFor="rotation" className="block text-sm font-medium text-gray-700 mb-1">
                    Rotation (degrees)
                  </label>
                  <input
                    type="number"
                    id="rotation"
                    value={selectedField.rotation || 0}
                    onChange={(e) => updateField(selectedField.id, { rotation: parseFloat(e.target.value) })}
                    min="-180"
                    max="180"
                    step="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Badge Preview */}
        <div className="lg:col-span-2">
          <div className="p-4 border border-gray-200 rounded-md">
            <h3 className="text-lg font-semibold mb-4">Badge Preview</h3>
            
            <div className="flex justify-center mb-4">
              <div className="relative">
                <style>
                  {badgeTemplateToStyles(template)}
                  {template.fields.map(field => badgeFieldToStyles(field)).join('\n')}
                </style>
                
                <div id="badge-container" className="badge-container">
                  {template.fields.map(field => (
                    <div
                      key={field.id}
                      className={`field-${field.id} ${selectedFieldId === field.id ? 'ring-2 ring-blue-500' : ''}`}
                      onClick={() => setSelectedFieldId(field.id)}
                      onMouseDown={(e) => handleFieldMouseDown(e, field.id)}
                      style={{ cursor: 'move' }}
                    >
                      {field.type === BadgeFieldType.TEXT && field.value}
                      {field.type === BadgeFieldType.VISITOR_NAME && 'John Doe'}
                      {field.type === BadgeFieldType.VISITOR_COMPANY && 'Acme Inc.'}
                      {field.type === BadgeFieldType.VISITOR_EMAIL && '<EMAIL>'}
                      {field.type === BadgeFieldType.HOST_NAME && 'Jane Smith'}
                      {field.type === BadgeFieldType.VISIT_DATE && new Date().toLocaleDateString()}
                      {field.type === BadgeFieldType.VISIT_PURPOSE && 'Business Meeting'}
                      {field.type === BadgeFieldType.ORGANIZATION_NAME && 'Your Organization'}
                      {field.type === BadgeFieldType.LOCATION_NAME && 'Headquarters'}
                      
                      {field.type === BadgeFieldType.VISITOR_PHOTO && (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center text-gray-500 text-xs">
                          Visitor Photo
                        </div>
                      )}
                      
                      {field.type === BadgeFieldType.QR_CODE && (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center text-gray-500 text-xs">
                          QR Code
                        </div>
                      )}
                      
                      {field.type === BadgeFieldType.BARCODE && (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center text-gray-500 text-xs">
                          Barcode
                        </div>
                      )}
                      
                      {field.type === BadgeFieldType.ORGANIZATION_LOGO && (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center text-gray-500 text-xs">
                          Logo
                        </div>
                      )}
                      
                      {selectedFieldId === field.id && (
                        <div
                          className="absolute bottom-0 right-0 w-4 h-4 bg-blue-500 cursor-se-resize"
                          onMouseDown={(e) => handleResizeMouseDown(e, field.id)}
                        />
                      )}
                    </div>
                  ))}
                </div>
                
                <div className="mt-2 text-center text-sm text-gray-500">
                  {template.width} mm × {template.height} mm
                </div>
              </div>
            </div>
            
            <div className="text-sm text-gray-500 mb-4">
              <p>Click and drag fields to position them. Select a field to edit its properties.</p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end mt-6 space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Cancel
        </button>
        
        <button
          type="button"
          onClick={handleSave}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? 'Saving...' : 'Save Template'}
        </button>
      </div>
    </div>
  )
}
