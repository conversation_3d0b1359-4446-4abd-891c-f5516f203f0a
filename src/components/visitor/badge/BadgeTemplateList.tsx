'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { BadgeTemplateData } from '@/types'

interface BadgeTemplateListProps {
  orgId: string
  onEdit: (templateId: string) => void
}

export default function BadgeTemplateList({
  orgId,
  onEdit,
}: BadgeTemplateListProps) {
  const supabase = createClient()
  
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [templates, setTemplates] = useState<Array<{
    id: string
    name: string
    template: BadgeTemplateData
    created_at: string
    updated_at: string
  }>>([])
  
  // Load templates when component mounts
  useEffect(() => {
    loadTemplates()
  }, [])
  
  const loadTemplates = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const { data, error } = await supabase
        .from('badges')
        .select('*')
        .eq('org_id', orgId)
        .order('name')
      
      if (error) throw error
      
      setTemplates(data || [])
    } catch (error) {
      console.error('Error loading badge templates:', error)
      setError('Failed to load badge templates. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  const handleSetDefault = async (templateId: string) => {
    try {
      // First, fetch all templates
      const { data: allTemplates, error: fetchError } = await supabase
        .from('badges')
        .select('id, template')
        .eq('org_id', orgId)
      
      if (fetchError) throw fetchError
      
      // Update each template individually
      for (const template of allTemplates || []) {
        const updatedTemplate = {
          ...template.template,
          isDefault: template.id === templateId
        }
        
        const { error: updateError } = await supabase
          .from('badges')
          .update({ template: updatedTemplate })
          .eq('id', template.id)
          .eq('org_id', orgId)
        
        if (updateError) throw updateError
      }
      
      // Reload templates
      await loadTemplates()
    } catch (error) {
      console.error('Error setting default template:', error)
      setError('Failed to set default template. Please try again.')
    }
  }
  
  const handleDelete = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) {
      return
    }
    
    try {
      const { error } = await supabase
        .from('badges')
        .delete()
        .eq('id', templateId)
        .eq('org_id', orgId)
      
      if (error) throw error
      
      // Reload templates
      await loadTemplates()
    } catch (error) {
      console.error('Error deleting badge template:', error)
      setError('Failed to delete badge template. Please try again.')
    }
  }
  
  if (loading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-md">
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-500">Loading badge templates...</div>
        </div>
      </div>
    )
  }
  
  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Badge Templates</h2>
        <Link
          href="/dashboard/visitors/badges/new"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Create New Template
        </Link>
      </div>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      {templates.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p className="mb-4">No badge templates found.</p>
          <p>Create your first badge template to get started.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Template Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Size
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Updated
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Default
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {templates.map((template) => (
                <tr key={template.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{template.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">
                      {template.template.width} × {template.template.height} mm
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">
                      {new Date(template.updated_at || template.created_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <input
                        type="radio"
                        name="defaultTemplate"
                        checked={template.template.isDefault || false}
                        onChange={() => handleSetDefault(template.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => onEdit(template.id)}
                      className="text-blue-600 hover:text-blue-900 mr-4"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete(template.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}
