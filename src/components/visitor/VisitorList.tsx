'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { Visit, VisitStatus, Visitor } from '@/types'
import { FilterState } from './VisitorFilter'
import { SearchResult } from './VisitorSearch'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface VisitorListProps {
  orgId: string
  filters: FilterState
  searchResults: SearchResult[] | null
}

interface VisitWithVisitor extends Visit {
  visitor: Visitor
  host_name: string
  location_name: string
}

export default function VisitorList({
  orgId,
  filters,
  searchResults,
}: VisitorListProps) {
  const supabase = createClient()
  
  const [visits, setVisits] = useState<VisitWithVisitor[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  
  // Load visits when filters change or search results are provided
  useEffect(() => {
    if (searchResults) {
      // If search results are provided, use them instead of fetching from the database
      const searchVisits = searchResults
        .filter(result => result.type === 'visit')
        .map(result => result.data as Visit)
      
      // We would need to fetch additional data for these visits
      // For simplicity, we'll just set them directly
      setVisits(searchVisits.map(visit => ({
        ...visit,
        visitor: { full_name: 'Search Result', company: '', email: '', phone: '' } as Visitor,
        host_name: 'Host',
        location_name: 'Location'
      })) as VisitWithVisitor[])
      
      setTotalCount(searchVisits.length)
      setCurrentPage(1)
      setLoading(false)
    } else {
      // Otherwise, fetch visits based on filters
      fetchVisits()
    }
  }, [filters, searchResults, currentPage, pageSize])
  
  const fetchVisits = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Build query
      let query = supabase
        .from('visits')
        .select(`
          *,
          visitors!inner(*),
          users!inner(full_name),
          locations!inner(name)
        `, { count: 'exact' })
        .eq('org_id', orgId)
      
      // Apply filters
      if (filters.status && filters.status.length > 0) {
        query = query.in('status', filters.status)
      }
      
      if (filters.hostId) {
        query = query.eq('host_id', filters.hostId)
      }
      
      if (filters.locationId) {
        query = query.eq('location_id', filters.locationId)
      }
      
      if (filters.dateRange.from) {
        query = query.gte('scheduled_time', filters.dateRange.from)
      }
      
      if (filters.dateRange.to) {
        query = query.lte('scheduled_time', filters.dateRange.to)
      }
      
      // Apply pagination
      const from = (currentPage - 1) * pageSize
      const to = from + pageSize - 1
      
      query = query
        .order('scheduled_time', { ascending: false })
        .range(from, to)
      
      const { data, error, count } = await query
      
      if (error) throw error
      
      // Format data
      const formattedVisits = data.map(visit => ({
        ...visit,
        visitor: visit.visitors,
        host_name: visit.users.full_name,
        location_name: visit.locations.name
      })) as VisitWithVisitor[]
      
      setVisits(formattedVisits)
      setTotalCount(count || 0)
    } catch (error) {
      console.error('Error fetching visits:', error)
      setError('Failed to load visits. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  const getStatusBadgeClass = (status: VisitStatus) => {
    switch (status) {
      case VisitStatus.SCHEDULED:
        return 'bg-yellow-100 text-yellow-800'
      case VisitStatus.CHECKED_IN:
        return 'bg-green-100 text-green-800'
      case VisitStatus.CHECKED_OUT:
        return 'bg-blue-100 text-blue-800'
      case VisitStatus.CANCELLED:
        return 'bg-red-100 text-red-800'
      case VisitStatus.NO_SHOW:
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
  
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleString()
  }
  
  const totalPages = Math.ceil(totalCount / pageSize)
  
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return
    setCurrentPage(page)
  }
  
  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setPageSize(Number(e.target.value))
    setCurrentPage(1) // Reset to first page when changing page size
  }
  
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-500">Loading visits...</div>
        </div>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-center items-center h-40">
          <div className="text-red-500">{error}</div>
        </div>
      </div>
    )
  }
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Visitor List</h3>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Show</span>
          <select
            value={pageSize}
            onChange={handlePageSizeChange}
            className="px-2 py-1 border border-gray-300 rounded-md text-sm"
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
          <span className="text-sm text-gray-500">per page</span>
        </div>
      </div>
      
      {visits.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No visits found matching your criteria.</p>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Visitor
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Host
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Purpose
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date/Time
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {visits.map((visit) => (
                  <tr key={visit.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{visit.visitor.full_name}</div>
                      <div className="text-sm text-gray-500">{visit.visitor.company}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{visit.host_name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{visit.location_name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{visit.purpose}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatDate(visit.scheduled_time)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(visit.status as VisitStatus)}`}>
                        {visit.status.charAt(0).toUpperCase() + visit.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link href={`/dashboard/visitors/visit/${visit.id}`} className="text-blue-600 hover:text-blue-900 mr-4">
                        View
                      </Link>
                      {visit.status === VisitStatus.SCHEDULED && (
                        <Link href={`/dashboard/visitors/check-in/${visit.id}`} className="text-green-600 hover:text-green-900 mr-4">
                          Check In
                        </Link>
                      )}
                      {visit.status === VisitStatus.CHECKED_IN && (
                        <>
                          <Link href={`/dashboard/visitors/check-out/${visit.id}`} className="text-red-600 hover:text-red-900 mr-4">
                            Check Out
                          </Link>
                          <Link href={`/dashboard/visitors/visit/${visit.id}/badge`} className="text-purple-600 hover:text-purple-900">
                            Badge
                          </Link>
                        </>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-gray-500">
              Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} results
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-2 py-1 border border-gray-300 rounded-md disabled:opacity-50"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                // Show pages around current page
                let pageNum = currentPage
                if (totalPages <= 5) {
                  pageNum = i + 1
                } else if (currentPage <= 3) {
                  pageNum = i + 1
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i
                } else {
                  pageNum = currentPage - 2 + i
                }
                
                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`px-3 py-1 border rounded-md ${
                      currentPage === pageNum
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {pageNum}
                  </button>
                )
              })}
              
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-2 py-1 border border-gray-300 rounded-md disabled:opacity-50"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
