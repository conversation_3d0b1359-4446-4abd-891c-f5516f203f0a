'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { VisitStatus, UserRole } from '@/types'

interface VisitorFilterProps {
  orgId: string
  onFilterChange: (filters: FilterState) => void
}

export interface FilterState {
  status: VisitStatus[] | null
  dateRange: {
    from: string | null
    to: string | null
  }
  hostId: string | null
  locationId: string | null
}

export default function VisitorFilter({ orgId, onFilterChange }: VisitorFilterProps) {
  const supabase = createClient()
  
  const [hosts, setHosts] = useState<{ id: string; full_name: string }[]>([])
  const [locations, setLocations] = useState<{ id: string; name: string }[]>([])
  
  const [filters, setFilters] = useState<FilterState>({
    status: null,
    dateRange: {
      from: null,
      to: null
    },
    hostId: null,
    locationId: null
  })
  
  // Predefined date ranges
  const dateRanges = [
    { label: 'Today', value: 'today' },
    { label: 'Yesterday', value: 'yesterday' },
    { label: 'This Week', value: 'this-week' },
    { label: 'Last Week', value: 'last-week' },
    { label: 'This Month', value: 'this-month' },
    { label: 'Last Month', value: 'last-month' },
    { label: 'Custom Range', value: 'custom' }
  ]
  
  const [selectedDateRange, setSelectedDateRange] = useState<string>('')
  const [showCustomDateRange, setShowCustomDateRange] = useState(false)
  
  // Load hosts and locations when component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch hosts
        const { data: hostsData, error: hostsError } = await supabase
          .from('users')
          .select('id, full_name')
          .eq('org_id', orgId)
          .in('role', [UserRole.HOST, UserRole.ADMIN, UserRole.MANAGER])
          .order('full_name')
        
        if (hostsError) throw hostsError
        setHosts(hostsData || [])
        
        // Fetch locations
        const { data: locationsData, error: locationsError } = await supabase
          .from('locations')
          .select('id, name')
          .eq('org_id', orgId)
          .order('name')
        
        if (locationsError) throw locationsError
        setLocations(locationsData || [])
      } catch (error) {
        console.error('Error fetching filter data:', error)
      }
    }
    
    fetchData()
  }, [orgId, supabase])
  
  // Update filters when any filter value changes
  useEffect(() => {
    onFilterChange(filters)
  }, [filters, onFilterChange])
  
  // Handle status filter change
  const handleStatusChange = (status: VisitStatus) => {
    setFilters(prev => {
      const currentStatuses = prev.status || []
      
      if (currentStatuses.includes(status)) {
        // Remove status if already selected
        return {
          ...prev,
          status: currentStatuses.filter(s => s !== status).length > 0 
            ? currentStatuses.filter(s => s !== status) 
            : null
        }
      } else {
        // Add status if not already selected
        return {
          ...prev,
          status: [...currentStatuses, status]
        }
      }
    })
  }
  
  // Handle host filter change
  const handleHostChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const hostId = e.target.value || null
    setFilters(prev => ({
      ...prev,
      hostId
    }))
  }
  
  // Handle location filter change
  const handleLocationChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const locationId = e.target.value || null
    setFilters(prev => ({
      ...prev,
      locationId
    }))
  }
  
  // Handle date range selection
  const handleDateRangeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value
    setSelectedDateRange(value)
    
    if (value === 'custom') {
      setShowCustomDateRange(true)
      return
    }
    
    setShowCustomDateRange(false)
    
    const now = new Date()
    let fromDate: Date | null = null
    let toDate: Date | null = null
    
    switch (value) {
      case 'today':
        fromDate = new Date(now.setHours(0, 0, 0, 0))
        toDate = new Date(now.setHours(23, 59, 59, 999))
        break
      case 'yesterday':
        fromDate = new Date(now)
        fromDate.setDate(fromDate.getDate() - 1)
        fromDate.setHours(0, 0, 0, 0)
        toDate = new Date(now)
        toDate.setDate(toDate.getDate() - 1)
        toDate.setHours(23, 59, 59, 999)
        break
      case 'this-week':
        fromDate = new Date(now)
        fromDate.setDate(fromDate.getDate() - fromDate.getDay())
        fromDate.setHours(0, 0, 0, 0)
        toDate = new Date(now)
        toDate.setDate(toDate.getDate() + (6 - toDate.getDay()))
        toDate.setHours(23, 59, 59, 999)
        break
      case 'last-week':
        fromDate = new Date(now)
        fromDate.setDate(fromDate.getDate() - fromDate.getDay() - 7)
        fromDate.setHours(0, 0, 0, 0)
        toDate = new Date(now)
        toDate.setDate(toDate.getDate() - toDate.getDay() - 1)
        toDate.setHours(23, 59, 59, 999)
        break
      case 'this-month':
        fromDate = new Date(now.getFullYear(), now.getMonth(), 1)
        toDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999)
        break
      case 'last-month':
        fromDate = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        toDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999)
        break
      default:
        // No date range selected
        break
    }
    
    setFilters(prev => ({
      ...prev,
      dateRange: {
        from: fromDate ? fromDate.toISOString() : null,
        to: toDate ? toDate.toISOString() : null
      }
    }))
  }
  
  // Handle custom date range changes
  const handleCustomDateChange = (field: 'from' | 'to', value: string) => {
    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [field]: value ? new Date(value).toISOString() : null
      }
    }))
  }
  
  // Clear all filters
  const handleClearFilters = () => {
    setFilters({
      status: null,
      dateRange: {
        from: null,
        to: null
      },
      hostId: null,
      locationId: null
    })
    setSelectedDateRange('')
    setShowCustomDateRange(false)
  }
  
  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Filter Visitors</h3>
        <button
          onClick={handleClearFilters}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Clear Filters
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <div className="flex flex-wrap gap-2">
            {Object.values(VisitStatus).map(status => (
              <button
                key={status}
                onClick={() => handleStatusChange(status)}
                className={`px-2 py-1 text-xs rounded-full ${
                  filters.status?.includes(status)
                    ? 'bg-blue-100 text-blue-800 border border-blue-300'
                    : 'bg-gray-100 text-gray-800 border border-gray-200 hover:bg-gray-200'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>
        
        {/* Date Range Filter */}
        <div>
          <label htmlFor="dateRange" className="block text-sm font-medium text-gray-700 mb-1">
            Date Range
          </label>
          <select
            id="dateRange"
            value={selectedDateRange}
            onChange={handleDateRangeChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Time</option>
            {dateRanges.map(range => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>
          
          {showCustomDateRange && (
            <div className="mt-2 grid grid-cols-2 gap-2">
              <div>
                <label htmlFor="fromDate" className="block text-xs font-medium text-gray-700 mb-1">
                  From
                </label>
                <input
                  type="date"
                  id="fromDate"
                  onChange={(e) => handleCustomDateChange('from', e.target.value)}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label htmlFor="toDate" className="block text-xs font-medium text-gray-700 mb-1">
                  To
                </label>
                <input
                  type="date"
                  id="toDate"
                  onChange={(e) => handleCustomDateChange('to', e.target.value)}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          )}
        </div>
        
        {/* Host Filter */}
        <div>
          <label htmlFor="hostFilter" className="block text-sm font-medium text-gray-700 mb-1">
            Host
          </label>
          <select
            id="hostFilter"
            value={filters.hostId || ''}
            onChange={handleHostChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Hosts</option>
            {hosts.map(host => (
              <option key={host.id} value={host.id}>
                {host.full_name}
              </option>
            ))}
          </select>
        </div>
        
        {/* Location Filter */}
        <div>
          <label htmlFor="locationFilter" className="block text-sm font-medium text-gray-700 mb-1">
            Location
          </label>
          <select
            id="locationFilter"
            value={filters.locationId || ''}
            onChange={handleLocationChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Locations</option>
            {locations.map(location => (
              <option key={location.id} value={location.id}>
                {location.name}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  )
}
