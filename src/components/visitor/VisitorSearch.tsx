'use client'

import { useState, useEffect, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Visitor, Visit } from '@/types'
import { Search, X } from 'lucide-react'

interface VisitorSearchProps {
  orgId: string
  onSearch: (query: string, results: SearchResult[]) => void
}

export interface SearchResult {
  type: 'visitor' | 'visit'
  id: string
  title: string
  subtitle: string
  data: Visitor | Visit
}

export default function VisitorSearch({ orgId, onSearch }: VisitorSearchProps) {
  const supabase = createClient()
  
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  
  // Load recent searches from localStorage
  useEffect(() => {
    const storedSearches = localStorage.getItem('recentVisitorSearches')
    if (storedSearches) {
      try {
        const parsedSearches = JSON.parse(storedSearches)
        if (Array.isArray(parsedSearches)) {
          setRecentSearches(parsedSearches.slice(0, 5))
        }
      } catch (error) {
        console.error('Error parsing recent searches:', error)
      }
    }
  }, [])
  
  // Handle clicks outside the search component
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false)
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])
  
  // Perform search when query changes
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (query.trim().length >= 2) {
        performSearch(query)
      } else {
        setResults([])
      }
    }, 300)
    
    return () => clearTimeout(delayDebounceFn)
  }, [query])
  
  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) return
    
    setLoading(true)
    
    try {
      // Search visitors
      const { data: visitorData, error: visitorError } = await supabase
        .from('visitors')
        .select('*')
        .eq('org_id', orgId)
        .or(`full_name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%,company.ilike.%${searchQuery}%,phone.ilike.%${searchQuery}%`)
        .limit(5)
      
      if (visitorError) throw visitorError
      
      // Search visits
      const { data: visitData, error: visitError } = await supabase
        .from('visits')
        .select('*, visitors(*)')
        .eq('org_id', orgId)
        .or(`purpose.ilike.%${searchQuery}%`)
        .limit(5)
      
      if (visitError) throw visitError
      
      // Format results
      const formattedResults: SearchResult[] = [
        ...(visitorData || []).map((visitor: Visitor) => ({
          type: 'visitor' as const,
          id: visitor.id,
          title: visitor.full_name,
          subtitle: visitor.company || visitor.email,
          data: visitor
        })),
        ...(visitData || []).map((visit: Visit & { visitors?: Visitor }) => ({
          type: 'visit' as const,
          id: visit.id,
          title: visit.visitors?.full_name || 'Unknown Visitor',
          subtitle: visit.purpose || 'No purpose specified',
          data: visit
        }))
      ]
      
      setResults(formattedResults)
      onSearch(searchQuery, formattedResults)
      
      // Add to recent searches
      if (searchQuery.trim() && !recentSearches.includes(searchQuery)) {
        const updatedSearches = [searchQuery, ...recentSearches].slice(0, 5)
        setRecentSearches(updatedSearches)
        localStorage.setItem('recentVisitorSearches', JSON.stringify(updatedSearches))
      }
    } catch (error) {
      console.error('Error performing search:', error)
    } finally {
      setLoading(false)
    }
  }
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value)
    setShowResults(true)
  }
  
  const handleInputFocus = () => {
    setShowResults(true)
  }
  
  const handleClearSearch = () => {
    setQuery('')
    setResults([])
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }
  
  const handleRecentSearchClick = (searchQuery: string) => {
    setQuery(searchQuery)
    performSearch(searchQuery)
  }
  
  const handleResultClick = (result: SearchResult) => {
    // This could navigate to the visitor/visit detail page
    // or perform some other action
    onSearch(query, [result])
    setShowResults(false)
  }
  
  return (
    <div ref={searchRef} className="relative w-full max-w-md">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <Search className="w-5 h-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          placeholder="Search visitors, visits..."
          className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        
        {query && (
          <button
            onClick={handleClearSearch}
            className="absolute inset-y-0 right-0 flex items-center pr-3"
          >
            <X className="w-5 h-5 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>
      
      {showResults && (
        <div className="absolute z-10 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200">
          {loading ? (
            <div className="p-4 text-center text-gray-500">
              Searching...
            </div>
          ) : results.length > 0 ? (
            <ul className="max-h-60 overflow-y-auto">
              {results.map((result) => (
                <li
                  key={`${result.type}-${result.id}`}
                  onClick={() => handleResultClick(result)}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                >
                  <div className="font-medium">{result.title}</div>
                  <div className="text-sm text-gray-500">
                    {result.type === 'visitor' ? 'Visitor' : 'Visit'}: {result.subtitle}
                  </div>
                </li>
              ))}
            </ul>
          ) : query.length >= 2 ? (
            <div className="p-4 text-center text-gray-500">
              No results found
            </div>
          ) : recentSearches.length > 0 ? (
            <div>
              <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase">
                Recent Searches
              </div>
              <ul>
                {recentSearches.map((search, index) => (
                  <li
                    key={index}
                    onClick={() => handleRecentSearchClick(search)}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                  >
                    <Search className="w-4 h-4 text-gray-400 mr-2" />
                    <span>{search}</span>
                  </li>
                ))}
              </ul>
            </div>
          ) : null}
        </div>
      )}
    </div>
  )
}
