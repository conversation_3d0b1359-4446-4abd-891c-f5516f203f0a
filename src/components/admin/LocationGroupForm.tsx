'use client'

import { useState, useEffect } from 'react'
import { LocationGroup } from '@/types/admin'
import { createLocationGroup, updateLocationGroup } from '@/lib/admin/client'

interface LocationGroupFormProps {
  orgId: string
  group?: LocationGroup
  onSuccess: () => void
  onCancel: () => void
}

export default function LocationGroupForm({
  orgId,
  group,
  onSuccess,
  onCancel
}: LocationGroupFormProps) {
  const isEditing = !!group
  
  const [formData, setFormData] = useState<Partial<LocationGroup>>({
    org_id: orgId,
    name: '',
    description: '',
    color: '#3B82F6' // Default blue color
  })
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Populate form with group data if editing
  useEffect(() => {
    if (group) {
      setFormData({
        ...group,
        org_id: orgId
      })
    }
  }, [group, orgId])
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    
    try {
      if (isEditing && group) {
        // Update existing group
        await updateLocationGroup(group.id, formData)
      } else {
        // Create new group
        await createLocationGroup(formData as Omit<LocationGroup, 'id' | 'created_at' | 'updated_at'>)
      }
      
      onSuccess()
    } catch (err) {
      console.error('Error saving location group:', err)
      setError('Failed to save location group. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  const colorOptions = [
    { name: 'Blue', value: '#3B82F6' },
    { name: 'Red', value: '#EF4444' },
    { name: 'Green', value: '#10B981' },
    { name: 'Yellow', value: '#F59E0B' },
    { name: 'Purple', value: '#8B5CF6' },
    { name: 'Pink', value: '#EC4899' },
    { name: 'Indigo', value: '#6366F1' },
    { name: 'Gray', value: '#6B7280' }
  ]
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}
      
      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Group Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name || ''}
            onChange={handleChange}
            required
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description || ''}
            onChange={handleChange}
            rows={3}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Color
          </label>
          <div className="grid grid-cols-4 gap-2">
            {colorOptions.map(color => (
              <div 
                key={color.value}
                className={`
                  flex items-center justify-center p-2 rounded-md cursor-pointer border
                  ${formData.color === color.value ? 'ring-2 ring-offset-2 ring-blue-500' : 'border-gray-300'}
                `}
                onClick={() => setFormData(prev => ({ ...prev, color: color.value }))}
              >
                <div 
                  className="h-6 w-6 rounded-full mr-2"
                  style={{ backgroundColor: color.value }}
                />
                <span className="text-sm">{color.name}</span>
              </div>
            ))}
          </div>
          <input
            type="hidden"
            name="color"
            value={formData.color || '#3B82F6'}
          />
        </div>
      </div>
      
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? 'Saving...' : isEditing ? 'Update Group' : 'Create Group'}
        </button>
      </div>
    </form>
  )
}
