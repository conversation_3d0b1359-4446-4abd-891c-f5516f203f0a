'use client'

import { useState, useEffect } from 'react'
import { Location, UserWithRole } from '@/types/admin'
import { getLocations, getUsers, addLocationPermission, removeLocationPermission } from '@/lib/admin/client'
import { Building, MapPin, Search, UserCheck } from 'lucide-react'

interface LocationPermissionsFormProps {
  orgId: string
  onSuccess?: () => void
}

interface UserLocationPermissions {
  user_id: string
  locations: {
    location_id: string
    is_primary: boolean
    access_level: 'full' | 'limited' | 'view_only' | 'none'
  }[]
}

export default function LocationPermissionsForm({
  orgId,
  onSuccess
}: LocationPermissionsFormProps) {
  const [locations, setLocations] = useState<Location[]>([])
  const [users, setUsers] = useState<UserWithRole[]>([])
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)
  const [userPermissions, setUserPermissions] = useState<UserLocationPermissions | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [error, setError] = useState<string | null>(null)
  
  // Fetch locations and users
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [locationsData, usersData] = await Promise.all([
          getLocations(orgId),
          getUsers(orgId)
        ])
        
        setLocations(locationsData)
        setUsers(usersData)
        setLoading(false)
      } catch (err) {
        console.error('Error fetching data:', err)
        setError('Failed to load locations and users')
        setLoading(false)
      }
    }
    
    fetchData()
  }, [orgId])
  
  // Fetch user permissions when a user is selected
  useEffect(() => {
    if (selectedUserId) {
      const fetchUserPermissions = async () => {
        try {
          // This would be replaced with an actual API call
          // For now, we'll create a mock implementation
          const mockPermissions: UserLocationPermissions = {
            user_id: selectedUserId,
            locations: locations.map(location => ({
              location_id: location.id,
              is_primary: false,
              access_level: 'view_only'
            }))
          }
          
          // Set the first location as primary for demonstration
          if (mockPermissions.locations.length > 0) {
            mockPermissions.locations[0].is_primary = true
            mockPermissions.locations[0].access_level = 'full'
          }
          
          setUserPermissions(mockPermissions)
        } catch (err) {
          console.error('Error fetching user permissions:', err)
          setError('Failed to load user permissions')
        }
      }
      
      fetchUserPermissions()
    } else {
      setUserPermissions(null)
    }
  }, [selectedUserId, locations])
  
  const handleUserSelect = (userId: string) => {
    setSelectedUserId(userId)
    setError(null)
  }
  
  const handleAccessLevelChange = (locationId: string, accessLevel: 'full' | 'limited' | 'view_only' | 'none') => {
    if (!userPermissions) return
    
    setUserPermissions(prev => {
      if (!prev) return prev
      
      return {
        ...prev,
        locations: prev.locations.map(loc => 
          loc.location_id === locationId 
            ? { ...loc, access_level: accessLevel } 
            : loc
        )
      }
    })
  }
  
  const handlePrimaryLocationChange = (locationId: string) => {
    if (!userPermissions) return
    
    setUserPermissions(prev => {
      if (!prev) return prev
      
      return {
        ...prev,
        locations: prev.locations.map(loc => ({
          ...loc,
          is_primary: loc.location_id === locationId
        }))
      }
    })
  }
  
  const handleSave = async () => {
    if (!userPermissions) return
    
    setSaving(true)
    setError(null)
    
    try {
      // Update permissions for each location
      const promises = userPermissions.locations.map(async (loc) => {
        if (loc.access_level === 'none') {
          // Remove all permissions
          await removeLocationPermission(userPermissions.user_id, loc.location_id, 'view');
          await removeLocationPermission(userPermissions.user_id, loc.location_id, 'edit');
          await removeLocationPermission(userPermissions.user_id, loc.location_id, 'manage');
        } else if (loc.access_level === 'view_only') {
          // Add view permission, remove others
          await addLocationPermission(userPermissions.user_id, loc.location_id, 'view');
          await removeLocationPermission(userPermissions.user_id, loc.location_id, 'edit');
          await removeLocationPermission(userPermissions.user_id, loc.location_id, 'manage');
        } else if (loc.access_level === 'limited') {
          // Add view and edit permissions, remove manage
          await addLocationPermission(userPermissions.user_id, loc.location_id, 'view');
          await addLocationPermission(userPermissions.user_id, loc.location_id, 'edit');
          await removeLocationPermission(userPermissions.user_id, loc.location_id, 'manage');
        } else if (loc.access_level === 'full') {
          // Add all permissions
          await addLocationPermission(userPermissions.user_id, loc.location_id, 'view');
          await addLocationPermission(userPermissions.user_id, loc.location_id, 'edit');
          await addLocationPermission(userPermissions.user_id, loc.location_id, 'manage');
        }
      });
      
      await Promise.all(promises);
      
      if (onSuccess) {
        onSuccess()
      }
    } catch (err) {
      console.error('Error saving user permissions:', err)
      setError('Failed to save user permissions')
    } finally {
      setSaving(false)
    }
  }
  
  const filteredUsers = users.filter(user => {
    const fullName = (user.full_name || '').toLowerCase()
    const email = (user.email || '').toLowerCase()
    const query = searchQuery.toLowerCase()
    
    return fullName.includes(query) || email.includes(query)
  })
  
  const selectedUser = users.find(user => user.id === selectedUserId)
  
  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse flex space-x-4">
          <div className="flex-1 space-y-4 py-1">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    )
  }
  
  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-4 border-b">
        <h2 className="text-lg font-medium">Location Permissions</h2>
        <p className="text-sm text-gray-500">
          Manage which locations users can access and their permission levels
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-0">
        {/* User List */}
        <div className="border-r">
          <div className="p-3 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              />
            </div>
          </div>
          
          <div className="overflow-y-auto max-h-96">
            {filteredUsers.length > 0 ? (
              <ul className="divide-y">
                {filteredUsers.map(user => (
                  <li 
                    key={user.id}
                    className={`
                      p-3 cursor-pointer hover:bg-gray-50
                      ${selectedUserId === user.id ? 'bg-blue-50' : ''}
                    `}
                    onClick={() => handleUserSelect(user.id)}
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                          <UserCheck className="h-4 w-4 text-gray-500" />
                        </div>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          {user.full_name}
                        </p>
                        <p className="text-xs text-gray-500">{user.email}</p>
                      </div>
                      <div className="ml-auto">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {user.role?.name || 'No Role'}
                        </span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="p-4 text-center text-gray-500">
                No users found matching your search
              </div>
            )}
          </div>
        </div>
        
        {/* Permissions Form */}
        <div className="col-span-2 p-4">
          {selectedUser && userPermissions ? (
            <div>
              <div className="mb-4">
                <h3 className="text-lg font-medium">
                  {selectedUser.full_name}
                </h3>
                <p className="text-sm text-gray-500">{selectedUser.email}</p>
                <p className="text-sm text-gray-500">
                  Role: <span className="font-medium">{selectedUser.role?.name || 'No Role'}</span>
                </p>
              </div>
              
              {error && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md">
                  {error}
                </div>
              )}
              
              <div className="mb-4">
                <h4 className="text-sm font-medium mb-2">Location Access</h4>
                <p className="text-xs text-gray-500 mb-4">
                  Set which locations this user can access and their permission level for each location.
                  Mark one location as primary - this will be their default location.
                </p>
                
                <div className="border rounded-md overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Location
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Access Level
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Primary
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {locations.map(location => {
                        const locationPermission = userPermissions.locations.find(
                          loc => loc.location_id === location.id
                        ) || {
                          location_id: location.id,
                          is_primary: false,
                          access_level: 'none' as const
                        }
                        
                        return (
                          <tr key={location.id}>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="flex items-center">
                                {location.location_type === 'headquarters' ? (
                                  <Building className="h-4 w-4 mr-2 text-blue-600" />
                                ) : (
                                  <MapPin className="h-4 w-4 mr-2 text-gray-600" />
                                )}
                                <div>
                                  <div className="text-sm font-medium text-gray-900">
                                    {location.name}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {location.location_type === 'headquarters' ? 'Headquarters' : 
                                     location.location_type === 'branch' ? 'Branch Office' :
                                     location.location_type === 'facility' ? 'Facility' :
                                     location.location_type === 'office' ? 'Office' : 'Location'}
                                    
                                    {location.city && (
                                      <span className="ml-1">
                                        • {location.city}
                                        {location.country && `, ${location.country}`}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <select
                                value={locationPermission.access_level}
                                onChange={(e) => handleAccessLevelChange(
                                  location.id, 
                                  e.target.value as 'full' | 'limited' | 'view_only' | 'none'
                                )}
                                className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                              >
                                <option value="full">Full Access</option>
                                <option value="limited">Limited Access</option>
                                <option value="view_only">View Only</option>
                                <option value="none">No Access</option>
                              </select>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-center">
                              <input
                                type="radio"
                                name="primary_location"
                                checked={locationPermission.is_primary}
                                onChange={() => handlePrimaryLocationChange(location.id)}
                                disabled={locationPermission.access_level === 'none'}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setSelectedUserId(null)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSave}
                  disabled={saving}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {saving ? 'Saving...' : 'Save Permissions'}
                </button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full py-12">
              <div className="text-center">
                <UserCheck className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No User Selected</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Select a user from the list to manage their location permissions
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
