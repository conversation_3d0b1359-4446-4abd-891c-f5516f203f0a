'use client'

import React, { useState, useEffect, Fragment } from 'react'
import { Workflow, WorkflowTrigger, WorkflowCondition, WorkflowAction } from '@/types/admin'
import { createWorkflow, updateWorkflow } from '@/lib/admin/client'
import { Plus, Minus, AlertCircle } from 'lucide-react'

interface WorkflowFormProps {
  orgId: string
  workflow?: Workflow
  onSuccess: () => void
  onCancel: () => void
}

export default function WorkflowForm({
  orgId,
  workflow,
  onSuccess,
  onCancel
}: WorkflowFormProps) {
  const isEditing = !!workflow

  const [formData, setFormData] = useState<Partial<Workflow>>({
    org_id: orgId,
    name: '',
    description: '',
    trigger: {
      event: 'pre_registration'
    },
    conditions: [],
    actions: [],
    is_active: true
  })

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Condition and action state
  const [newCondition, setNewCondition] = useState<WorkflowCondition>({
    field: 'visitor.email',
    operator: 'contains',
    value: ''
  })

  const [newAction, setNewAction] = useState<WorkflowAction>({
    type: 'notification',
    config: {
      recipient: 'host',
      channel: 'email',
      subject: '',
      message: ''
    }
  })

  // Populate form with workflow data if editing
  useEffect(() => {
    if (workflow) {
      setFormData({
        ...workflow,
        org_id: orgId
      })
    }
  }, [workflow, orgId])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleTriggerChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target

    setFormData(prev => {
      const updatedTrigger = {
        ...(prev.trigger || {}),
        [name]: value
      }

      return {
        ...prev,
        trigger: updatedTrigger as WorkflowTrigger
      }
    })
  }

  const handleConditionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target

    setNewCondition(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleActionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target

    if (name.startsWith('config.')) {
      const configKey = name.replace('config.', '')
      setNewAction(prev => ({
        ...prev,
        config: {
          ...prev.config,
          [configKey]: value
        }
      }))
    } else {
      setNewAction(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleAddCondition = () => {
    if (!newCondition.field || !newCondition.operator) {
      setError('Field and operator are required for conditions')
      return
    }

    if (newCondition.operator !== 'is_empty' && newCondition.operator !== 'is_not_empty' && !newCondition.value) {
      setError('Value is required for this condition operator')
      return
    }

    setFormData(prev => ({
      ...prev,
      conditions: [...(prev.conditions || []), { ...newCondition }]
    }))

    setNewCondition({
      field: 'visitor.email',
      operator: 'contains',
      value: ''
    })

    setError(null)
  }

  const handleRemoveCondition = (index: number) => {
    setFormData(prev => ({
      ...prev,
      conditions: (prev.conditions || []).filter((_, i) => i !== index)
    }))
  }

  const handleAddAction = () => {
    if (newAction.type === 'notification') {
      if (!newAction.config.recipient || !newAction.config.channel || !newAction.config.message) {
        setError('Recipient, channel, and message are required for notifications')
        return
      }
    } else if (newAction.type === 'email') {
      if (!newAction.config.recipient || !newAction.config.subject || !newAction.config.message) {
        setError('Recipient, subject, and message are required for emails')
        return
      }
    } else if (newAction.type === 'webhook') {
      if (!newAction.config.url) {
        setError('URL is required for webhooks')
        return
      }
    }

    setFormData(prev => ({
      ...prev,
      actions: [...(prev.actions || []), { ...newAction }]
    }))

    // Reset new action form
    setNewAction({
      type: 'notification',
      config: {
        recipient: 'host',
        channel: 'email',
        subject: '',
        message: ''
      }
    })

    setError(null)
  }

  const handleRemoveAction = (index: number) => {
    setFormData(prev => ({
      ...prev,
      actions: (prev.actions || []).filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Validate form
    if (!formData.name) {
      setError('Workflow name is required')
      setLoading(false)
      return
    }

    if (!formData.trigger || !formData.trigger.event) {
      setError('Trigger event is required')
      setLoading(false)
      return
    }

    if (!formData.actions || formData.actions.length === 0) {
      setError('At least one action is required')
      setLoading(false)
      return
    }

    try {
      if (isEditing && workflow) {
        // Update existing workflow
        await updateWorkflow(workflow.id, formData)
      } else {
        // Create new workflow
        await createWorkflow(formData as Omit<Workflow, 'id' | 'created_at' | 'updated_at'>)
      }

      onSuccess()
    } catch (err) {
      console.error('Error saving workflow:', err)
      setError('Failed to save workflow. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const triggerEventOptions = [
    { value: 'pre_registration', label: 'Pre-registration' },
    { value: 'check_in', label: 'Check-in' },
    { value: 'check_out', label: 'Check-out' },
    { value: 'visit_approved', label: 'Visit Approved' },
    { value: 'visit_rejected', label: 'Visit Rejected' },
    { value: 'visit_cancelled', label: 'Visit Cancelled' },
    { value: 'scheduled', label: 'Scheduled' }
  ]

  const conditionFieldOptions = [
    { value: 'visitor.email', label: 'Visitor Email' },
    { value: 'visitor.name', label: 'Visitor Name' },
    { value: 'visitor.company', label: 'Visitor Company' },
    { value: 'visit.purpose', label: 'Visit Purpose' },
    { value: 'host.email', label: 'Host Email' },
    { value: 'host.name', label: 'Host Name' },
    { value: 'location.name', label: 'Location Name' }
  ]

  const conditionOperatorOptions = [
    { value: 'equals', label: 'Equals' },
    { value: 'not_equals', label: 'Not Equals' },
    { value: 'contains', label: 'Contains' },
    { value: 'not_contains', label: 'Not Contains' },
    { value: 'greater_than', label: 'Greater Than' },
    { value: 'less_than', label: 'Less Than' },
    { value: 'in', label: 'In List' },
    { value: 'not_in', label: 'Not In List' },
    { value: 'is_empty', label: 'Is Empty' },
    { value: 'is_not_empty', label: 'Is Not Empty' }
  ]

  const actionTypeOptions = [
    { value: 'notification', label: 'Send Notification' },
    { value: 'email', label: 'Send Email' },
    { value: 'sms', label: 'Send SMS' },
    { value: 'webhook', label: 'Call Webhook' }
  ]

  const recipientOptions = [
    { value: 'host', label: 'Host' },
    { value: 'visitor', label: 'Visitor' },
    { value: 'admin', label: 'Admin' },
    { value: 'custom', label: 'Custom Email' }
  ]

  const channelOptions = [
    { value: 'email', label: 'Email' },
    { value: 'sms', label: 'SMS' },
    { value: 'in_app', label: 'In-App' }
  ]

  const getConditionFieldLabel = (field: string) => {
    return conditionFieldOptions.find(option => option.value === field)?.label || field
  }

  const getConditionOperatorLabel = (operator: string) => {
    return conditionOperatorOptions.find(option => option.value === operator)?.label || operator
  }

  const getActionTypeLabel = (type: string) => {
    return actionTypeOptions.find(option => option.value === type)?.label || type
  }

  return (
    <div>
      <h1>Workflow Form</h1>
      <p>This is a test</p>
    </div>
  )
}

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Basic Information</h3>

        <div className="grid grid-cols-1 gap-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Workflow Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description || ''}
              onChange={handleChange}
              rows={2}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_active"
            name="is_active"
            checked={formData.is_active !== false}
            onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700">
            Active
          </label>
        </div>
      </div>

      <div className="space-y-4 border-t pt-4">
        <h3 className="text-lg font-medium">Trigger</h3>

        <div>
          <label htmlFor="event" className="block text-sm font-medium text-gray-700">
            Event *
          </label>
          <select
            id="event"
            name="event"
            value={formData.trigger?.event || 'pre_registration'}
            onChange={handleTriggerChange}
            required
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            {triggerEventOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {formData.trigger?.event === 'scheduled' && (
          <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
            Scheduled triggers will be implemented in a future update.
          </div>
        )}
      </div>

      <div className="space-y-4 border-t pt-4">
        <h3 className="text-lg font-medium">Conditions</h3>
        <p className="text-sm text-gray-500">
          Conditions are optional. If no conditions are specified, the workflow will run for all events of the selected type.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="field" className="block text-sm font-medium text-gray-700">
              Field
            </label>
            <select
              id="field"
              name="field"
              value={newCondition.field}
              onChange={handleConditionChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              {conditionFieldOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="operator" className="block text-sm font-medium text-gray-700">
              Operator
            </label>
            <select
              id="operator"
              name="operator"
              value={newCondition.operator}
              onChange={handleConditionChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              {conditionOperatorOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="value" className="block text-sm font-medium text-gray-700">
              Value
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                id="value"
                name="value"
                value={typeof newCondition.value === 'string' ? newCondition.value : (newCondition.value?.toString() || '')}
                onChange={handleConditionChange}
                disabled={newCondition.operator === 'is_empty' || newCondition.operator === 'is_not_empty'}
                className="flex-1 mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-100 disabled:text-gray-500"
                placeholder={
                  newCondition.operator === 'in' || newCondition.operator === 'not_in'
                    ? 'Comma-separated values'
                    : 'Value'
                }
              />
              <button
                type="button"
                onClick={handleAddCondition}
                className="mt-1 inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          {(formData.conditions || []).map((condition, index) => (
            <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
              <div className="flex-1">
                <span className="text-sm font-medium">
                  {getConditionFieldLabel(condition.field)}
                </span>
                <span className="text-sm text-gray-500 mx-2">
                  {getConditionOperatorLabel(condition.operator)}
                </span>
                {condition.operator !== 'is_empty' && condition.operator !== 'is_not_empty' && (
                  <span className="text-sm">
                    {condition.value}
                  </span>
                )}
              </div>
              <button
                type="button"
                onClick={() => handleRemoveCondition(index)}
                className="text-red-600 hover:text-red-800"
              >
                <Minus className="h-4 w-4" />
              </button>
            </div>
          ))}

          {(formData.conditions || []).length === 0 && (
            <p className="text-sm text-gray-500">No conditions added yet</p>
          )}
        </div>
      </div>

      <div className="space-y-4 border-t pt-4">
        <h3 className="text-lg font-medium">Actions</h3>

        <div>
          <label htmlFor="action_type" className="block text-sm font-medium text-gray-700">
            Action Type
          </label>
          <select
            id="action_type"
            name="type"
            value={newAction.type}
            onChange={handleActionChange}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            {actionTypeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Action configuration based on type */}
        {(newAction.type === 'notification' || newAction.type === 'email' || newAction.type === 'sms') && (
          <div className="space-y-4">
            <div>
              <label htmlFor="recipient" className="block text-sm font-medium text-gray-700">
                Recipient
              </label>
              <select
                id="recipient"
                name="config.recipient"
                value={typeof newAction.config.recipient === 'string' ? newAction.config.recipient : 'host'}
                onChange={handleActionChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                {recipientOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {newAction.config.recipient === 'custom' && (
              <div>
                <label htmlFor="custom_recipient" className="block text-sm font-medium text-gray-700">
                  Custom Recipient
                </label>
                <input
                  type="text"
                  id="custom_recipient"
                  name="config.custom_recipient"
                  value={typeof newAction.config.custom_recipient === 'string' ? newAction.config.custom_recipient : ''}
                  onChange={handleActionChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Email address"
                />
              </div>
            )}

            {newAction.type === 'notification' && (
              <div>
                <label htmlFor="channel" className="block text-sm font-medium text-gray-700">
                  Channel
                </label>
                <select
                  id="channel"
                  name="config.channel"
                  value={typeof newAction.config.channel === 'string' ? newAction.config.channel : 'email'}
                  onChange={handleActionChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  {channelOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {(newAction.type === 'email' || (newAction.type === 'notification' && newAction.config.channel === 'email')) && (
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700">
                  Subject
                </label>
                <input
                  type="text"
                  id="subject"
                  name="config.subject"
                  value={typeof newAction.config.subject === 'string' ? newAction.config.subject : ''}
                  onChange={handleActionChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            )}

            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700">
                Message
              </label>
              <textarea
                id="message"
                name="config.message"
                value={typeof newAction.config.message === 'string' ? newAction.config.message : ''}
                onChange={handleActionChange}
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-gray-500">
                You can use placeholders like {'{visitor.name}'}, {'{host.name}'}, {'{visit.date}'}, etc.
              </p>
            </div>
          </div>
        )}

        {newAction.type === 'webhook' && (
          <div>
            <label htmlFor="url" className="block text-sm font-medium text-gray-700">
              Webhook URL
            </label>
            <input
              type="url"
              id="url"
              name="config.url"
              value={typeof newAction.config.url === 'string' ? newAction.config.url : ''}
              onChange={handleActionChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="https://"
            />
          </div>
        )}

        <div className="flex justify-end">
          <button
            type="button"
            onClick={handleAddAction}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Action
          </button>
        </div>

        <div className="space-y-2">
          {(formData.actions || []).map((action, index) => (
            <div key={index} className="bg-gray-50 p-3 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">{getActionTypeLabel(action.type)}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveAction(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <Minus className="h-4 w-4" />
                </button>
              </div>

              <div className="text-sm">
                {action.type === 'notification' && (
                  <>
                    <p>
                      <span className="text-gray-500">Recipient:</span> {String(action.config.recipient)}
                      {action.config.recipient === 'custom' && action.config.custom_recipient ? ` (${String(action.config.custom_recipient)})` : ''}
                    </p>
                    <p>
                      <span className="text-gray-500">Channel:</span> {String(action.config.channel)}
                    </p>
                    {action.config.channel === 'email' && action.config.subject && (
                      <p>
                        <span className="text-gray-500">Subject:</span> {String(action.config.subject)}
                      </p>
                    )}
                    <p>
                      <span className="text-gray-500">Message:</span> {String(action.config.message)}
                    </p>
                  </>
                )}

                {action.type === 'email' && (
                  <>
                    <p>
                      <span className="text-gray-500">Recipient:</span> {String(action.config.recipient)}
                      {action.config.recipient === 'custom' && action.config.custom_recipient ? ` (${String(action.config.custom_recipient)})` : ''}
                    </p>
                    <p>
                      <span className="text-gray-500">Subject:</span> {String(action.config.subject)}
                    </p>
                    <p>
                      <span className="text-gray-500">Message:</span> {String(action.config.message)}
                    </p>
                  </>
                )}

                {action.type === 'sms' && (
                  <>
                    <p>
                      <span className="text-gray-500">Recipient:</span> {String(action.config.recipient)}
                      {action.config.recipient === 'custom' && action.config.custom_recipient ? ` (${String(action.config.custom_recipient)})` : ''}
                    </p>
                    <p>
                      <span className="text-gray-500">Message:</span> {String(action.config.message)}
                    </p>
                  </>
                )}

                {action.type === 'webhook' && action.config.url ? (
                  <p>
                    <span className="text-gray-500">URL:</span> {String(action.config.url)}
                  </p>
                )}
              </div>
            </div>
          ))}

          {(formData.actions || []).length === 0 && (
            <p className="text-sm text-gray-500">No actions added yet</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? 'Saving...' : isEditing ? 'Update Workflow' : 'Create Workflow'}
        </button>
      </div>
    </div>
  )
}
