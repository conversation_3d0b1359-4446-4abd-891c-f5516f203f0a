'use client'

import { useState, useEffect, useRef } from 'react'
import { BrandingSettings, EmailTemplate, EmailTemplates, LandingPageSettings } from '@/types/admin'
import { updateBrandingSettings } from '@/lib/admin/client'
import { Upload, X, Eye, EyeOff, RefreshCw, Check } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'

interface BrandingFormProps {
  orgId: string
  settings?: BrandingSettings | null
  onSuccess: () => void
}

export default function BrandingForm({
  orgId,
  settings,
  onSuccess
}: BrandingFormProps) {
  const supabase = createClient()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const faviconInputRef = useRef<HTMLInputElement>(null)
  const bgImageInputRef = useRef<HTMLInputElement>(null)
  
  const [formData, setFormData] = useState<Partial<BrandingSettings>>({
    org_id: orgId,
    logo_url: '',
    favicon_url: '',
    primary_color: '#007bff',
    secondary_color: '#6c757d',
    accent_color: '#20c997',
    font_family: 'system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    email_templates: {
      invitation: {
        subject: 'Invitation to join {organization_name}',
        body: '<p>Hello,</p><p>You have been invited to join {organization_name}. Please click the link below to accept the invitation:</p><p><a href="{invitation_link}">Accept Invitation</a></p><p>Regards,<br>{organization_name}</p>',
        is_html: true
      },
      pre_registration_confirmation: {
        subject: 'Visit Confirmation for {organization_name}',
        body: '<p>Hello {visitor_name},</p><p>Your visit to {organization_name} has been confirmed for {visit_date} at {visit_time}.</p><p>Host: {host_name}</p><p>Location: {location_name}</p><p>Please arrive 10 minutes before your scheduled time.</p><p>Regards,<br>{organization_name}</p>',
        is_html: true
      },
      visitor_check_in_notification: {
        subject: 'Visitor Check-in Notification',
        body: '<p>Hello {host_name},</p><p>Your visitor {visitor_name} has checked in at {location_name} at {check_in_time}.</p><p>Regards,<br>{organization_name}</p>',
        is_html: true
      },
      visitor_check_out_notification: {
        subject: 'Visitor Check-out Notification',
        body: '<p>Hello {host_name},</p><p>Your visitor {visitor_name} has checked out from {location_name} at {check_out_time}.</p><p>Regards,<br>{organization_name}</p>',
        is_html: true
      }
    },
    landing_page_settings: {
      welcome_message: 'Welcome to our visitor management system',
      background_image_url: '',
      show_logo: true,
      custom_css: ''
    }
  })
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  const [faviconPreview, setFaviconPreview] = useState<string | null>(null)
  const [bgImagePreview, setBgImagePreview] = useState<string | null>(null)
  const [activeEmailTemplate, setActiveEmailTemplate] = useState<keyof EmailTemplates>('invitation')
  const [previewHtml, setPreviewHtml] = useState(false)
  
  // Populate form with settings data if editing
  useEffect(() => {
    if (settings) {
      setFormData({
        ...settings,
        org_id: orgId
      })
      
      if (settings.logo_url) {
        setLogoPreview(settings.logo_url)
      }
      
      if (settings.favicon_url) {
        setFaviconPreview(settings.favicon_url)
      }
      
      if (settings.landing_page_settings?.background_image_url) {
        setBgImagePreview(settings.landing_page_settings.background_image_url)
      }
    }
  }, [settings, orgId])
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      
      if (name.startsWith('landing_page_settings.')) {
        const field = name.replace('landing_page_settings.', '')
        
        // Ensure show_logo is always a boolean
        if (field === 'show_logo') {
          setFormData(prev => {
            const landingPageSettings = {
              ...(prev.landing_page_settings || {}),
              show_logo: checked
            }
            
            return {
              ...prev,
              landing_page_settings: landingPageSettings
            }
          })
        } else {
          setFormData(prev => ({
            ...prev,
            landing_page_settings: {
              ...(prev.landing_page_settings || {}),
              [field]: checked
            } as LandingPageSettings
          }))
        }
      } else {
        setFormData(prev => ({
          ...prev,
          [name]: checked
        }))
      }
    } else if (name.startsWith('email_templates.')) {
      const [, template, field] = name.split('.')
      
      setFormData(prev => ({
        ...prev,
        email_templates: {
          ...(prev.email_templates || {}),
          [template]: {
            ...(prev.email_templates?.[template as keyof EmailTemplates] || {}),
            [field]: value
          }
        }
      }))
    } else if (name.startsWith('landing_page_settings.')) {
      const field = name.replace('landing_page_settings.', '')

      setFormData(prev => ({
        ...prev,
        landing_page_settings: {
          ...(prev.landing_page_settings || {}),
          [field]: value
        } as LandingPageSettings
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }
  
  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      return
    }
    
    const file = e.target.files[0]
    const fileExt = file.name.split('.').pop()
    const fileName = `${orgId}-logo-${Math.random().toString(36).substring(2, 15)}.${fileExt}`
    const filePath = `branding/${fileName}`
    
    // Create a preview
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target?.result) {
        setLogoPreview(e.target.result as string)
      }
    }
    reader.readAsDataURL(file)
    
    // Upload to Supabase Storage
    try {
      const { error: uploadError } = await supabase.storage
        .from('assets')
        .upload(filePath, file)
      
      if (uploadError) {
        throw uploadError
      }
      
      // Get public URL
      const { data } = supabase.storage.from('assets').getPublicUrl(filePath)
      
      if (data) {
        setFormData(prev => ({
          ...prev,
          logo_url: data.publicUrl
        }))
      }
    } catch (error) {
      console.error('Error uploading logo:', error)
      setError('Failed to upload logo. Please try again.')
    }
  }
  
  const handleFaviconUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      return
    }
    
    const file = e.target.files[0]
    const fileExt = file.name.split('.').pop()
    const fileName = `${orgId}-favicon-${Math.random().toString(36).substring(2, 15)}.${fileExt}`
    const filePath = `branding/${fileName}`
    
    // Create a preview
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target?.result) {
        setFaviconPreview(e.target.result as string)
      }
    }
    reader.readAsDataURL(file)
    
    // Upload to Supabase Storage
    try {
      const { error: uploadError } = await supabase.storage
        .from('assets')
        .upload(filePath, file)
      
      if (uploadError) {
        throw uploadError
      }
      
      // Get public URL
      const { data } = supabase.storage.from('assets').getPublicUrl(filePath)
      
      if (data) {
        setFormData(prev => ({
          ...prev,
          favicon_url: data.publicUrl
        }))
      }
    } catch (error) {
      console.error('Error uploading favicon:', error)
      setError('Failed to upload favicon. Please try again.')
    }
  }
  
  const handleBgImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      return
    }
    
    const file = e.target.files[0]
    const fileExt = file.name.split('.').pop()
    const fileName = `${orgId}-bg-${Math.random().toString(36).substring(2, 15)}.${fileExt}`
    const filePath = `branding/${fileName}`
    
    // Create a preview
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target?.result) {
        setBgImagePreview(e.target.result as string)
      }
    }
    reader.readAsDataURL(file)
    
    // Upload to Supabase Storage
    try {
      const { error: uploadError } = await supabase.storage
        .from('assets')
        .upload(filePath, file)
      
      if (uploadError) {
        throw uploadError
      }
      
      // Get public URL
      const { data } = supabase.storage.from('assets').getPublicUrl(filePath)
      
      if (data) {
        setFormData(prev => ({
          ...prev,
          landing_page_settings: {
            ...(prev.landing_page_settings || {}),
            background_image_url: data.publicUrl
          } as LandingPageSettings
        }))
      }
    } catch (error) {
      console.error('Error uploading background image:', error)
      setError('Failed to upload background image. Please try again.')
    }
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      const result = await updateBrandingSettings(orgId, formData)
      
      if (result) {
        setSuccess('Branding settings updated successfully')
        onSuccess()
      } else {
        setError('Failed to update branding settings. Please try again.')
      }
    } catch (err) {
      console.error('Error saving branding settings:', err)
      setError('Failed to save branding settings. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  const getEmailTemplate = (templateKey: keyof EmailTemplates): EmailTemplate => {
    return (
      formData.email_templates?.[templateKey] || {
        subject: '',
        body: '',
        is_html: true
      }
    )
  }
  
  const getLandingPageSettings = (): LandingPageSettings => {
    return {
      welcome_message: formData.landing_page_settings?.welcome_message || '',
      background_image_url: formData.landing_page_settings?.background_image_url || '',
      show_logo: formData.landing_page_settings?.show_logo !== false, // Default to true if undefined
      custom_css: formData.landing_page_settings?.custom_css || ''
    }
  }
  
  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md flex items-center">
          <Check className="h-5 w-5 mr-2" />
          {success}
        </div>
      )}
      
      <div className="space-y-6">
        <h3 className="text-lg font-medium">Brand Identity</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Logo
            </label>
            <div className="mt-1 flex items-center space-x-4">
              <div className="w-32 h-32 border border-gray-300 rounded-md flex items-center justify-center overflow-hidden bg-white">
                {logoPreview ? (
                  <img 
                    src={logoPreview} 
                    alt="Logo preview" 
                    className="max-w-full max-h-full object-contain"
                  />
                ) : (
                  <span className="text-gray-400">No logo</span>
                )}
              </div>
              <div className="space-y-2">
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Logo
                </button>
                {logoPreview && (
                  <button
                    type="button"
                    onClick={() => {
                      setLogoPreview(null)
                      setFormData(prev => ({ ...prev, logo_url: '' }))
                    }}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-red-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Remove
                  </button>
                )}
              </div>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleLogoUpload}
                accept="image/*"
                className="hidden"
              />
            </div>
            <p className="mt-2 text-sm text-gray-500">
              Recommended size: 200x200 pixels. PNG or SVG with transparent background.
            </p>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Favicon
            </label>
            <div className="mt-1 flex items-center space-x-4">
              <div className="w-16 h-16 border border-gray-300 rounded-md flex items-center justify-center overflow-hidden bg-white">
                {faviconPreview ? (
                  <img 
                    src={faviconPreview} 
                    alt="Favicon preview" 
                    className="max-w-full max-h-full object-contain"
                  />
                ) : (
                  <span className="text-gray-400 text-xs">No favicon</span>
                )}
              </div>
              <div className="space-y-2">
                <button
                  type="button"
                  onClick={() => faviconInputRef.current?.click()}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Favicon
                </button>
                {faviconPreview && (
                  <button
                    type="button"
                    onClick={() => {
                      setFaviconPreview(null)
                      setFormData(prev => ({ ...prev, favicon_url: '' }))
                    }}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-red-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Remove
                  </button>
                )}
              </div>
              <input
                type="file"
                ref={faviconInputRef}
                onChange={handleFaviconUpload}
                accept="image/x-icon,image/png,image/svg+xml"
                className="hidden"
              />
            </div>
            <p className="mt-2 text-sm text-gray-500">
              Recommended size: 32x32 or 64x64 pixels. ICO, PNG, or SVG format.
            </p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="primary_color" className="block text-sm font-medium text-gray-700">
              Primary Color
            </label>
            <div className="mt-1 flex items-center space-x-2">
              <input
                type="color"
                id="primary_color"
                name="primary_color"
                value={formData.primary_color || '#007bff'}
                onChange={handleChange}
                className="h-8 w-8 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={formData.primary_color || '#007bff'}
                onChange={handleChange}
                name="primary_color"
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Used for primary buttons, links, and accents
            </p>
          </div>
          
          <div>
            <label htmlFor="secondary_color" className="block text-sm font-medium text-gray-700">
              Secondary Color
            </label>
            <div className="mt-1 flex items-center space-x-2">
              <input
                type="color"
                id="secondary_color"
                name="secondary_color"
                value={formData.secondary_color || '#6c757d'}
                onChange={handleChange}
                className="h-8 w-8 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={formData.secondary_color || '#6c757d'}
                onChange={handleChange}
                name="secondary_color"
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Used for secondary buttons and less prominent elements
            </p>
          </div>
          
          <div>
            <label htmlFor="accent_color" className="block text-sm font-medium text-gray-700">
              Accent Color
            </label>
            <div className="mt-1 flex items-center space-x-2">
              <input
                type="color"
                id="accent_color"
                name="accent_color"
                value={formData.accent_color || '#20c997'}
                onChange={handleChange}
                className="h-8 w-8 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={formData.accent_color || '#20c997'}
                onChange={handleChange}
                name="accent_color"
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Used for highlighting special features or elements
            </p>
          </div>
        </div>
        
        <div>
          <label htmlFor="font_family" className="block text-sm font-medium text-gray-700">
            Font Family
          </label>
          <select
            id="font_family"
            name="font_family"
            value={formData.font_family || 'system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'}
            onChange={handleChange}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value='system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'>
              System Default
            </option>
            <option value='"Roboto", sans-serif'>Roboto</option>
            <option value='"Open Sans", sans-serif'>Open Sans</option>
            <option value='"Lato", sans-serif'>Lato</option>
            <option value='"Montserrat", sans-serif'>Montserrat</option>
            <option value='"Poppins", sans-serif'>Poppins</option>
            <option value='"Source Sans Pro", sans-serif'>Source Sans Pro</option>
            <option value='"Nunito", sans-serif'>Nunito</option>
          </select>
          <p className="mt-1 text-xs text-gray-500">
            The font family used throughout the application
          </p>
        </div>
      </div>
      
      <div className="space-y-6">
        <h3 className="text-lg font-medium">Email Templates</h3>
        
        <div className="bg-white border border-gray-200 rounded-md overflow-hidden">
          <div className="bg-gray-50 border-b border-gray-200 px-4 py-3 flex flex-wrap gap-2">
            <button
              type="button"
              onClick={() => setActiveEmailTemplate('invitation')}
              className={`px-3 py-1 text-sm rounded-md ${
                activeEmailTemplate === 'invitation'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              Invitation
            </button>
            <button
              type="button"
              onClick={() => setActiveEmailTemplate('pre_registration_confirmation')}
              className={`px-3 py-1 text-sm rounded-md ${
                activeEmailTemplate === 'pre_registration_confirmation'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              Pre-registration Confirmation
            </button>
            <button
              type="button"
              onClick={() => setActiveEmailTemplate('visitor_check_in_notification')}
              className={`px-3 py-1 text-sm rounded-md ${
                activeEmailTemplate === 'visitor_check_in_notification'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              Check-in Notification
            </button>
            <button
              type="button"
              onClick={() => setActiveEmailTemplate('visitor_check_out_notification')}
              className={`px-3 py-1 text-sm rounded-md ${
                activeEmailTemplate === 'visitor_check_out_notification'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              Check-out Notification
            </button>
          </div>
          
          <div className="p-4 space-y-4">
            <div>
              <label 
                htmlFor={`email_templates.${activeEmailTemplate}.subject`} 
                className="block text-sm font-medium text-gray-700"
              >
                Subject
              </label>
              <input
                type="text"
                id={`email_templates.${activeEmailTemplate}.subject`}
                name={`email_templates.${activeEmailTemplate}.subject`}
                value={getEmailTemplate(activeEmailTemplate).subject}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            
            <div>
              <div className="flex items-center justify-between">
                <label 
                  htmlFor={`email_templates.${activeEmailTemplate}.body`} 
                  className="block text-sm font-medium text-gray-700"
                >
                  Body
                </label>
                <button
                  type="button"
                  onClick={() => setPreviewHtml(!previewHtml)}
                  className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200"
                >
                  {previewHtml ? (
                    <>
                      <EyeOff className="h-3 w-3 mr-1" />
                      Hide Preview
                    </>
                  ) : (
                    <>
                      <Eye className="h-3 w-3 mr-1" />
                      Preview
                    </>
                  )}
                </button>
              </div>
              
              {previewHtml ? (
                <div 
                  className="mt-1 p-4 border border-gray-300 rounded-md bg-white min-h-[200px] overflow-auto"
                  dangerouslySetInnerHTML={{ 
                    __html: getEmailTemplate(activeEmailTemplate).body 
                  }}
                />
              ) : (
                <textarea
                  id={`email_templates.${activeEmailTemplate}.body`}
                  name={`email_templates.${activeEmailTemplate}.body`}
                  value={getEmailTemplate(activeEmailTemplate).body}
                  onChange={handleChange}
                  rows={8}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
                />
              )}
              
              <p className="mt-1 text-xs text-gray-500">
                You can use HTML for formatting and the following placeholders: {'{organization_name}'}, {'{visitor_name}'}, {'{host_name}'}, {'{location_name}'}, {'{visit_date}'}, {'{visit_time}'}, {'{check_in_time}'}, {'{check_out_time}'}, {'{invitation_link}'}
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="space-y-6">
        <h3 className="text-lg font-medium">Landing Page</h3>
        
        <div>
          <label htmlFor="landing_page_settings.welcome_message" className="block text-sm font-medium text-gray-700">
            Welcome Message
          </label>
          <input
            type="text"
            id="landing_page_settings.welcome_message"
            name="landing_page_settings.welcome_message"
            value={getLandingPageSettings().welcome_message}
            onChange={handleChange}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
          <p className="mt-1 text-xs text-gray-500">
            The welcome message displayed on the landing page
          </p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Background Image
          </label>
          <div className="mt-1 flex items-center space-x-4">
            <div className="w-32 h-20 border border-gray-300 rounded-md flex items-center justify-center overflow-hidden bg-white">
              {bgImagePreview ? (
                <img 
                  src={bgImagePreview} 
                  alt="Background preview" 
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-gray-400 text-xs">No image</span>
              )}
            </div>
            <div className="space-y-2">
              <button
                type="button"
                onClick={() => bgImageInputRef.current?.click()}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Image
              </button>
              {bgImagePreview && (
                <button
                  type="button"
                  onClick={() => {
                    setBgImagePreview(null)
                    setFormData(prev => ({
                      ...prev,
                      landing_page_settings: {
                        ...(prev.landing_page_settings || {}),
                        background_image_url: ''
                      } as LandingPageSettings
                    }))
                  }}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-red-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <X className="h-4 w-4 mr-2" />
                  Remove
                </button>
              )}
            </div>
            <input
              type="file"
              ref={bgImageInputRef}
              onChange={handleBgImageUpload}
              accept="image/*"
              className="hidden"
            />
          </div>
          <p className="mt-2 text-sm text-gray-500">
            Recommended size: 1920x1080 pixels. JPG or PNG format.
          </p>
        </div>
        
        <div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="landing_page_settings.show_logo"
              name="landing_page_settings.show_logo"
              checked={getLandingPageSettings().show_logo}
              onChange={handleChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="landing_page_settings.show_logo" className="ml-2 block text-sm text-gray-700">
              Show logo on landing page
            </label>
          </div>
          <p className="mt-1 text-xs text-gray-500 ml-6">
            Display your organization logo on the landing page
          </p>
        </div>
        
        <div>
          <label htmlFor="landing_page_settings.custom_css" className="block text-sm font-medium text-gray-700">
            Custom CSS
          </label>
          <textarea
            id="landing_page_settings.custom_css"
            name="landing_page_settings.custom_css"
            value={getLandingPageSettings().custom_css || ''}
            onChange={handleChange}
            rows={5}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
            placeholder=".landing-page { /* your custom styles */ }"
          />
          <p className="mt-1 text-xs text-gray-500">
            Add custom CSS to further customize the landing page appearance
          </p>
        </div>
      </div>
      
      <div className="pt-5 border-t border-gray-200">
        <div className="flex justify-end">
          <button
            type="button"
            onClick={() => window.history.back()}
            className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <RefreshCw className="animate-spin h-4 w-4 mr-2" />
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </button>
        </div>
      </div>
    </form>
  )
}
