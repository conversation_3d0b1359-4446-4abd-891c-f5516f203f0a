'use client'

import { useState } from 'react'
import { deleteLocation } from '@/lib/admin/client'

interface DeleteLocationConfirmProps {
  locationId: string
  locationName: string
  onSuccess: () => void
  onCancel: () => void
}

export default function DeleteLocationConfirm({
  locationId,
  locationName,
  onSuccess,
  onCancel
}: DeleteLocationConfirmProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const handleDelete = async () => {
    setLoading(true)
    setError(null)
    
    try {
      await deleteLocation(locationId)
      onSuccess()
    } catch (err) {
      console.error('Error deleting location:', err)
      setError('Failed to delete location. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <div className="p-4">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">
          {error}
        </div>
      )}
      
      <div className="text-center mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Delete Location</h3>
        <p className="text-sm text-gray-500">
          Are you sure you want to delete <span className="font-semibold">{locationName}</span>?
          This action cannot be undone.
        </p>
      </div>
      
      <div className="flex justify-center space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleDelete}
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
        >
          {loading ? 'Deleting...' : 'Delete Location'}
        </button>
      </div>
    </div>
  )
}
