'use client'

import { useState, useEffect } from 'react'
import { Location, LocationGroup, LocationSettings } from '@/types/admin'
import { 
  createLocation, 
  updateLocation, 
  getLocationGroups, 
  getLocations 
} from '@/lib/admin/client'

interface LocationFormProps {
  orgId: string
  location?: Location
  onSuccess: () => void
  onCancel: () => void
}

export default function LocationForm({
  orgId,
  location,
  onSuccess,
  onCancel
}: LocationFormProps) {
  const isEditing = !!location
  
  const [formData, setFormData] = useState<Partial<Location>>({
    org_id: orgId,
    name: '',
    address: '',
    city: '',
    state: '',
    country: '',
    postal_code: '',
    phone: '',
    email: '',
    timezone: 'UTC',
    location_type: 'office',
    is_active: true,
    working_hours: {
      monday: { is_open: true, open_time: '09:00', close_time: '17:00' },
      tuesday: { is_open: true, open_time: '09:00', close_time: '17:00' },
      wednesday: { is_open: true, open_time: '09:00', close_time: '17:00' },
      thursday: { is_open: true, open_time: '09:00', close_time: '17:00' },
      friday: { is_open: true, open_time: '09:00', close_time: '17:00' },
      saturday: { is_open: false },
      sunday: { is_open: false }
    },
    settings: {
      require_host_approval: true,
      allow_walk_ins: true,
      require_photo: true,
      require_id: false,
      check_in_methods: ['self', 'receptionist'],
      default_check_in_method: 'self',
      notification_settings: {
        notify_on_check_in: true,
        notify_on_check_out: true,
        notify_on_pre_registration: true,
        default_notification_channels: ['email', 'in_app']
      }
    }
  })
  
  const [locationGroups, setLocationGroups] = useState<LocationGroup[]>([])
  const [parentLocations, setParentLocations] = useState<Location[]>([])
  
  const [loading, setLoading] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Fetch location groups and parent locations
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [groups, locations] = await Promise.all([
          getLocationGroups(orgId),
          getLocations(orgId, true)
        ])
        
        setLocationGroups(groups)
        
        // Filter out the current location from potential parents to avoid circular references
        if (location) {
          setParentLocations(locations.filter((loc: Location) => loc.id !== location.id))
        } else {
          setParentLocations(locations)
        }
        
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching location data:', err)
        setError('Failed to load location data. Please try again.')
        setIsLoading(false)
      }
    }
    
    fetchData()
  }, [orgId, location])
  
  // Populate form with location data if editing
  useEffect(() => {
    if (location) {
      setFormData({
        ...location,
        org_id: orgId
      })
    }
  }, [location, orgId])
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }
  
  const updateCheckInMethods = (method: string, isChecked: boolean) => {
    setFormData(prev => {
      const currentMethods = prev.settings?.check_in_methods || []
      const newMethods = isChecked
        ? [...currentMethods, method]
        : currentMethods.filter(m => m !== method)
      
      return {
        ...prev,
        settings: {
          ...(prev.settings || {}),
          check_in_methods: newMethods
        } as LocationSettings
      }
    })
  }
  
  const updateDefaultCheckInMethod = (method: 'self' | 'receptionist' | 'kiosk') => {
    setFormData(prev => {
      return {
        ...prev,
        settings: {
          ...(prev.settings || {}),
          default_check_in_method: method
        } as LocationSettings
      }
    })
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    
    try {
      if (isEditing && location) {
        // Update existing location
        await updateLocation(location.id, formData)
      } else {
        // Create new location
        await createLocation(formData as Omit<Location, 'id' | 'created_at' | 'updated_at'>)
      }
      
      onSuccess()
    } catch (err) {
      console.error('Error saving location:', err)
      setError('Failed to save location. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        <span className="ml-2">Loading location data...</span>
      </div>
    )
  }
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Basic Information</h3>
        
        <div className="grid grid-cols-1 gap-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Location Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="location_type" className="block text-sm font-medium text-gray-700">
                Location Type
              </label>
              <select
                id="location_type"
                name="location_type"
                value={formData.location_type || 'office'}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="headquarters">Headquarters</option>
                <option value="branch">Branch</option>
                <option value="office">Office</option>
                <option value="facility">Facility</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="parent_id" className="block text-sm font-medium text-gray-700">
                Parent Location
              </label>
              <select
                id="parent_id"
                name="parent_id"
                value={formData.parent_id || ''}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">None (Top-level Location)</option>
                {parentLocations.map(parent => (
                  <option key={parent.id} value={parent.id}>
                    {parent.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="group_id" className="block text-sm font-medium text-gray-700">
                Location Group
              </label>
              <select
                id="group_id"
                name="group_id"
                value={formData.group_id || ''}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">None</option>
                {locationGroups.map(group => (
                  <option key={group.id} value={group.id}>
                    {group.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label htmlFor="capacity" className="block text-sm font-medium text-gray-700">
                Capacity
              </label>
              <input
                type="number"
                id="capacity"
                name="capacity"
                value={formData.capacity || ''}
                onChange={handleChange}
                min="0"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              name="is_active"
              checked={formData.is_active !== false}
              onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
              Active Location
            </label>
          </div>
          
          <div>
            <label htmlFor="address" className="block text-sm font-medium text-gray-700">
              Address
            </label>
            <textarea
              id="address"
              name="address"
              value={formData.address || ''}
              onChange={handleChange}
              rows={3}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="city" className="block text-sm font-medium text-gray-700">
              City
            </label>
            <input
              type="text"
              id="city"
              name="city"
              value={formData.city || ''}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          
          <div>
            <label htmlFor="state" className="block text-sm font-medium text-gray-700">
              State/Province
            </label>
            <input
              type="text"
              id="state"
              name="state"
              value={formData.state || ''}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="country" className="block text-sm font-medium text-gray-700">
              Country
            </label>
            <input
              type="text"
              id="country"
              name="country"
              value={formData.country || ''}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          
          <div>
            <label htmlFor="postal_code" className="block text-sm font-medium text-gray-700">
              Postal Code
            </label>
            <input
              type="text"
              id="postal_code"
              name="postal_code"
              value={formData.postal_code || ''}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Contact Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone || ''}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email || ''}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
        </div>
        
        <div>
          <label htmlFor="timezone" className="block text-sm font-medium text-gray-700">
            Timezone
          </label>
          <select
            id="timezone"
            name="timezone"
            value={formData.timezone || 'UTC'}
            onChange={handleChange}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="UTC">UTC</option>
            <option value="America/New_York">Eastern Time (ET)</option>
            <option value="America/Chicago">Central Time (CT)</option>
            <option value="America/Denver">Mountain Time (MT)</option>
            <option value="America/Los_Angeles">Pacific Time (PT)</option>
            <option value="Europe/London">London (GMT)</option>
            <option value="Europe/Paris">Paris (CET)</option>
            <option value="Asia/Tokyo">Tokyo (JST)</option>
            <option value="Asia/Shanghai">China (CST)</option>
            <option value="Asia/Kolkata">India (IST)</option>
            <option value="Australia/Sydney">Sydney (AEST)</option>
          </select>
        </div>
        
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Check-in Methods</h4>
          <div className="space-y-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="check_in_self"
                checked={formData.settings?.check_in_methods?.includes('self') ?? true}
                onChange={(e) => updateCheckInMethods('self', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="check_in_self" className="ml-2 block text-sm text-gray-900">
                Self Check-in
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="check_in_receptionist"
                checked={formData.settings?.check_in_methods?.includes('receptionist') ?? true}
                onChange={(e) => updateCheckInMethods('receptionist', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="check_in_receptionist" className="ml-2 block text-sm text-gray-900">
                Receptionist Check-in
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="check_in_kiosk"
                checked={formData.settings?.check_in_methods?.includes('kiosk') ?? false}
                onChange={(e) => updateCheckInMethods('kiosk', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="check_in_kiosk" className="ml-2 block text-sm text-gray-900">
                Kiosk Check-in
              </label>
            </div>
          </div>
          
          <div className="mt-3">
            <label htmlFor="default_check_in_method" className="block text-sm font-medium text-gray-700">
              Default Check-in Method
            </label>
            <select
              id="default_check_in_method"
              name="default_check_in_method"
              value={formData.settings?.default_check_in_method || 'self'}
              onChange={(e) => updateDefaultCheckInMethod(e.target.value as 'self' | 'receptionist' | 'kiosk')}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              {(formData.settings?.check_in_methods || ['self', 'receptionist']).map(method => (
                <option key={method} value={method}>
                  {method === 'self' ? 'Self Check-in' : 
                   method === 'receptionist' ? 'Receptionist Check-in' : 
                   'Kiosk Check-in'}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? 'Saving...' : isEditing ? 'Update Location' : 'Create Location'}
        </button>
      </div>
    </form>
  )
}
