'use client'

import { useState, useEffect } from 'react'
import { CustomField, ValidationRule } from '@/types/admin'
import { createCustomField, updateCustomField } from '@/lib/admin/client'
import { Plus, Minus } from 'lucide-react'

interface CustomFieldFormProps {
  orgId: string
  field?: CustomField
  onSuccess: () => void
  onCancel: () => void
}

export default function CustomFieldForm({
  orgId,
  field,
  onSuccess,
  onCancel
}: CustomFieldFormProps) {
  const isEditing = !!field
  
  const [formData, setFormData] = useState<Partial<CustomField>>({
    org_id: orgId,
    name: '',
    label: '',
    type: 'text',
    entity_type: 'visitor',
    options: [],
    is_required: false,
    is_visible: true,
    validation_rules: [],
    default_value: '',
    placeholder: '',
    help_text: '',
    order: 0
  })
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [optionInput, setOptionInput] = useState('')
  const [validationRules, setValidationRules] = useState<ValidationRule[]>([])
  const [newValidationRule, setNewValidationRule] = useState<ValidationRule>({
    type: 'min',
    value: '',
    message: ''
  })
  
  // Populate form with field data if editing
  useEffect(() => {
    if (field) {
      setFormData({
        ...field,
        org_id: orgId
      })
      
      if (field.validation_rules) {
        setValidationRules(field.validation_rules)
      }
    }
  }, [field, orgId])
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }
  
  const handleAddOption = () => {
    if (optionInput.trim() === '') return
    
    setFormData(prev => ({
      ...prev,
      options: [...(prev.options || []), optionInput.trim()]
    }))
    
    setOptionInput('')
  }
  
  const handleRemoveOption = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: (prev.options || []).filter((_, i) => i !== index)
    }))
  }
  
  const handleAddValidationRule = () => {
    if (!newValidationRule.message) {
      setError('Validation rule message is required')
      return
    }
    
    if (newValidationRule.type !== 'email' && newValidationRule.type !== 'url' && !newValidationRule.value) {
      setError('Validation rule value is required')
      return
    }
    
    setValidationRules(prev => [...prev, { ...newValidationRule }])
    
    setNewValidationRule({
      type: 'min',
      value: '',
      message: ''
    })
    
    setError(null)
  }
  
  const handleRemoveValidationRule = (index: number) => {
    setValidationRules(prev => prev.filter((_, i) => i !== index))
  }
  
  const handleValidationRuleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    
    setNewValidationRule(prev => ({
      ...prev,
      [name]: value
    }))
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    
    // Generate a name from the label if not provided
    if (!formData.name && formData.label) {
      formData.name = formData.label
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '_')
        .replace(/_+/g, '_')
        .replace(/^_|_$/g, '')
    }
    
    // Validate form
    if (!formData.name) {
      setError('Field name is required')
      setLoading(false)
      return
    }
    
    if (!formData.label) {
      setError('Field label is required')
      setLoading(false)
      return
    }
    
    if (!formData.entity_type) {
      setError('Entity type is required')
      setLoading(false)
      return
    }
    
    if (!formData.type) {
      setError('Field type is required')
      setLoading(false)
      return
    }
    
    // Check if options are provided for select, multiselect, and radio types
    if (['select', 'multiselect', 'radio'].includes(formData.type) && (!formData.options || formData.options.length === 0)) {
      setError('Options are required for this field type')
      setLoading(false)
      return
    }
    
    // Add validation rules to form data
    formData.validation_rules = validationRules
    
    try {
      if (isEditing && field) {
        // Update existing field
        await updateCustomField(field.id, formData)
      } else {
        // Create new field
        await createCustomField(formData as Omit<CustomField, 'id' | 'created_at' | 'updated_at'>)
      }
      
      onSuccess()
    } catch (err) {
      console.error('Error saving custom field:', err)
      setError('Failed to save custom field. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  const fieldTypeOptions = [
    { value: 'text', label: 'Text' },
    { value: 'number', label: 'Number' },
    { value: 'date', label: 'Date' },
    { value: 'time', label: 'Time' },
    { value: 'datetime', label: 'Date & Time' },
    { value: 'select', label: 'Dropdown' },
    { value: 'multiselect', label: 'Multi-select' },
    { value: 'checkbox', label: 'Checkbox' },
    { value: 'radio', label: 'Radio Button' },
    { value: 'textarea', label: 'Text Area' }
  ]
  
  const entityTypeOptions = [
    { value: 'visitor', label: 'Visitor' },
    { value: 'visit', label: 'Visit' },
    { value: 'host', label: 'Host' },
    { value: 'location', label: 'Location' }
  ]
  
  const validationTypeOptions = [
    { value: 'min', label: 'Minimum Value/Length' },
    { value: 'max', label: 'Maximum Value/Length' },
    { value: 'regex', label: 'Regular Expression' },
    { value: 'email', label: 'Email Format' },
    { value: 'url', label: 'URL Format' },
    { value: 'custom', label: 'Custom Validation' }
  ]
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Basic Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="label" className="block text-sm font-medium text-gray-700">
              Field Label *
            </label>
            <input
              type="text"
              id="label"
              name="label"
              value={formData.label || ''}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            <p className="mt-1 text-xs text-gray-500">
              The label displayed to users
            </p>
          </div>
          
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Field Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            <p className="mt-1 text-xs text-gray-500">
              The internal name used in the database (auto-generated if left blank)
            </p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="entity_type" className="block text-sm font-medium text-gray-700">
              Entity Type *
            </label>
            <select
              id="entity_type"
              name="entity_type"
              value={formData.entity_type || 'visitor'}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              {entityTypeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <p className="mt-1 text-xs text-gray-500">
              The entity this field belongs to
            </p>
          </div>
          
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700">
              Field Type *
            </label>
            <select
              id="type"
              name="type"
              value={formData.type || 'text'}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              {fieldTypeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <p className="mt-1 text-xs text-gray-500">
              The type of input field
            </p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="placeholder" className="block text-sm font-medium text-gray-700">
              Placeholder
            </label>
            <input
              type="text"
              id="placeholder"
              name="placeholder"
              value={formData.placeholder || ''}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            <p className="mt-1 text-xs text-gray-500">
              Placeholder text shown in the input field
            </p>
          </div>
          
          <div>
            <label htmlFor="default_value" className="block text-sm font-medium text-gray-700">
              Default Value
            </label>
            <input
              type="text"
              id="default_value"
              name="default_value"
              value={formData.default_value || ''}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            <p className="mt-1 text-xs text-gray-500">
              Default value for the field
            </p>
          </div>
        </div>
        
        <div>
          <label htmlFor="help_text" className="block text-sm font-medium text-gray-700">
            Help Text
          </label>
          <textarea
            id="help_text"
            name="help_text"
            value={formData.help_text || ''}
            onChange={handleChange}
            rows={2}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
          <p className="mt-1 text-xs text-gray-500">
            Help text displayed below the field
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_required"
              name="is_required"
              checked={formData.is_required || false}
              onChange={(e) => setFormData(prev => ({ ...prev, is_required: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_required" className="ml-2 block text-sm text-gray-700">
              Required Field
            </label>
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_visible"
              name="is_visible"
              checked={formData.is_visible !== false}
              onChange={(e) => setFormData(prev => ({ ...prev, is_visible: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_visible" className="ml-2 block text-sm text-gray-700">
              Visible
            </label>
          </div>
        </div>
      </div>
      
      {/* Options for select, multiselect, and radio types */}
      {['select', 'multiselect', 'radio'].includes(formData.type || '') && (
        <div className="space-y-4 border-t pt-4">
          <h3 className="text-lg font-medium">Options</h3>
          
          <div className="flex space-x-2">
            <input
              type="text"
              value={optionInput}
              onChange={(e) => setOptionInput(e.target.value)}
              placeholder="Enter an option"
              className="flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            <button
              type="button"
              onClick={handleAddOption}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>
          
          <div className="space-y-2">
            {(formData.options || []).map((option, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
                <span className="text-sm">{option}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveOption(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <Minus className="h-4 w-4" />
                </button>
              </div>
            ))}
            
            {(formData.options || []).length === 0 && (
              <p className="text-sm text-gray-500">No options added yet</p>
            )}
          </div>
        </div>
      )}
      
      {/* Validation Rules */}
      <div className="space-y-4 border-t pt-4">
        <h3 className="text-lg font-medium">Validation Rules</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="validation_type" className="block text-sm font-medium text-gray-700">
              Rule Type
            </label>
            <select
              id="validation_type"
              name="type"
              value={newValidationRule.type}
              onChange={handleValidationRuleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              {validationTypeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          {newValidationRule.type !== 'email' && newValidationRule.type !== 'url' && (
            <div>
              <label htmlFor="validation_value" className="block text-sm font-medium text-gray-700">
                Value
              </label>
              <input
                type="text"
                id="validation_value"
                name="value"
                value={newValidationRule.value || ''}
                onChange={handleValidationRuleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder={
                  newValidationRule.type === 'min' ? 'Minimum value/length' :
                  newValidationRule.type === 'max' ? 'Maximum value/length' :
                  newValidationRule.type === 'regex' ? 'Regular expression' :
                  'Value'
                }
              />
            </div>
          )}
          
          <div className={newValidationRule.type === 'email' || newValidationRule.type === 'url' ? 'md:col-span-2' : ''}>
            <label htmlFor="validation_message" className="block text-sm font-medium text-gray-700">
              Error Message
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                id="validation_message"
                name="message"
                value={newValidationRule.message || ''}
                onChange={handleValidationRuleChange}
                className="flex-1 mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Error message to display"
              />
              <button
                type="button"
                onClick={handleAddValidationRule}
                className="mt-1 inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
        
        <div className="space-y-2">
          {validationRules.map((rule, index) => (
            <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
              <div className="flex-1">
                <span className="text-sm font-medium">
                  {validationTypeOptions.find(opt => opt.value === rule.type)?.label || rule.type}
                </span>
                {rule.value && (
                  <span className="text-sm text-gray-500 ml-2">
                    ({rule.value})
                  </span>
                )}
                <p className="text-xs text-gray-500">{rule.message}</p>
              </div>
              <button
                type="button"
                onClick={() => handleRemoveValidationRule(index)}
                className="text-red-600 hover:text-red-800"
              >
                <Minus className="h-4 w-4" />
              </button>
            </div>
          ))}
          
          {validationRules.length === 0 && (
            <p className="text-sm text-gray-500">No validation rules added yet</p>
          )}
        </div>
      </div>
      
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? 'Saving...' : isEditing ? 'Update Field' : 'Create Field'}
        </button>
      </div>
    </form>
  )
}
