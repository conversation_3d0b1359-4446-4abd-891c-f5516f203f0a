'use client'

import { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>Axis, 
  <PERSON>A<PERSON>s, 
  CartesianGrid, 
  <PERSON>lt<PERSON>, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts'
import { createClientReportingService } from '@/lib/reporting/client'
import { VisitDurationData, ReportFilters } from '@/lib/reporting'

interface VisitDurationChartProps {
  orgId: string
}

// Duration ranges for grouping (in minutes)
const DURATION_RANGES = [
  { min: 0, max: 15, label: '0-15 min' },
  { min: 15, max: 30, label: '15-30 min' },
  { min: 30, max: 60, label: '30-60 min' },
  { min: 60, max: 120, label: '1-2 hours' },
  { min: 120, max: 240, label: '2-4 hours' },
  { min: 240, max: Infinity, label: '4+ hours' }
]

// Colors for pie chart
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

export default function VisitDurationChart({ orgId }: VisitDurationChartProps) {
  const [durationData, setDurationData] = useState<VisitDurationData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [averageDuration, setAverageDuration] = useState(0)
  const [medianDuration, setMedianDuration] = useState(0)
  const [durationDistribution, setDurationDistribution] = useState<Array<{
    range: string;
    count: number;
    percentage: number;
  }>>([])
  
  const reportingService = createClientReportingService()
  
  // Load visit duration data
  useEffect(() => {
    const loadDurationData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const filters: ReportFilters = {
          timeRange: 'month' // Use last month's data
        }
        
        const data = await reportingService.getVisitDurationData(orgId, filters)
        setDurationData(data)
        
        // Calculate average duration
        if (data.length > 0) {
          const totalDuration = data.reduce((sum, item) => sum + item.duration, 0)
          setAverageDuration(Math.round(totalDuration / data.length))
          
          // Calculate median duration
          const sortedDurations = [...data].sort((a, b) => a.duration - b.duration)
          const midIndex = Math.floor(sortedDurations.length / 2)
          setMedianDuration(
            sortedDurations.length % 2 === 0
              ? Math.round((sortedDurations[midIndex - 1].duration + sortedDurations[midIndex].duration) / 2)
              : sortedDurations[midIndex].duration
          )
          
          // Calculate duration distribution
          const distribution = DURATION_RANGES.map(range => {
            const count = data.filter(
              item => item.duration >= range.min && item.duration < range.max
            ).length
            
            return {
              range: range.label,
              count,
              percentage: Math.round((count / data.length) * 100)
            }
          })
          
          setDurationDistribution(distribution)
        }
      } catch (err) {
        console.error('Error loading visit duration data:', err)
        setError('Failed to load visit duration data')
      } finally {
        setLoading(false)
      }
    }
    
    loadDurationData()
  }, [orgId, reportingService])
  
  // Format duration for display
  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`
    } else {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return remainingMinutes > 0
        ? `${hours}h ${remainingMinutes}m`
        : `${hours}h`
    }
  }
  
  // Custom tooltip for the pie chart
  interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
      name: string;
      value: number;
      payload: {
        range: string;
        count: number;
        percentage: number;
      };
    }>;
  }
  
  const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-2 border border-gray-300 shadow-md rounded-md">
          <p className="font-medium">{data.range}</p>
          <p>{`Count: ${data.count} visits`}</p>
          <p>{`Percentage: ${data.percentage}%`}</p>
        </div>
      )
    }
    
    return null
  }
  
  if (loading) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="text-red-600 text-center h-64 flex items-center justify-center">
          <p>{error}</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-lg font-semibold mb-4">Visit Duration Analysis</h2>
      
      {durationData.length === 0 ? (
        <div className="text-gray-500 text-center h-64 flex items-center justify-center">
          <p>No visit duration data available.</p>
        </div>
      ) : (
        <div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500 mb-1">Average Duration</h3>
              <p className="text-2xl font-bold">{formatDuration(averageDuration)}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500 mb-1">Median Duration</h3>
              <p className="text-2xl font-bold">{formatDuration(medianDuration)}</p>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-md font-medium mb-2">Duration Distribution</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={durationDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                    nameKey="range"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {durationDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
          
          <div>
            <h3 className="text-md font-medium mb-2">Duration by Range</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={durationDistribution}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="range" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="count" name="Number of Visits" fill="#3B82F6" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
