'use client'

import { useState } from 'react'
import { createClientReportingService } from '@/lib/reporting/client'
import { ReportFilters, TimeRange } from '@/lib/reporting'

interface DataExporterProps {
  orgId: string
}

export default function DataExporter({ orgId }: DataExporterProps) {
  const [timeRange, setTimeRange] = useState<TimeRange>('month')
  const [dataType, setDataType] = useState<'traffic' | 'duration'>('traffic')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const reportingService = createClientReportingService()
  
  // Handle export
  const handleExport = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const filters: ReportFilters = {
        timeRange
      }
      
      let data
      let filename
      
      if (dataType === 'traffic') {
        data = await reportingService.getVisitorTraffic(orgId, filters)
        filename = `visitor-traffic-${timeRange}-${new Date().toISOString().split('T')[0]}.csv`
        
        // Convert data to CSV
        const csvContent = [
          'Date,Visitor Count',
          ...data.map(item => `${item.date},${item.count}`)
        ].join('\n')
        
        // Download CSV
        downloadCSV(csvContent, filename)
      } else {
        data = await reportingService.getVisitDurationData(orgId, filters)
        filename = `visit-duration-${timeRange}-${new Date().toISOString().split('T')[0]}.csv`
        
        // Convert data to CSV
        const csvContent = [
          'Visit ID,Duration (minutes)',
          ...data.map(item => `${item.visitId},${item.duration}`)
        ].join('\n')
        
        // Download CSV
        downloadCSV(csvContent, filename)
      }
    } catch (err) {
      console.error('Error exporting data:', err)
      setError('Failed to export data')
    } finally {
      setLoading(false)
    }
  }
  
  // Helper function to download CSV
  const downloadCSV = (csvContent: string, filename: string) => {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  
  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-lg font-semibold mb-4">Export Data</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Data Type
          </label>
          <div className="flex space-x-4">
            <label className="inline-flex items-center">
              <input
                type="radio"
                className="form-radio"
                name="dataType"
                value="traffic"
                checked={dataType === 'traffic'}
                onChange={() => setDataType('traffic')}
              />
              <span className="ml-2">Visitor Traffic</span>
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                className="form-radio"
                name="dataType"
                value="duration"
                checked={dataType === 'duration'}
                onChange={() => setDataType('duration')}
              />
              <span className="ml-2">Visit Duration</span>
            </label>
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Time Range
          </label>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as TimeRange)}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="day">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
        </div>
        
        {error && (
          <div className="text-red-600 text-sm">
            {error}
          </div>
        )}
        
        <div>
          <button
            onClick={handleExport}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? 'Exporting...' : 'Export CSV'}
          </button>
        </div>
      </div>
    </div>
  )
}
