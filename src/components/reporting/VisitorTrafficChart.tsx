'use client'

import { useState, useEffect } from 'react'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>ltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts'
import { createClientReportingService } from '@/lib/reporting/client'
import { VisitorTrafficData, ReportFilters, TimeRange } from '@/lib/reporting'

interface VisitorTrafficChartProps {
  orgId: string
  initialTimeRange?: TimeRange
}

export default function VisitorTrafficChart({ 
  orgId, 
  initialTimeRange = 'week' 
}: VisitorTrafficChartProps) {
  const [trafficData, setTrafficData] = useState<VisitorTrafficData[]>([])
  const [timeRange, setTimeRange] = useState<TimeRange>(initialTimeRange)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const reportingService = createClientReportingService()
  
  // Load visitor traffic data
  useEffect(() => {
    const loadTrafficData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const filters: ReportFilters = {
          timeRange
        }
        
        const data = await reportingService.getVisitorTraffic(orgId, filters)
        setTrafficData(data)
      } catch (err) {
        console.error('Error loading visitor traffic data:', err)
        setError('Failed to load visitor traffic data')
      } finally {
        setLoading(false)
      }
    }
    
    loadTrafficData()
  }, [orgId, timeRange, reportingService])
  
  // Handle time range change
  const handleTimeRangeChange = (newTimeRange: TimeRange) => {
    setTimeRange(newTimeRange)
  }
  
  // Get chart title based on time range
  const getChartTitle = () => {
    switch (timeRange) {
      case 'day':
        return 'Today\'s Visitor Traffic'
      case 'week':
        return 'This Week\'s Visitor Traffic'
      case 'month':
        return 'This Month\'s Visitor Traffic'
      case 'year':
        return 'This Year\'s Visitor Traffic'
      default:
        return 'Visitor Traffic'
    }
  }
  
  if (loading) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="text-red-600 text-center h-64 flex items-center justify-center">
          <p>{error}</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">{getChartTitle()}</h2>
        
        <div className="flex space-x-2">
          <button
            onClick={() => handleTimeRangeChange('day')}
            className={`px-3 py-1 text-sm rounded-md ${
              timeRange === 'day'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Day
          </button>
          <button
            onClick={() => handleTimeRangeChange('week')}
            className={`px-3 py-1 text-sm rounded-md ${
              timeRange === 'week'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Week
          </button>
          <button
            onClick={() => handleTimeRangeChange('month')}
            className={`px-3 py-1 text-sm rounded-md ${
              timeRange === 'month'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Month
          </button>
          <button
            onClick={() => handleTimeRangeChange('year')}
            className={`px-3 py-1 text-sm rounded-md ${
              timeRange === 'year'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Year
          </button>
        </div>
      </div>
      
      {trafficData.length === 0 ? (
        <div className="text-gray-500 text-center h-64 flex items-center justify-center">
          <p>No visitor traffic data available for the selected time range.</p>
        </div>
      ) : (
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={trafficData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis allowDecimals={false} />
              <Tooltip />
              <Legend />
              <Bar dataKey="count" name="Visitors" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}
    </div>
  )
}
