'use client'

import { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  XAxis, 
  <PERSON>Axis, 
  ZAxis,
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Cell
} from 'recharts'
import { createClientReportingService } from '@/lib/reporting/client'
import { PeakTimeData, ReportFilters } from '@/lib/reporting'

interface PeakTimeHeatmapProps {
  orgId: string
}

// Days of the week for Y-axis
const DAYS_OF_WEEK = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

// Hours of the day for X-axis (0-23)
const HOURS_OF_DAY = Array.from({ length: 24 }, (_, i) => i)

// Color scale for heatmap
const COLOR_RANGE = [
  '#f7fbff', // Lightest blue (lowest count)
  '#deebf7',
  '#c6dbef',
  '#9ecae1',
  '#6baed6',
  '#4292c6',
  '#2171b5',
  '#08519c',
  '#08306b'  // Darkest blue (highest count)
]

export default function PeakTimeHeatmap({ orgId }: PeakTimeHeatmapProps) {
  const [peakTimeData, setPeakTimeData] = useState<PeakTimeData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [maxCount, setMaxCount] = useState(0)
  
  const reportingService = createClientReportingService()
  
  // Load peak time data
  useEffect(() => {
    const loadPeakTimeData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const filters: ReportFilters = {
          timeRange: 'month' // Use last month's data for peak time analysis
        }
        
        const data = await reportingService.getPeakTimeData(orgId, filters)
        
        // Find the maximum count for color scaling
        const max = data.reduce((max, item) => Math.max(max, item.count), 0)
        setMaxCount(max)
        
        setPeakTimeData(data)
      } catch (err) {
        console.error('Error loading peak time data:', err)
        setError('Failed to load peak time data')
      } finally {
        setLoading(false)
      }
    }
    
    loadPeakTimeData()
  }, [orgId, reportingService])
  
  // Get color based on count value
  const getColor = (count: number) => {
    if (maxCount === 0) return COLOR_RANGE[0]
    
    const index = Math.min(
      Math.floor((count / maxCount) * (COLOR_RANGE.length - 1)),
      COLOR_RANGE.length - 1
    )
    
    return COLOR_RANGE[index]
  }
  
  // Define tooltip props interface
  interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
      payload: {
        day: string;
        hour: number;
        count: number;
      };
    }>;
  }
  
  // Custom tooltip for the heatmap
  const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-2 border border-gray-300 shadow-md rounded-md">
          <p className="font-medium">{data.day}</p>
          <p>{`Time: ${data.hour}:00 - ${data.hour + 1}:00`}</p>
          <p>{`Visitors: ${data.count}`}</p>
        </div>
      )
    }
    
    return null
  }
  
  // Format hour for X-axis
  const formatHour = (hour: number) => {
    return `${hour}:00`
  }
  
  if (loading) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="text-red-600 text-center h-64 flex items-center justify-center">
          <p>{error}</p>
        </div>
      </div>
    )
  }
  
  // Create a complete dataset with all day-hour combinations
  const completeData = []
  for (const day of DAYS_OF_WEEK) {
    for (const hour of HOURS_OF_DAY) {
      // Find existing data point or use count 0
      const existingPoint = peakTimeData.find(
        point => point.day === day && point.hour === hour
      )
      
      completeData.push({
        day,
        hour,
        count: existingPoint ? existingPoint.count : 0,
        // Map day to y-coordinate (0-6)
        y: DAYS_OF_WEEK.indexOf(day),
        // Use hour as x-coordinate (0-23)
        x: hour
      })
    }
  }
  
  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-lg font-semibold mb-4">Peak Visitor Times</h2>
      
      {peakTimeData.length === 0 ? (
        <div className="text-gray-500 text-center h-64 flex items-center justify-center">
          <p>No peak time data available.</p>
        </div>
      ) : (
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart
              margin={{ top: 20, right: 20, bottom: 20, left: 80 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                type="number" 
                dataKey="x" 
                name="Hour" 
                domain={[0, 23]}
                tickCount={24}
                tickFormatter={formatHour}
                label={{ value: 'Hour of Day', position: 'insideBottom', offset: -10 }}
              />
              <YAxis 
                type="number" 
                dataKey="y" 
                name="Day" 
                domain={[0, 6]}
                tickCount={7}
                tickFormatter={(value) => DAYS_OF_WEEK[value]}
                label={{ value: 'Day of Week', angle: -90, position: 'insideLeft' }}
              />
              <ZAxis type="number" dataKey="count" range={[50, 500]} />
              <Tooltip content={<CustomTooltip />} />
              <Scatter name="Peak Times" data={completeData} shape="square">
                {completeData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={getColor(entry.count)} />
                ))}
              </Scatter>
            </ScatterChart>
          </ResponsiveContainer>
        </div>
      )}
      
      <div className="mt-4 flex justify-center">
        <div className="flex items-center">
          <span className="text-xs mr-2">Low</span>
          <div className="flex h-4">
            {COLOR_RANGE.map((color, index) => (
              <div
                key={index}
                style={{ backgroundColor: color, width: '20px', height: '100%' }}
              />
            ))}
          </div>
          <span className="text-xs ml-2">High</span>
        </div>
      </div>
    </div>
  )
}
