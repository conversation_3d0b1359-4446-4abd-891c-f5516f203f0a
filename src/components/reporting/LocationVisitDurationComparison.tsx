'use client'

import { useState, useEffect } from 'react'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>lt<PERSON>, 
  Legend, 
  ResponsiveContainer
} from 'recharts'
import { createClientReportingService } from '@/lib/reporting/client'
import { LocationVisitDurationData, ReportFilters, TimeRange } from '@/lib/reporting'

interface LocationVisitDurationComparisonProps {
  orgId: string
  initialTimeRange?: TimeRange
}

export default function LocationVisitDurationComparison({ 
  orgId, 
  initialTimeRange = 'month' 
}: LocationVisitDurationComparisonProps) {
  const [durationData, setDurationData] = useState<LocationVisitDurationData[]>([])
  const [timeRange, setTimeRange] = useState<TimeRange>(initialTimeRange)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const reportingService = createClientReportingService()
  
  // Load visit duration data by location
  useEffect(() => {
    const loadDurationData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const filters: ReportFilters = {
          timeRange
        }
        
        // Get location visit duration data
        const result = await reportingService.getLocationVisitDuration(orgId, filters)
        setDurationData(result)
      } catch (err) {
        console.error('Error loading location visit duration data:', err)
        setError('Failed to load location visit duration data')
      } finally {
        setLoading(false)
      }
    }
    
    loadDurationData()
  }, [orgId, timeRange, reportingService])
  
  // Handle time range change
  const handleTimeRangeChange = (newTimeRange: TimeRange) => {
    setTimeRange(newTimeRange)
  }
  
  // Get chart title based on time range
  const getChartTitle = () => {
    switch (timeRange) {
      case 'day':
        return 'Today\'s Average Visit Duration by Location'
      case 'week':
        return 'This Week\'s Average Visit Duration by Location'
      case 'month':
        return 'This Month\'s Average Visit Duration by Location'
      case 'year':
        return 'This Year\'s Average Visit Duration by Location'
      default:
        return 'Average Visit Duration by Location'
    }
  }
  
  // Format minutes as hours and minutes
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours > 0) {
      return `${hours}h ${mins}m`
    } else {
      return `${mins}m`
    }
  }
  
  // Calculate overall average duration
  const getOverallAverageDuration = () => {
    if (durationData.length === 0) return 0
    
    const totalDuration = durationData.reduce((sum, item) => sum + item.averageDuration, 0)
    return Math.round(totalDuration / durationData.length)
  }
  
  if (loading) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="text-red-600 text-center h-64 flex items-center justify-center">
          <p>{error}</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">{getChartTitle()}</h2>
        
        <div className="flex space-x-2">
          <button
            onClick={() => handleTimeRangeChange('day')}
            className={`px-3 py-1 text-sm rounded-md ${
              timeRange === 'day'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Day
          </button>
          <button
            onClick={() => handleTimeRangeChange('week')}
            className={`px-3 py-1 text-sm rounded-md ${
              timeRange === 'week'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Week
          </button>
          <button
            onClick={() => handleTimeRangeChange('month')}
            className={`px-3 py-1 text-sm rounded-md ${
              timeRange === 'month'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Month
          </button>
          <button
            onClick={() => handleTimeRangeChange('year')}
            className={`px-3 py-1 text-sm rounded-md ${
              timeRange === 'year'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Year
          </button>
        </div>
      </div>
      
      {durationData.length === 0 ? (
        <div className="text-gray-500 text-center h-64 flex items-center justify-center">
          <p>No visit duration data available for the selected time range.</p>
        </div>
      ) : (
        <>
          <div className="h-64 mb-6">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={durationData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="locationName" />
                <YAxis 
                  allowDecimals={false} 
                  label={{ value: 'Minutes', angle: -90, position: 'insideLeft' }} 
                />
                <Tooltip formatter={(value) => [formatDuration(Number(value)), 'Average Duration']} />
                <Legend />
                <Bar dataKey="averageDuration" name="Average Visit Duration" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-blue-800 mb-1">Overall Average Duration</h3>
              <p className="text-2xl font-bold text-blue-900">{formatDuration(getOverallAverageDuration())}</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-green-800 mb-1">Longest Average Duration</h3>
              <p className="text-2xl font-bold text-green-900">
                {durationData.length > 0 ? formatDuration(durationData[0].averageDuration) : 'N/A'}
              </p>
              <p className="text-sm text-green-700">
                {durationData.length > 0 ? durationData[0].locationName : ''}
              </p>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-purple-800 mb-1">Shortest Average Duration</h3>
              <p className="text-2xl font-bold text-purple-900">
                {durationData.length > 0 ? formatDuration(durationData[durationData.length - 1].averageDuration) : 'N/A'}
              </p>
              <p className="text-sm text-purple-700">
                {durationData.length > 0 ? durationData[durationData.length - 1].locationName : ''}
              </p>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
