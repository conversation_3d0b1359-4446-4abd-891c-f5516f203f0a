'use client'

import { useState, useEffect } from 'react'
import { 
  <PERSON>C<PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>lt<PERSON>, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts'
import { createClientReportingService } from '@/lib/reporting/client'
import { VisitorTrafficByLocationData, ReportFilters, TimeRange } from '@/lib/reporting'

interface CrossLocationComparisonProps {
  orgId: string
  initialTimeRange?: TimeRange
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FF6B6B', '#6B66FF'];

export default function CrossLocationComparison({ 
  orgId, 
  initialTimeRange = 'month' 
}: CrossLocationComparisonProps) {
  const [trafficData, setTrafficData] = useState<VisitorTrafficByLocationData[]>([])
  const [timeRange, setTimeRange] = useState<TimeRange>(initialTimeRange)
  const [viewType, setViewType] = useState<'bar' | 'pie'>('bar')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const reportingService = createClientReportingService()
  
  // Load visitor traffic data by location
  useEffect(() => {
    const loadTrafficData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const filters: ReportFilters = {
          timeRange
        }
        
        const data = await reportingService.getVisitorTrafficByLocation(orgId, filters)
        setTrafficData(data)
      } catch (err) {
        console.error('Error loading cross-location traffic data:', err)
        setError('Failed to load cross-location traffic data')
      } finally {
        setLoading(false)
      }
    }
    
    loadTrafficData()
  }, [orgId, timeRange, reportingService])
  
  // Handle time range change
  const handleTimeRangeChange = (newTimeRange: TimeRange) => {
    setTimeRange(newTimeRange)
  }
  
  // Handle view type change
  const handleViewTypeChange = (newViewType: 'bar' | 'pie') => {
    setViewType(newViewType)
  }
  
  // Get chart title based on time range
  const getChartTitle = () => {
    switch (timeRange) {
      case 'day':
        return 'Today\'s Visitor Traffic by Location'
      case 'week':
        return 'This Week\'s Visitor Traffic by Location'
      case 'month':
        return 'This Month\'s Visitor Traffic by Location'
      case 'year':
        return 'This Year\'s Visitor Traffic by Location'
      default:
        return 'Visitor Traffic by Location'
    }
  }
  
  // Format data for pie chart
  const getPieChartData = () => {
    return trafficData.map(item => ({
      name: item.locationName,
      value: item.count
    }))
  }
  
  // Calculate total visitors
  const getTotalVisitors = () => {
    return trafficData.reduce((sum, item) => sum + item.count, 0)
  }
  
  if (loading) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="text-red-600 text-center h-64 flex items-center justify-center">
          <p>{error}</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">{getChartTitle()}</h2>
        
        <div className="flex space-x-4">
          <div className="flex space-x-2">
            <button
              onClick={() => handleViewTypeChange('bar')}
              className={`px-3 py-1 text-sm rounded-md ${
                viewType === 'bar'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Bar
            </button>
            <button
              onClick={() => handleViewTypeChange('pie')}
              className={`px-3 py-1 text-sm rounded-md ${
                viewType === 'pie'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Pie
            </button>
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={() => handleTimeRangeChange('day')}
              className={`px-3 py-1 text-sm rounded-md ${
                timeRange === 'day'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Day
            </button>
            <button
              onClick={() => handleTimeRangeChange('week')}
              className={`px-3 py-1 text-sm rounded-md ${
                timeRange === 'week'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Week
            </button>
            <button
              onClick={() => handleTimeRangeChange('month')}
              className={`px-3 py-1 text-sm rounded-md ${
                timeRange === 'month'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Month
            </button>
            <button
              onClick={() => handleTimeRangeChange('year')}
              className={`px-3 py-1 text-sm rounded-md ${
                timeRange === 'year'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Year
            </button>
          </div>
        </div>
      </div>
      
      {trafficData.length === 0 ? (
        <div className="text-gray-500 text-center h-64 flex items-center justify-center">
          <p>No visitor traffic data available for the selected time range.</p>
        </div>
      ) : (
        <>
          <div className="h-64 mb-6">
            {viewType === 'bar' ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={trafficData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="locationName" />
                  <YAxis allowDecimals={false} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="count" name="Visitors" fill="#3B82F6" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={getPieChartData()}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {getPieChartData().map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} visitors`, 'Count']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            )}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-blue-800 mb-1">Total Visitors</h3>
              <p className="text-2xl font-bold text-blue-900">{getTotalVisitors()}</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-green-800 mb-1">Busiest Location</h3>
              <p className="text-2xl font-bold text-green-900">
                {trafficData.length > 0 ? trafficData[0].locationName : 'N/A'}
              </p>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-purple-800 mb-1">Number of Active Locations</h3>
              <p className="text-2xl font-bold text-purple-900">{trafficData.length}</p>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
