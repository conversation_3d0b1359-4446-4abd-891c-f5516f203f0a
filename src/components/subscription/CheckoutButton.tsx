'use client'

import { useState } from 'react'
import { Loader2 } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'

interface CheckoutButtonProps {
  planId: string
  orgId: string
  buttonText?: string
  className?: string
}

export default function CheckoutButton({
  planId,
  orgId,
  buttonText = 'Subscribe',
  className = 'w-full bg-blue-600 text-white py-2 px-4 rounded-md font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed',
}: CheckoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleCheckout = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Get the current session
      const supabase = createClient()
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        throw new Error('No active session. Please log in again.')
      }

      // Create a checkout session
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          planId,
          orgId,
        }),
      })

      const { url, error: checkoutError } = await response.json()

      if (checkoutError) {
        throw new Error(checkoutError)
      }

      // Redirect to the checkout page
      if (url) {
        // For enterprise plans, we might handle differently
        if (planId === 'enterprise') {
          // For enterprise plans, we might show a contact form instead
          // But for now, we'll just redirect to the checkout page
          window.location.href = url
        } else {
          // For standard plans, redirect to the Stripe checkout page
          window.location.href = url
        }
      } else {
        throw new Error('No checkout URL returned')
      }
    } catch (error) {
      console.error('Checkout error:', error)
      setError(error instanceof Error ? error.message : 'An error occurred during checkout')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div>
      {error && (
        <div className="mb-4 p-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
          {error}
        </div>
      )}

      <button
        type="button"
        onClick={handleCheckout}
        disabled={isLoading}
        className={className}
      >
        {isLoading ? (
          <span className="flex items-center justify-center">
            <Loader2 className="animate-spin h-5 w-5 mr-2" />
            Processing...
          </span>
        ) : (
          buttonText
        )}
      </button>
    </div>
  )
}
