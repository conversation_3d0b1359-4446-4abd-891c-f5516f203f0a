'use client'

import { useState } from 'react'
import { Subscription } from '@/types'
import { getPlanById } from '@/lib/stripe/plans'
import { Loader2 } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'

interface SubscriptionInfoProps {
  subscription: Subscription
}

export default function SubscriptionInfo({ subscription }: SubscriptionInfoProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Get the plan details
  const plan = getPlanById(subscription.plan_id)

  // Format the current period end date
  const currentPeriodEnd = new Date(subscription.current_period_end).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })

  // Handle manage subscription
  const handleManageSubscription = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Get the current session
      const supabase = createClient()
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        throw new Error('No active session. Please log in again.')
      }

      // Create a billing portal session
      const response = await fetch('/api/stripe/billing-portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          customerId: subscription.stripe_customer_id,
        }),
      })

      const { url, error: portalError } = await response.json()

      if (portalError) {
        throw new Error(portalError)
      }

      // Redirect to the billing portal
      if (url) {
        window.location.href = url
      } else {
        throw new Error('No billing portal URL returned')
      }
    } catch (error) {
      console.error('Billing portal error:', error)
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  if (!plan) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold mb-4">Subscription</h2>
        <p className="text-gray-600">No subscription plan found.</p>
      </div>
    )
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <h2 className="text-xl font-semibold mb-4">Your Subscription</h2>

      {error && (
        <div className="mb-4 p-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
          {error}
        </div>
      )}

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-medium">{plan.name} Plan</h3>
            <p className="text-gray-600">{plan.description}</p>
          </div>
          <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
            {subscription.status === 'active' ? 'Active' :
             subscription.status === 'trialing' ? 'Trial' :
             subscription.status === 'past_due' ? 'Past Due' :
             subscription.status === 'canceled' ? 'Canceled' :
             subscription.status === 'pending' ? 'Pending' :
             subscription.status}
          </div>
        </div>

        <div className="border-t pt-4">
          <p className="text-sm text-gray-600">
            {subscription.status === 'active' ? (
              <>Your subscription will renew on <span className="font-medium">{currentPeriodEnd}</span></>
            ) : subscription.status === 'canceled' ? (
              <>Your subscription will end on <span className="font-medium">{currentPeriodEnd}</span></>
            ) : subscription.status === 'pending' ? (
              <>Your subscription is pending approval</>
            ) : (
              <>Your subscription period ends on <span className="font-medium">{currentPeriodEnd}</span></>
            )}
          </p>
        </div>

        <div className="border-t pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Plan Features:</h4>
          <ul className="space-y-1">
            {plan.features.slice(0, 5).map((feature, index) => (
              <li key={index} className="text-sm text-gray-600 flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                {feature}
              </li>
            ))}
            {plan.features.length > 5 && (
              <li className="text-sm text-gray-600">
                <span className="text-gray-400">+ {plan.features.length - 5} more features</span>
              </li>
            )}
          </ul>
        </div>

        {subscription.status !== 'pending' && subscription.status !== 'canceled' && (
          <div className="border-t pt-4">
            <button
              onClick={handleManageSubscription}
              disabled={isLoading}
              className="px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <span className="flex items-center">
                  <Loader2 className="animate-spin h-4 w-4 mr-2" />
                  Loading...
                </span>
              ) : (
                'Manage Subscription'
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
