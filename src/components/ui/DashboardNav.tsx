'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { User } from '@supabase/supabase-js'
import { 
  BarChart3, 
  Building2, 
  Calendar, 
  CreditCard, 
  LineChart, 
  LogOut, 
  Menu, 
  Settings, 
  ShieldCheck,
  UserPlus, 
  Users, 
  X,
  UserCog,
  ClipboardCheck
} from 'lucide-react'

export default function DashboardNav() {
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const supabase = createClient()

  useEffect(() => {
    const getUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        setUser(user)
      } catch (error) {
        console.error('Error fetching user:', error)
      }
    }
    
    getUser()
  }, [supabase])

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    window.location.href = '/'
  }

  const isActive = (path: string) => {
    if (path === '/dashboard' && pathname === '/dashboard') {
      return true
    }
    if (path !== '/dashboard' && pathname.startsWith(path)) {
      return true
    }
    return false
  }

  return (
    <>
      {/* Mobile menu button */}
      <button 
        className="fixed top-4 right-4 z-50 md:hidden bg-white p-2 rounded-md shadow-md"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </button>

      {/* Sidebar - mobile version uses fixed positioning */}
      <aside className={`
        fixed top-0 left-0 z-40 h-full w-64 bg-white border-r border-gray-200 transform transition-transform duration-200 ease-in-out
        md:static md:translate-x-0 md:flex md:flex-col
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="p-4 border-b">
          <Link href="/dashboard" className="flex items-center">
            <span className="font-bold text-xl">VisitFlow</span>
          </Link>
        </div>
        <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
          <Link
            href="/dashboard"
            className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors ${
              isActive('/dashboard') ? 'bg-gray-100 text-blue-600' : 'text-gray-700'
            }`}
            onClick={() => setIsOpen(false)}
          >
            <BarChart3 className="h-5 w-5 mr-3" />
            Dashboard
          </Link>
          <Link
            href="/dashboard/visitors"
            className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors ${
              isActive('/dashboard/visitors') ? 'bg-gray-100 text-blue-600' : 'text-gray-700'
            }`}
            onClick={() => setIsOpen(false)}
          >
            <Users className="h-5 w-5 mr-3" />
            Visitors
          </Link>
          <Link
            href="/dashboard/visitors/pre-register"
            className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors ${
              isActive('/dashboard/visitors/pre-register') ? 'bg-gray-100 text-blue-600' : 'text-gray-700'
            }`}
            onClick={() => setIsOpen(false)}
          >
            <UserPlus className="h-5 w-5 mr-3" />
            Pre-register Visitor
          </Link>
          <Link
            href="/dashboard/appointments"
            className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors ${
              isActive('/dashboard/appointments') ? 'bg-gray-100 text-blue-600' : 'text-gray-700'
            }`}
            onClick={() => setIsOpen(false)}
          >
            <Calendar className="h-5 w-5 mr-3" />
            Appointments
          </Link>
          {/* Host section */}
          <div className="pt-2 pb-1">
            <p className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Host</p>
          </div>
          <Link
            href="/dashboard/host/status"
            className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors ${
              isActive('/dashboard/host/status') ? 'bg-gray-100 text-blue-600' : 'text-gray-700'
            }`}
            onClick={() => setIsOpen(false)}
          >
            <UserCog className="h-5 w-5 mr-3" />
            Status
          </Link>
          <Link
            href="/dashboard/host/expected-visitors"
            className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors ${
              isActive('/dashboard/host/expected-visitors') ? 'bg-gray-100 text-blue-600' : 'text-gray-700'
            }`}
            onClick={() => setIsOpen(false)}
          >
            <ClipboardCheck className="h-5 w-5 mr-3" />
            Expected Visitors
          </Link>
          <Link
            href="/dashboard/locations"
            className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors ${
              isActive('/dashboard/locations') ? 'bg-gray-100 text-blue-600' : 'text-gray-700'
            }`}
            onClick={() => setIsOpen(false)}
          >
            <Building2 className="h-5 w-5 mr-3" />
            Locations
          </Link>
          <Link
            href="/dashboard/reports"
            className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors ${
              isActive('/dashboard/reports') ? 'bg-gray-100 text-blue-600' : 'text-gray-700'
            }`}
            onClick={() => setIsOpen(false)}
          >
            <LineChart className="h-5 w-5 mr-3" />
            Reports
          </Link>
          <Link
            href="/dashboard/billing"
            className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors ${
              isActive('/dashboard/billing') ? 'bg-gray-100 text-blue-600' : 'text-gray-700'
            }`}
            onClick={() => setIsOpen(false)}
          >
            <CreditCard className="h-5 w-5 mr-3" />
            Billing
          </Link>
          <Link
            href="/dashboard/settings"
            className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors ${
              isActive('/dashboard/settings') ? 'bg-gray-100 text-blue-600' : 'text-gray-700'
            }`}
            onClick={() => setIsOpen(false)}
          >
            <Settings className="h-5 w-5 mr-3" />
            Settings
          </Link>
          {user?.user_metadata?.is_admin && (
            <Link
              href="/dashboard/admin"
              className={`flex items-center px-3 py-2 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors ${
                isActive('/dashboard/admin') ? 'bg-gray-100 text-blue-600' : 'text-gray-700'
              }`}
              onClick={() => setIsOpen(false)}
            >
              <ShieldCheck className="h-5 w-5 mr-3" />
              Admin
            </Link>
          )}
        </nav>
        <div className="p-4 border-t">
          <button 
            onClick={handleSignOut}
            className="flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors w-full"
          >
            <LogOut className="h-5 w-5 mr-3" />
            Sign out
          </button>
        </div>
      </aside>

      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  )
}
