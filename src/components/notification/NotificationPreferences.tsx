'use client'

import { useState, useEffect } from 'react'
import { NotificationType } from '@/types'
import { createClientNotificationService } from '@/lib/notification/client'

interface NotificationPreferencesProps {
  userId: string
}

export default function NotificationPreferences({ userId }: NotificationPreferencesProps) {
  const [preferences, setPreferences] = useState<Record<NotificationType, {
    email: boolean
    sms: boolean
    inApp: boolean
  }> | null>(null)
  
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  
  const notificationService = createClientNotificationService()
  
  // Notification type labels for display
  const notificationTypeLabels: Record<NotificationType, string> = {
    [NotificationType.VISITOR_PREREGISTERED]: 'Visitor Pre-registered',
    [NotificationType.VISITOR_CHECKED_IN]: 'Visitor Checked In',
    [NotificationType.VISITOR_CHECKED_OUT]: 'Visitor Checked Out',
    [NotificationType.VISIT_CANCELLED]: 'Visit Cancelled',
    [NotificationType.VISIT_RESCHEDULED]: 'Visit Rescheduled',
    [NotificationType.VISIT_REMINDER]: 'Visit Reminder'
  }
  
  // Load user preferences
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const userPreferences = await notificationService.getUserPreferences(userId)
        
        // Initialize preferences object with all notification types
        const prefsObj = Object.values(NotificationType).reduce((acc, type) => ({
          ...acc,
          [type]: {
            email: true,
            sms: false,
            inApp: true
          }
        }), {} as Record<NotificationType, { email: boolean; sms: boolean; inApp: boolean }>)
        
        // Set default values (email and in-app enabled, SMS disabled)
        Object.values(NotificationType).forEach(type => {
          prefsObj[type] = {
            email: true,
            sms: false,
            inApp: true
          }
        })
        
        // Update with user preferences
        userPreferences.forEach(pref => {
          prefsObj[pref.notification_type] = {
            email: pref.email_enabled,
            sms: pref.sms_enabled,
            inApp: pref.in_app_enabled
          }
        })
        
        setPreferences(prefsObj)
      } catch (err) {
        console.error('Error loading notification preferences:', err)
        setError('Failed to load notification preferences')
      } finally {
        setLoading(false)
      }
    }
    
    loadPreferences()
  }, [userId, notificationService])
  
  // Save preferences
  const savePreferences = async () => {
    if (!preferences) return
    
    try {
      setSaving(true)
      setError(null)
      setSuccess(false)
      
      // Save each preference type
      for (const [type, channels] of Object.entries(preferences)) {
        await notificationService.setUserPreference(
          userId,
          type as NotificationType,
          channels.email,
          channels.sms,
          channels.inApp
        )
      }
      
      setSuccess(true)
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(false)
      }, 3000)
    } catch (err) {
      console.error('Error saving notification preferences:', err)
      setError('Failed to save notification preferences')
    } finally {
      setSaving(false)
    }
  }
  
  // Handle preference change
  const handlePreferenceChange = (
    type: NotificationType,
    channel: 'email' | 'sms' | 'inApp',
    value: boolean
  ) => {
    if (!preferences) return
    
    setPreferences({
      ...preferences,
      [type]: {
        ...preferences[type],
        [channel]: value
      }
    })
  }
  
  if (loading) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="flex items-center justify-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }
  
  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-6">Notification Preferences</h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
          Notification preferences saved successfully!
        </div>
      )}
      
      <div className="space-y-6">
        {preferences && Object.entries(preferences).map(([type, channels]) => (
          <div key={type} className="border-b pb-4">
            <h3 className="font-medium mb-2">{notificationTypeLabels[type as NotificationType]}</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id={`${type}-email`}
                  checked={channels.email}
                  onChange={(e) => handlePreferenceChange(
                    type as NotificationType,
                    'email',
                    e.target.checked
                  )}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor={`${type}-email`} className="ml-2 block text-sm text-gray-700">
                  Email
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id={`${type}-sms`}
                  checked={channels.sms}
                  onChange={(e) => handlePreferenceChange(
                    type as NotificationType,
                    'sms',
                    e.target.checked
                  )}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor={`${type}-sms`} className="ml-2 block text-sm text-gray-700">
                  SMS
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id={`${type}-inApp`}
                  checked={channels.inApp}
                  onChange={(e) => handlePreferenceChange(
                    type as NotificationType,
                    'inApp',
                    e.target.checked
                  )}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor={`${type}-inApp`} className="ml-2 block text-sm text-gray-700">
                  In-App
                </label>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-6">
        <button
          onClick={savePreferences}
          disabled={saving || !preferences}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {saving ? 'Saving...' : 'Save Preferences'}
        </button>
      </div>
    </div>
  )
}
