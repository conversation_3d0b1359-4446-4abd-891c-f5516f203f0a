'use client'

import { useState, useEffect } from 'react'
import { Notification, NotificationType } from '@/types'
import { createClientNotificationService } from '@/lib/notification/client'
import { Bell, Check } from 'lucide-react'

interface NotificationCenterProps {
  userId: string
}

export default function NotificationCenter({ userId }: NotificationCenterProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const notificationService = createClientNotificationService()
  
  // Notification type labels for display
  const notificationTypeLabels: Record<NotificationType, string> = {
    [NotificationType.VISITOR_PREREGISTERED]: 'Visitor Pre-registered',
    [NotificationType.VISITOR_CHECKED_IN]: 'Visitor Checked In',
    [NotificationType.VISITOR_CHECKED_OUT]: 'Visitor Checked Out',
    [NotificationType.VISIT_CANCELLED]: 'Visit Cancelled',
    [NotificationType.VISIT_RESCHEDULED]: 'Visit Rescheduled',
    [NotificationType.VISIT_REMINDER]: 'Visit Reminder'
  }
  
  // Load unread notifications
  const loadNotifications = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const unreadNotifications = await notificationService.getUnreadNotifications(userId)
      setNotifications(unreadNotifications)
    } catch (err) {
      console.error('Error loading notifications:', err)
      setError('Failed to load notifications')
    } finally {
      setLoading(false)
    }
  }
  
  // Load notifications on mount and when userId changes
  useEffect(() => {
    loadNotifications()
    
    // Poll for new notifications every 30 seconds
    const interval = setInterval(() => {
      if (!isOpen) { // Only poll when notification center is closed
        loadNotifications()
      }
    }, 30000)
    
    return () => clearInterval(interval)
  }, [userId, isOpen])
  
  // Mark notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      await notificationService.markNotificationAsRead(notificationId)
      setNotifications(notifications.filter(n => n.id !== notificationId))
    } catch (err) {
      console.error('Error marking notification as read:', err)
    }
  }
  
  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      await notificationService.markAllNotificationsAsRead(userId)
      setNotifications([])
    } catch (err) {
      console.error('Error marking all notifications as read:', err)
    }
  }
  
  // Format notification date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date)
  }
  
  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none"
        aria-label="Notifications"
      >
        <Bell size={20} />
        {notifications.length > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
            {notifications.length}
          </span>
        )}
      </button>
      
      {/* Notification Panel */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 sm:w-96 bg-white rounded-md shadow-lg overflow-hidden z-50">
          <div className="p-3 bg-gray-100 border-b flex justify-between items-center">
            <h3 className="text-sm font-medium">Notifications</h3>
            {notifications.length > 0 && (
              <button
                onClick={markAllAsRead}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                Mark all as read
              </button>
            )}
          </div>
          
          <div className="max-h-96 overflow-y-auto">
            {loading && (
              <div className="flex justify-center items-center h-20">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            )}
            
            {error && (
              <div className="p-3 text-sm text-red-600">
                {error}
              </div>
            )}
            
            {!loading && !error && notifications.length === 0 && (
              <div className="p-4 text-sm text-gray-500 text-center">
                No new notifications
              </div>
            )}
            
            {notifications.map(notification => (
              <div
                key={notification.id}
                className="p-4 border-b hover:bg-gray-50 transition-colors duration-150"
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="text-sm font-medium">
                      {notificationTypeLabels[notification.notification_type]}
                    </h4>
                    <p className="text-sm text-gray-600 mt-1">
                      {notification.message}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {formatDate(notification.created_at)}
                    </p>
                  </div>
                  <button
                    onClick={() => markAsRead(notification.id)}
                    className="text-gray-400 hover:text-gray-600"
                    aria-label="Mark as read"
                  >
                    <Check size={16} />
                  </button>
                </div>
              </div>
            ))}
          </div>
          
          <div className="p-2 bg-gray-100 border-t text-center">
            <button
              onClick={() => setIsOpen(false)}
              className="text-xs text-gray-600 hover:text-gray-800"
            >
              Close
            </button>
          </div>
        </div>
      )}
      
      {/* Backdrop for closing when clicking outside */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        ></div>
      )}
    </div>
  )
}
