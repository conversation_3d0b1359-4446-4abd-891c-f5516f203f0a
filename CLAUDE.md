# CLAUDE.md - Development Guidelines

## Build Commands
- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Code Style Guidelines
- **TypeScript**: Strict mode, ES2017 target
- **Path Aliases**: Use `@/*` for `./src/*` imports
- **Components**: React 19, functional components with typed props
- **CSS**: Tailwind CSS 4.x with utility-first approach
- **Naming**:
  - PascalCase for components, types, interfaces
  - camelCase for functions, variables, instances
  - Use descriptive names (e.g., `VisitStatus`, `formatDate`)
- **State Management**: Zustand for global state
- **Form Handling**: React Hook Form with Zod validation
- **Utility Functions**: Use JSDoc comments, proper typing
- **Error Handling**: TypeScript for compile-time safety, try/catch for async operations

## Supabase & Authentication
- Database types via Supabase schema
- Auth flows with Supabase Auth Helpers

## Formatting
- Use the provided `cn()` utility for className merging
- Follow ESLint Next.js core-web-vitals rules