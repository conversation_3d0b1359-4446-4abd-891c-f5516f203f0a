# Style and Aesthetic Guide

## Design Principles

### Clean and Professional
The visitor management system is designed with a clean, professional aesthetic that conveys trust and efficiency. The interface is uncluttered, with ample white space to enhance readability and focus.

### Intuitive Navigation
Navigation is designed to be intuitive, with a clear hierarchy and consistent patterns. The sidebar provides easy access to main sections, while breadcrumbs help users understand their location within the application.

### Responsive Design
The interface is designed to be responsive, adapting to different screen sizes and devices. This ensures a consistent experience whether accessed from a desktop computer, tablet, or mobile device.

### Accessibility
The design prioritizes accessibility, with sufficient color contrast, keyboard navigation support, and semantic HTML. This ensures the application is usable by people with various abilities and preferences.

## Color Palette

### Primary Colors
- **Blue (#007bff)**: Used for primary actions, links, and highlighting important information
- **White (#ffffff)**: Background color for content areas
- **Gray (#f8f9fa)**: Background color for the application
- **Dark Gray (#343a40)**: Used for text and icons

### Secondary Colors
- **Green (#28a745)**: Used for success states and positive actions
- **Red (#dc3545)**: Used for error states and destructive actions
- **Yellow (#ffc107)**: Used for warning states and attention-grabbing elements
- **Light Blue (#e9f2ff)**: Used for selected states and hover effects

### Accent Colors
- **Purple (#6f42c1)**: Used sparingly for special features or premium content
- **Teal (#20c997)**: Used for highlighting new features or special promotions

## Typography

### Font Family
- **Primary Font**: System font stack (San Francisco on macOS/iOS, Segoe UI on Windows, Roboto on Android)
- **Fallback**: Arial, sans-serif

### Font Sizes
- **Headings**:
  - H1: 2rem (32px)
  - H2: 1.5rem (24px)
  - H3: 1.25rem (20px)
  - H4: 1rem (16px)
- **Body Text**: 1rem (16px)
- **Small Text**: 0.875rem (14px)
- **Extra Small Text**: 0.75rem (12px)

### Font Weights
- **Regular**: 400
- **Medium**: 500
- **Bold**: 700

## Components

### Buttons
- **Primary Button**: Blue background, white text, rounded corners
- **Secondary Button**: White background, blue border, blue text
- **Danger Button**: Red background, white text
- **Disabled Button**: Gray background, darker gray text

### Cards
- White background
- Light shadow
- Rounded corners
- Consistent padding
- Optional border

### Forms
- Clear labels above input fields
- Validation feedback inline with fields
- Consistent spacing between form elements
- Clear error messages

### Tables
- Zebra striping for better readability
- Hover effects on rows
- Responsive design for mobile viewing
- Clear headers and consistent alignment

## Icons

The application uses Lucide Icons for a consistent, modern look. Icons are used to:
- Enhance navigation
- Provide visual cues for actions
- Illustrate features and concepts
- Improve the overall aesthetic

## Spacing System

The spacing system is based on a 4px grid:
- **Extra Small**: 4px
- **Small**: 8px
- **Medium**: 16px
- **Large**: 24px
- **Extra Large**: 32px
- **2x Extra Large**: 48px

This consistent spacing system ensures harmony throughout the interface and makes the design feel cohesive.

## Animation and Transitions

Animations and transitions are subtle and purposeful:
- Page transitions are smooth but quick
- Hover effects provide feedback without distraction
- Loading states use subtle animations to indicate progress
- Notifications appear and disappear with gentle fades

## Subscription Plan Presentation

The subscription plans are presented in a card-based layout that:
- Clearly differentiates between plan tiers
- Highlights the recommended plan
- Makes feature comparisons easy
- Uses visual hierarchy to emphasize key selling points
- Provides clear call-to-action buttons
