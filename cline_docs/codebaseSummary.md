# Codebase Summary

## Key Components and Their Interactions

### Authentication Flow
- **Signup Page** (`src/app/signup/page.tsx`): User registration with organization creation
- **Login Page** (`src/app/login/page.tsx`): User authentication
- **Auth Callback** (`src/app/auth/callback/route.ts`): Handles Supabase authentication callbacks
- **Middleware** (`src/middleware.ts`): Protects routes and handles authentication state

### Subscription System
- **Subscription Plans** (`src/lib/stripe/plans.ts`): Defines available subscription tiers
- **Stripe Server** (`src/lib/stripe/server.ts`): Server-side Stripe API functions
- **Stripe Client** (`src/lib/stripe/client.ts`): Client-side Stripe integration
- **Checkout API** (`src/app/api/stripe/checkout/route.ts`): Creates Stripe checkout sessions
- **Webhook Handler** (`src/app/api/stripe/webhook/route.ts`): Processes Stripe events
- **Billing Portal API** (`src/app/api/stripe/billing-portal/route.ts`): Manages subscription changes

### Visitor Management System
- **Pre-registration**: Allows hosts to pre-register visitors
- **Check-in Process**: Self-service or receptionist-assisted check-in
- **Returning Visitor Recognition**: Identifies returning visitors for quick check-in
- **Photo Capture**: Captures visitor photos during check-in
- **Digital Agreements**: Presents and records acceptance of agreements/NDAs
- **Check-out Process**: Records when visitors leave
- **Badge Generation**: Creates and prints visitor badges

### Visitor Recognition System
- **Visitor Recognition Service** (`src/lib/visitor/recognition.ts`): Core service for visitor matching and recognition
- **Returning Visitor Form** (`src/components/visitor/ReturningVisitorForm.tsx`): UI for returning visitors to quickly check in
- **Visitor Search Form** (`src/components/visitor/VisitorSearchForm.tsx`): Form for visitors to search for their existing profiles
- **Recognition Page** (`src/app/dashboard/visitors/check-in/recognize/page.tsx`): Page that handles the visitor recognition process

### Badge Management System
- **Badge Template Editor** (`src/components/visitor/badge/BadgeTemplateEditor.tsx`): UI for creating and editing badge templates
- **Badge Template List** (`src/components/visitor/badge/BadgeTemplateList.tsx`): UI for managing badge templates
- **Badge Generator** (`src/components/visitor/badge/BadgeGenerator.tsx`): Generates badges based on templates and visitor data
- **Badge Utilities** (`src/lib/utils/badge.ts`): Helper functions for badge generation and rendering

### Multi-Location Support System
- **Location Selector** (`src/components/location/LocationSelector.tsx`): UI for selecting locations with hierarchical view
- **Location Based Dashboard** (`src/components/location/LocationBasedDashboard.tsx`): Dashboard that filters data based on selected location
- **Location Form** (`src/components/admin/LocationForm.tsx`): Form for creating and editing locations
- **Location Group Form** (`src/components/admin/LocationGroupForm.tsx`): Form for creating and editing location groups
- **Location Management Page** (`src/app/dashboard/admin/locations/page.tsx`): Page for managing locations and location groups
- **Location Hierarchy** (`src/lib/admin/migrations/location_hierarchy.sql`): Database schema for hierarchical location structure
- **Location Permissions Form** (`src/components/admin/LocationPermissionsForm.tsx`): Form for managing user access to locations
- **Permissions Management Page** (`src/app/dashboard/admin/permissions/page.tsx`): Page for managing location-based permissions

### UI Components
- **Plan Selector** (`src/components/subscription/PlanSelector.tsx`): UI for selecting subscription plans
- **Checkout Button** (`src/components/subscription/CheckoutButton.tsx`): Initiates checkout process
- **Subscription Info** (`src/components/subscription/SubscriptionInfo.tsx`): Displays subscription details
- **Visitor Forms**: Components for visitor registration, check-in, and check-out

### Pages
- **Dashboard** (`src/app/dashboard/page.tsx`): Main user dashboard
- **Billing Page** (`src/app/dashboard/billing/page.tsx`): Subscription management
- **Subscription Page** (`src/app/subscription/page.tsx`): Plan selection after signup
- **Visitor Management Pages**: Pages for managing visitors, check-in/out, and badges
- **Badge Management Pages**: Pages for creating, editing, and managing badge templates

## Data Flow

1. **User Registration**:
   - User signs up on the signup page
   - Supabase creates user account
   - User is redirected to subscription selection

2. **Subscription Selection**:
   - User selects a subscription plan
   - Frontend creates a checkout session via API
   - User is redirected to Stripe Checkout

3. **Payment Processing**:
   - User completes payment on Stripe Checkout
   - Stripe sends webhook event to our webhook handler
   - Webhook handler updates subscription in database
   - User is redirected to dashboard

4. **Subscription Management**:
   - User can view subscription details on billing page
   - User can manage subscription via Stripe Billing Portal
   - Changes to subscription are processed via webhooks

5. **Visitor Check-in Flow**:
   - Visitor is pre-registered or walks in
   - Visitor completes check-in process (photo, agreements)
   - System generates visitor badge
   - Badge is printed for visitor
   - Visitor is checked in

6. **Multi-Location Management Flow**:
   - Admin creates location hierarchy (HQ, branches, etc.)
   - Admin assigns locations to location groups
   - Users select locations when viewing data
   - System filters data based on selected location
   - Reports can be generated for specific locations or across all locations

## External Dependencies

### Supabase
- **Authentication**: User signup, login, and session management
- **Database**: Stores user, organization, and subscription data
- **Storage**: Stores visitor photos and other assets
- **Environment Variables**:
  - `NEXT_PUBLIC_SUPABASE_URL`
  - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
  - `SUPABASE_SERVICE_ROLE_KEY`

### Stripe
- **Checkout**: Secure payment collection
- **Billing Portal**: Subscription management
- **Webhooks**: Event processing
- **Environment Variables**:
  - `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`
  - `STRIPE_SECRET_KEY`
  - `STRIPE_WEBHOOK_SECRET`

### Other Libraries
- **Lucide Icons**: UI icons
- **TypeScript**: Type definitions
- **Tailwind CSS**: Styling
- **UUID**: For generating unique identifiers

### Reporting and Analytics System
- **Reporting Service** (`src/lib/reporting/index.ts`): Core service for data aggregation and querying
- **Client Reporting** (`src/lib/reporting/client.ts`): Client-side reporting service factory
- **Server Reporting** (`src/lib/reporting/server.ts`): Server-side reporting service factory
- **Visitor Traffic Chart** (`src/components/reporting/VisitorTrafficChart.tsx`): Displays visitor traffic over time
- **Peak Time Heatmap** (`src/components/reporting/PeakTimeHeatmap.tsx`): Shows peak visitor times by day and hour
- **Visit Duration Chart** (`src/components/reporting/VisitDurationChart.tsx`): Analyzes visit duration metrics
- **Data Exporter** (`src/components/reporting/DataExporter.tsx`): Exports data in CSV format
- **Cross-Location Comparison** (`src/components/reporting/CrossLocationComparison.tsx`): Compares visitor traffic across locations
- **Location Visit Duration Comparison** (`src/components/reporting/LocationVisitDurationComparison.tsx`): Compares visit durations across locations
- **Reports Dashboard** (`src/app/dashboard/reports/ReportingDashboardClient.tsx`): Main reporting dashboard with tabbed interface

### Admin Dashboard System
- **Admin Service** (`src/lib/admin/index.ts`): Core service for admin operations
- **Admin Server** (`src/lib/admin/server.ts`): Server-side admin service factory
- **Admin Client** (`src/lib/admin/client.ts`): Client-side admin service factory
- **Admin Dashboard** (`src/app/dashboard/admin/page.tsx`): Main admin dashboard page
- **Admin Layout** (`src/app/dashboard/admin/layout.tsx`): Layout for admin pages with navigation
- **Organization Settings** (`src/app/dashboard/admin/organization/page.tsx`): Organization configuration page
- **User Management** (`src/app/dashboard/admin/users/page.tsx`): User and role management page
- **Location Management** (`src/app/dashboard/admin/locations/page.tsx`): Location management page
- **Custom Fields Management** (`src/app/dashboard/admin/fields/page.tsx`): Custom fields management page
- **Workflows Management** (`src/app/dashboard/admin/workflows/page.tsx`): Workflows management page
- **Branding Management** (`src/app/dashboard/admin/branding/page.tsx`): Branding customization page
- **Custom Field Form** (`src/components/admin/CustomFieldForm.tsx`): Form for creating and editing custom fields
- **Workflow Form** (`src/components/admin/WorkflowForm.tsx`): Form for creating and editing workflows
- **Branding Form** (`src/components/admin/BrandingForm.tsx`): Form for customizing branding elements

## Recent Significant Changes

### Cross-Location Reporting Implementation (Latest)
- Implemented comprehensive cross-location reporting system
  - Enhanced ReportingService with methods for cross-location data aggregation
  - Created CrossLocationComparison component for comparing visitor traffic across locations
  - Developed LocationVisitDurationComparison component for comparing visit durations across locations
  - Updated ReportingDashboardClient with new tabs for cross-location reports
- Added visualization options for location-based data
  - Implemented bar chart view for location comparison
  - Added pie chart view for location distribution
  - Created summary statistics for location-based metrics
  - Added time range filtering for all location-based reports
- Enhanced reporting UI for better user experience
  - Made tabs scrollable for better mobile experience
  - Added explanatory text for each report type
  - Improved chart responsiveness for different screen sizes
  - Standardized time range selectors across all reports

### Multi-Location Support Implementation (Previous)
- Implemented comprehensive multi-location support system
  - Created LocationSelector component with hierarchical view of locations
  - Developed LocationBasedDashboard component for location-filtered data display
  - Implemented LocationGroupForm for managing location groups
  - Enhanced location management page with hierarchical view and groups
  - Added location-specific settings and configurations
- Enhanced location data model
  - Updated Location interface with parent-child relationships
  - Added LocationGroup interface for grouping related locations
  - Created LocationHierarchy interface for representing nested locations
  - Implemented database schema for hierarchical location structure
- Improved location management UI
  - Added tabbed interface for locations and location groups
  - Implemented tree view for hierarchical location display
  - Added location group color coding and visual indicators
  - Enhanced location form with parent location selection
  - Implemented location activation/deactivation functionality
- Updated visitor management for multi-location support
  - Modified visitor check-in to support location selection
  - Updated visitor badges with location information
  - Added location-based permissions for visitor management
  - Implemented location-specific visitor policies
  - Enhanced visitor search and filtering with location context

### Host Dashboard and Approval Workflow Implementation (Previous)
- Implemented comprehensive host dashboard for expected visitors
  - Created ExpectedVisitorsClient component with tabbed interface for upcoming visits and pending approvals
  - Developed visit approval/rejection functionality with comments support
  - Implemented detailed visit information display with visitor details, purpose, location, and timing
  - Added status indicators for different visit states (pending, approved, rejected, etc.)
  - Created API routes for fetching host-specific visits and pending approvals
- Implemented visitor approval workflow system
  - Created VisitService with methods for managing approval requests and responses
  - Developed API routes for approval workflow management
  - Implemented approval workflow creation and configuration
  - Added support for multi-step approval processes with different approver types
  - Created visit statistics API for host performance metrics
- Enhanced navigation with dedicated host section
  - Updated DashboardNav component with host-specific section
  - Added links to host status and expected visitors pages
  - Improved organization of navigation items for better UX

### Host Availability Status Implementation (Previous)
- Implemented comprehensive host status management system
  - Created HostStatusService for managing host availability states
  - Developed HostStatusIndicator component for displaying host status
  - Implemented HostStatusSelector for hosts to update their status
  - Added HostStatusHistory component to track status changes
  - Created API routes for all host status operations
- Built host status page with full functionality
  - Status types (available, busy, out of office, in meeting, do not disturb)
  - Status duration settings (indefinite, until specific time, all day)
  - Status messages for additional context
  - Status history tracking
- Laid foundation for calendar integration
  - Created database schema for calendar integrations and events
  - Implemented service methods for managing calendar data
  - Added API routes for calendar operations

### Visitor Recognition Implementation (Previous)
- Implemented comprehensive visitor recognition system
  - Created VisitorRecognitionService with fuzzy matching capabilities
  - Developed confidence scoring algorithm for potential matches
  - Implemented visitor history retrieval functionality
  - Added support for matching based on email, phone, and name
- Built visitor recognition UI components
  - Created VisitorSearchForm for returning visitors to search for their profiles
  - Developed ReturningVisitorForm to display potential matches and enable quick check-in
  - Implemented recognition page to handle the matching process
  - Enhanced check-in page to include both new visitor and returning visitor flows
- Integrated visitor recognition with check-in process
  - Added quick check-in functionality for returning visitors
  - Implemented proper validation and error handling
  - Created seamless flow between recognition and regular check-in

### Customizable Fields and Workflows Implementation (Previous)
- Implemented comprehensive custom fields management
  - Created custom field management page with CRUD operations
  - Developed custom field form with support for various field types
  - Added validation rules for custom fields
  - Implemented field visibility and requirement settings
- Implemented workflow management system
  - Created workflow management page with CRUD operations
  - Developed workflow form with triggers, conditions, and actions
  - Added support for notification, email, SMS, and webhook actions
  - Implemented workflow status toggling
- Integrated customizable fields and workflows with admin dashboard
  - Added navigation links in admin sidebar
  - Connected to admin service for data operations
  - Implemented proper validation and error handling

### Admin Dashboard Implementation (Previous)
- Implemented comprehensive admin dashboard system
  - Created admin service for organization management operations
  - Developed admin dashboard with modular layout and navigation
  - Implemented organization settings management
  - Added user and role management functionality
  - Created types and interfaces for admin operations
- Built admin dashboard UI
  - Created admin layout with sidebar navigation
  - Implemented admin dashboard home page with module cards
  - Developed organization settings page with form controls
  - Created user management page with user list and role management
- Integrated admin system with main dashboard
  - Added admin access control based on user permissions
  - Created admin navigation link in main dashboard sidebar

### Reporting and Analytics Implementation (Previous)
- Implemented comprehensive reporting and analytics system
  - Created reporting service for data aggregation and querying
  - Developed visitor traffic analysis with time range filtering
  - Implemented peak time analysis with heatmap visualization
  - Created visit duration analysis with distribution charts
  - Added data export functionality for CSV downloads
- Built reporting dashboard with tabbed interface
  - Created visitor traffic chart component with time range selection
  - Developed peak time heatmap component for day/hour analysis
  - Implemented visit duration chart component with pie and bar charts
  - Added data exporter component for CSV downloads
- Integrated reporting system with visitor management
  - Connected reporting components to visitor data
  - Implemented proper data filtering and aggregation
  - Added explanatory content for each report type

### Host Notification System Implementation (Previous)
- Implemented comprehensive notification system
  - Created notification types and interfaces for email, SMS, and in-app notifications
  - Developed notification service for sending and managing notifications
  - Implemented notification preferences management
  - Created notification center UI for displaying in-app notifications
  - Added notification triggers for various visitor events (check-in, check-out, etc.)
- Added notification settings page
  - Created UI for managing notification preferences
  - Implemented per-notification-type channel preferences
- Integrated notification system with visitor management
  - Added notification triggers for visitor pre-registration, check-in, check-out, etc.
  - Implemented real-time notification display in the UI

### Visitor Dashboard Enhancements (Previous)
- Implemented advanced filtering and search functionality
  - Created VisitorFilter component with status, date range, host, and location filters
  - Developed VisitorSearch component with real-time search and search history
  - Built VisitorList component with pagination and responsive design
- Enhanced visitor management workflow
  - Added comprehensive visitor history view with export options
  - Improved visitor dashboard UI/UX with better data visualization
  - Added real-time statistics for visitor management
- Integrated components into visitor management pages
  - Updated main visitors dashboard page with filtering and search
  - Created dedicated visitor history page with advanced filtering

### Badge Generation Implementation (Previous)
- Implemented complete visitor badge generation functionality
  - Created badge template data types and interfaces
  - Developed badge template editor with drag-and-drop field positioning
  - Implemented badge template management (create, edit, delete, set default)
  - Created badge generator component for rendering badges
  - Added badge printing functionality
- Integrated badge generation with visitor check-in process
  - Automatically generates badge after successful check-in
  - Added badge printing option in visitor management dashboard
- Created badge management pages
  - Added badge templates list page
  - Added badge template creation and editing pages
  - Added badge viewing and printing page

### Visitor Check-in/Check-out Implementation (Previous)
- Implemented complete visitor check-in and check-out functionality
  - Created VisitorCheckInForm component with photo capture and agreement signing
  - Created VisitorCheckOutForm component for visitor check-out
  - Added check-in and check-out pages with proper routing
- Enhanced visitor management workflow
  - Pre-registration → Check-in → Check-out process
  - Support for both pre-registered and walk-in visitors
  - Digital agreement signing during check-in
  - Photo capture during check-in
- Added proper validation and error handling throughout the visitor journey

### Visitor Management Implementation (Previous)
- Enhanced database schema for visitor management
  - Added visitors, agreements, badges, and visit_agreements tables
  - Updated existing visits table with additional fields
- Created visitor pre-registration form component
- Implemented visitor management pages
  - Added visitors index page with upcoming visits list
  - Added visitor pre-registration page
- Updated dashboard navigation to include visitor management links

### Stripe Integration (Previous)
- Added subscription plans with tiered pricing
- Implemented Stripe checkout process
- Created webhook handler for subscription events
- Added subscription management interface
- Updated signup flow to include plan selection

### Authentication System (Earlier)
- Implemented Supabase authentication
- Created signup and login pages
- Added middleware for route protection
- Set up organization creation during signup

## User Feedback Integration

No user feedback has been integrated yet, as the application is still in development. Future feedback will be documented here along with the changes made in response.
