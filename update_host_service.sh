#!/bin/bash

# Find all API route files that use HostService
files=$(grep -l "new HostService()" src/app/api/host/**/route.ts)

# Update each file
for file in $files; do
  echo "Updating $file..."
  
  # Replace the import
  sed -i '' 's/import { HostService } from/import { createServerHostService } from/g' "$file"
  
  # Replace the instantiation
  sed -i '' 's/const hostService = new HostService()/const hostService = await createServerHostService()/g' "$file"
done

echo "Done!"
