# Supabase Database Migrations

This directory contains the database migrations for the Visitor Management SaaS application.

## Migration Files

The migrations are organized in numbered files to ensure they run in the correct order:

1. `00001_create_core_tables.sql` - Creates the core tables (organizations, users, locations, visitors, visits, etc.)
2. `00002_create_host_tables.sql` - Creates tables for host status management and calendar integrations
3. `00003_create_admin_tables.sql` - Creates tables for admin features (organization settings, user roles, etc.)
4. `00004_create_location_hierarchy.sql` - Adds location hierarchy and related tables
5. `00005_create_visit_approvals.sql` - Creates tables for visit approval workflows
6. `00006_create_notification_tables.sql` - Creates tables for notifications and preferences

## Running Migrations

### Local Development

To run migrations locally:

```bash
# Start Supabase locally
supabase start

# Apply migrations
supabase db reset
```

This will apply all migrations and seed data.

### Production

To apply migrations to your production Supabase project:

```bash
# Link to your Supabase project
supabase link --project-ref your-project-ref

# Push migrations to production
supabase db push
```

Replace `your-project-ref` with your actual Supabase project reference.

## Seed Data

The `seed.sql` file is currently empty. No default data is created automatically.

When a user signs up, they will be prompted to create an organization, and the necessary records will be created automatically.

## Manual Setup After Migration

After running the migrations, you can either:

1. **Use the application normally**:
   - Sign up through the application
   - Create an organization during the signup process
   - All necessary records will be created automatically

2. **Or manually set up a user and organization**:
   - Create a user through Supabase Auth (in the Supabase Dashboard)
   - Get your Supabase Auth user ID from the Supabase Dashboard (Authentication > Users)
   - Edit the `setup_user.sql` file and replace all placeholders with your actual values:
     - `your-org-id`: Generate a UUID or use a specific ID
     - `Your Organization Name`: Your organization name
     - `your-auth-user-id`: Your Supabase Auth user ID
     - `<EMAIL>`: Your email
   - Run the setup script:

```bash
# For local development
supabase db reset -f supabase/setup_user.sql

# For production
psql "$(supabase db remote connection-string)" -f supabase/setup_user.sql
```

This will:
- Create an organization
- Link your user to the organization
- Create necessary records in the `user_organizations` table for RLS policies
- Set up default notification preferences

## Row Level Security (RLS)

All tables have Row Level Security (RLS) policies applied to ensure data security. These policies restrict access based on the user's organization and role.

## Troubleshooting

If you encounter issues with the migrations:

1. Check the Supabase logs for error messages
2. Ensure you have the latest version of the Supabase CLI
3. Verify that your Supabase project has the necessary extensions enabled (uuid-ossp)
4. Make sure your Supabase Auth is properly configured
