-- Add new columns to locations table
ALTER TABLE locations ADD COLUMN IF NOT EXISTS parent_id UUID REFERENCES locations(id) ON DELETE SET NULL;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS location_type TEXT NOT NULL DEFAULT 'office';
ALTER TABLE locations ADD COLUMN IF NOT EXISTS is_active BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS capacity INTEGER;

-- Create location_groups table
CREATE TABLE IF NOT EXISTS location_groups (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  color TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add group_id to locations table
ALTER TABLE locations ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES location_groups(id) ON DELETE SET NULL;

-- Create visitor_policies table
CREATE TABLE IF NOT EXISTS visitor_policies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  location_id UUID NOT NULL REFERENCES locations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  rules JSONB NOT NULL DEFAULT '[]',
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create access_restrictions table
CREATE TABLE IF NOT EXISTS access_restrictions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  location_id UUID NOT NULL REFERENCES locations(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  rule TEXT NOT NULL,
  message TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Update location_settings to include new fields
ALTER TABLE locations
  DROP COLUMN IF EXISTS settings,
  ADD COLUMN IF NOT EXISTS settings JSONB NOT NULL DEFAULT '{
    "require_host_approval": true,
    "allow_walk_ins": true,
    "require_photo": true,
    "require_id": false,
    "check_in_methods": ["self", "receptionist"],
    "default_check_in_method": "self"
  }';

-- Create location_permissions table for fine-grained access control
CREATE TABLE IF NOT EXISTS location_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  location_id UUID NOT NULL REFERENCES locations(id) ON DELETE CASCADE,
  permission TEXT NOT NULL, -- 'admin', 'manager', 'staff', 'host', 'viewer'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, location_id, permission)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_locations_parent_id ON locations(parent_id);
CREATE INDEX IF NOT EXISTS idx_locations_group_id ON locations(group_id);
CREATE INDEX IF NOT EXISTS idx_location_groups_org_id ON location_groups(org_id);
CREATE INDEX IF NOT EXISTS idx_visitor_policies_location_id ON visitor_policies(location_id);
CREATE INDEX IF NOT EXISTS idx_access_restrictions_location_id ON access_restrictions(location_id);
CREATE INDEX IF NOT EXISTS idx_location_permissions_user_id ON location_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_location_permissions_location_id ON location_permissions(location_id);

-- Create triggers for updated_at columns
CREATE TRIGGER update_location_groups_updated_at
BEFORE UPDATE ON location_groups
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_visitor_policies_updated_at
BEFORE UPDATE ON visitor_policies
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_access_restrictions_updated_at
BEFORE UPDATE ON access_restrictions
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_location_permissions_updated_at
BEFORE UPDATE ON location_permissions
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE location_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE visitor_policies ENABLE ROW LEVEL SECURITY;
ALTER TABLE access_restrictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE location_permissions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Location groups policies
CREATE POLICY "Users can view location groups for their organization" ON location_groups
  FOR SELECT USING (
    org_id = get_auth_user_org_id()
  );

-- Visitor policies policies
CREATE POLICY "Users can view visitor policies for locations in their organization" ON visitor_policies
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM locations
      WHERE locations.id = visitor_policies.location_id
      AND locations.org_id = get_auth_user_org_id()
    )
  );

-- Access restrictions policies
CREATE POLICY "Users can view access restrictions for locations in their organization" ON access_restrictions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM locations
      WHERE locations.id = access_restrictions.location_id
      AND locations.org_id = get_auth_user_org_id()
    )
  );

-- Location permissions policies
CREATE POLICY "Users can view location permissions for their organization" ON location_permissions
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM locations
      WHERE locations.id = location_permissions.location_id
      AND locations.org_id = get_auth_user_org_id()
      AND EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid()
        AND users.role IN ('admin', 'manager')
      )
    )
  );
