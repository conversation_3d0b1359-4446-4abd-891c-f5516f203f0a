-- Host Status Management Tables

-- Table for storing current host statuses
CREATE TABLE IF NOT EXISTS host_statuses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status VARCHAR(50) NOT NULL CHECK (status IN ('available', 'busy', 'out_of_office', 'in_meeting', 'do_not_disturb')),
  message TEXT,
  duration_type VARCHAR(20) NOT NULL CHECK (duration_type IN ('indefinite', 'until_time', 'all_day')),
  end_time TIMESTAMP WITH TIME ZONE,
  end_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Constraints
  CONSTRAINT valid_duration_end_time CHECK (
    (duration_type = 'until_time' AND end_time IS NOT NULL) OR
    (duration_type != 'until_time' AND end_time IS NULL)
  ),
  CONSTRAINT valid_duration_end_date CHECK (
    (duration_type = 'all_day' AND end_date IS NOT NULL) OR
    (duration_type != 'all_day' AND end_date IS NULL)
  )
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_host_statuses_user_id ON host_statuses(user_id);

-- Table for storing host status history
CREATE TABLE IF NOT EXISTS host_status_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status VARCHAR(50) NOT NULL,
  message TEXT,
  duration_type VARCHAR(20) NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  end_date DATE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL,
  ended_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_host_status_history_user_id ON host_status_history(user_id);
CREATE INDEX IF NOT EXISTS idx_host_status_history_created_at ON host_status_history(created_at);

-- Table for storing calendar integrations
CREATE TABLE IF NOT EXISTS calendar_integrations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  provider VARCHAR(50) NOT NULL CHECK (provider IN ('google', 'outlook', 'ical')),
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  sync_frequency VARCHAR(20) NOT NULL CHECK (sync_frequency IN ('realtime', 'hourly', 'daily')),
  last_synced_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Unique constraint to prevent duplicate integrations for the same user and provider
  CONSTRAINT unique_user_provider UNIQUE (user_id, provider)
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_calendar_integrations_user_id ON calendar_integrations(user_id);

-- Table for storing calendar events
CREATE TABLE IF NOT EXISTS calendar_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  calendar_integration_id UUID NOT NULL REFERENCES calendar_integrations(id) ON DELETE CASCADE,
  external_event_id VARCHAR(255) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  location TEXT,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  is_all_day BOOLEAN DEFAULT FALSE NOT NULL,
  status VARCHAR(20) NOT NULL CHECK (status IN ('confirmed', 'tentative', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Unique constraint to prevent duplicate events
  CONSTRAINT unique_external_event UNIQUE (calendar_integration_id, external_event_id)
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_calendar_events_user_id ON calendar_events(user_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_integration_id ON calendar_events(calendar_integration_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_start_time ON calendar_events(start_time);
CREATE INDEX IF NOT EXISTS idx_calendar_events_end_time ON calendar_events(end_time);

-- Create RLS policies for host_statuses
ALTER TABLE host_statuses ENABLE ROW LEVEL SECURITY;

CREATE POLICY host_statuses_select_policy ON host_statuses
  FOR SELECT USING (
    auth.uid() = user_id OR
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = host_statuses.user_id
      AND users.org_id = get_auth_user_org_id()
    )
  );

CREATE POLICY host_statuses_insert_policy ON host_statuses
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY host_statuses_update_policy ON host_statuses
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY host_statuses_delete_policy ON host_statuses
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for host_status_history
ALTER TABLE host_status_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY host_status_history_select_policy ON host_status_history
  FOR SELECT USING (
    auth.uid() = user_id OR
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = host_status_history.user_id
      AND users.org_id = get_auth_user_org_id()
    )
  );

-- Create RLS policies for calendar_integrations
ALTER TABLE calendar_integrations ENABLE ROW LEVEL SECURITY;

CREATE POLICY calendar_integrations_select_policy ON calendar_integrations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY calendar_integrations_insert_policy ON calendar_integrations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY calendar_integrations_update_policy ON calendar_integrations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY calendar_integrations_delete_policy ON calendar_integrations
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for calendar_events
ALTER TABLE calendar_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY calendar_events_select_policy ON calendar_events
  FOR SELECT USING (
    auth.uid() = user_id OR
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = calendar_events.user_id
      AND users.org_id = get_auth_user_org_id()
    )
  );

CREATE POLICY calendar_events_insert_policy ON calendar_events
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY calendar_events_update_policy ON calendar_events
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY calendar_events_delete_policy ON calendar_events
  FOR DELETE USING (auth.uid() = user_id);

-- Create function to update host_status_history when a status is changed or deleted
CREATE OR REPLACE FUNCTION update_host_status_history()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'DELETE' THEN
    INSERT INTO host_status_history (
      user_id, status, message, duration_type, end_time, end_date, created_at, ended_at
    ) VALUES (
      OLD.user_id, OLD.status, OLD.message, OLD.duration_type, OLD.end_time, OLD.end_date, OLD.created_at, NOW()
    );
    RETURN OLD;
  ELSIF TG_OP = 'UPDATE' THEN
    IF OLD.status != NEW.status OR OLD.message != NEW.message OR OLD.duration_type != NEW.duration_type OR
       OLD.end_time != NEW.end_time OR OLD.end_date != NEW.end_date THEN
      INSERT INTO host_status_history (
        user_id, status, message, duration_type, end_time, end_date, created_at, ended_at
      ) VALUES (
        OLD.user_id, OLD.status, OLD.message, OLD.duration_type, OLD.end_time, OLD.end_date, OLD.created_at, NOW()
      );
    END IF;
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update host_status_history
CREATE TRIGGER host_status_update_history
BEFORE UPDATE ON host_statuses
FOR EACH ROW EXECUTE FUNCTION update_host_status_history();

CREATE TRIGGER host_status_delete_history
BEFORE DELETE ON host_statuses
FOR EACH ROW EXECUTE FUNCTION update_host_status_history();

-- Create triggers to update updated_at timestamp
CREATE TRIGGER update_host_statuses_updated_at
BEFORE UPDATE ON host_statuses
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_calendar_integrations_updated_at
BEFORE UPDATE ON calendar_integrations
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_calendar_events_updated_at
BEFORE UPDATE ON calendar_events
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
