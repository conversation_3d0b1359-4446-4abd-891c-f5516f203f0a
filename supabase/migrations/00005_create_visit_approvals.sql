-- Visit Approvals Table
CREATE TABLE IF NOT EXISTS visit_approvals (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  visit_id UUID NOT NULL REFERENCES visits(id) ON DELETE CASCADE,
  approver_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT NOT NULL CHECK (status IN ('pending', 'approved', 'rejected')),
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (visit_id, approver_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_visit_approvals_visit_id ON visit_approvals(visit_id);
CREATE INDEX IF NOT EXISTS idx_visit_approvals_approver_id ON visit_approvals(approver_id);
CREATE INDEX IF NOT EXISTS idx_visit_approvals_status ON visit_approvals(status);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_visit_approvals_updated_at
BEFORE UPDATE ON visit_approvals
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies
ALTER TABLE visit_approvals ENABLE ROW LEVEL SECURITY;

-- Policy for select: users can see approvals for visits where they are the host or approver
CREATE POLICY select_visit_approvals ON visit_approvals
  FOR SELECT
  USING (
    approver_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM visits
      WHERE visits.id = visit_id AND visits.host_id = auth.uid()
    )
  );

-- Policy for insert: users can only create approvals where they are the approver
CREATE POLICY insert_visit_approvals ON visit_approvals
  FOR INSERT
  WITH CHECK (approver_id = auth.uid());

-- Policy for update: users can only update their own approvals
CREATE POLICY update_visit_approvals ON visit_approvals
  FOR UPDATE
  USING (approver_id = auth.uid());

-- Policy for delete: users can only delete their own approvals
CREATE POLICY delete_visit_approvals ON visit_approvals
  FOR DELETE
  USING (approver_id = auth.uid());

-- Add approval_required column to visits table if it doesn't exist
ALTER TABLE visits ADD COLUMN IF NOT EXISTS approval_required BOOLEAN NOT NULL DEFAULT TRUE;

-- Add approval_workflow_id column to visits table if it doesn't exist
ALTER TABLE visits ADD COLUMN IF NOT EXISTS approval_workflow_id UUID NULL;

-- Create approval_workflows table if it doesn't exist
CREATE TABLE IF NOT EXISTS approval_workflows (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_approval_workflows_organization_id ON approval_workflows(organization_id);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_approval_workflows_updated_at
BEFORE UPDATE ON approval_workflows
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies
ALTER TABLE approval_workflows ENABLE ROW LEVEL SECURITY;

-- Create user_organizations table if it doesn't exist (for RLS policies)
CREATE TABLE IF NOT EXISTS user_organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (user_id, organization_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_organizations_user_id ON user_organizations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_organizations_organization_id ON user_organizations(organization_id);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_user_organizations_updated_at
BEFORE UPDATE ON user_organizations
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;

-- Policy for select: users can see their own organization memberships
CREATE POLICY select_user_organizations ON user_organizations
  FOR SELECT
  USING (user_id = auth.uid());

-- Policy for select: users can see approval workflows for their organization
CREATE POLICY select_approval_workflows ON approval_workflows
  FOR SELECT
  USING (
    organization_id = get_auth_user_org_id()
  );

-- Policy for insert: users can only create approval workflows for their organization if they are admins
CREATE POLICY insert_approval_workflows ON approval_workflows
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_organizations
      WHERE user_organizations.user_id = auth.uid()
      AND user_organizations.organization_id = organization_id
      AND user_organizations.role = 'admin'
    )
  );

-- Policy for update: users can only update approval workflows for their organization if they are admins
CREATE POLICY update_approval_workflows ON approval_workflows
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM user_organizations
      WHERE user_organizations.user_id = auth.uid()
      AND user_organizations.organization_id = approval_workflows.organization_id
      AND user_organizations.role = 'admin'
    )
  );

-- Policy for delete: users can only delete approval workflows for their organization if they are admins
CREATE POLICY delete_approval_workflows ON approval_workflows
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM user_organizations
      WHERE user_organizations.user_id = auth.uid()
      AND user_organizations.organization_id = approval_workflows.organization_id
      AND user_organizations.role = 'admin'
    )
  );

-- Create approval_workflow_steps table if it doesn't exist
CREATE TABLE IF NOT EXISTS approval_workflow_steps (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  workflow_id UUID NOT NULL REFERENCES approval_workflows(id) ON DELETE CASCADE,
  step_number INTEGER NOT NULL,
  approver_type TEXT NOT NULL CHECK (approver_type IN ('user', 'role', 'department')),
  approver_id TEXT NOT NULL,
  is_required BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (workflow_id, step_number)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_approval_workflow_steps_workflow_id ON approval_workflow_steps(workflow_id);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_approval_workflow_steps_updated_at
BEFORE UPDATE ON approval_workflow_steps
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies
ALTER TABLE approval_workflow_steps ENABLE ROW LEVEL SECURITY;

-- Policy for select: users can see approval workflow steps for their organization
CREATE POLICY select_approval_workflow_steps ON approval_workflow_steps
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM approval_workflows
      WHERE approval_workflows.id = approval_workflow_steps.workflow_id
      AND approval_workflows.organization_id = get_auth_user_org_id()
    )
  );

-- Policy for insert: users can only create approval workflow steps for their organization if they are admins
CREATE POLICY insert_approval_workflow_steps ON approval_workflow_steps
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM approval_workflows
      WHERE approval_workflows.id = workflow_id
      AND approval_workflows.organization_id = get_auth_user_org_id()
      AND EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid()
        AND users.role = 'admin'
      )
    )
  );

-- Policy for update: users can only update approval workflow steps for their organization if they are admins
CREATE POLICY update_approval_workflow_steps ON approval_workflow_steps
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM approval_workflows
      WHERE approval_workflows.id = approval_workflow_steps.workflow_id
      AND approval_workflows.organization_id = get_auth_user_org_id()
      AND EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid()
        AND users.role = 'admin'
      )
    )
  );

-- Policy for delete: users can only delete approval workflow steps for their organization if they are admins
CREATE POLICY delete_approval_workflow_steps ON approval_workflow_steps
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM approval_workflows
      WHERE approval_workflows.id = approval_workflow_steps.workflow_id
      AND approval_workflows.organization_id = get_auth_user_org_id()
      AND EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid()
        AND users.role = 'admin'
      )
    )
  );
