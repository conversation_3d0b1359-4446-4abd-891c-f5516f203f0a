-- This file is for manual setup of a user and organization
-- Replace the placeholders with your actual values

-- First, create an organization
INSERT INTO organizations (id, name, created_at, updated_at)
VALUES (
  'your-org-id',  -- Generate a UUID or use a specific ID
  'Your Organization Name',  -- Your organization name
  NOW(),
  NOW()
);

-- Insert a user record linked to the organization
INSERT INTO users (id, org_id, full_name, email, role)
VALUES (
  'your-auth-user-id',  -- Replace with your actual Supabase Auth user ID
  'your-org-id',  -- Use the same organization ID from above
  'Admin User',  -- Your name
  '<EMAIL>',  -- Your email
  'admin'  -- Role (admin, manager, host, receptionist)
);

-- Insert a record in user_organizations table (needed for RLS policies)
INSERT INTO user_organizations (user_id, organization_id, role)
VALUES (
  'your-auth-user-id',  -- Replace with your actual Supabase Auth user ID
  'your-org-id',  -- Use the same organization ID from above
  'admin'  -- Role
);

-- Create default notification preferences for the user
INSERT INTO notification_preferences
  (user_id, org_id, notification_type, email_enabled, sms_enabled, in_app_enabled)
VALUES
  ('your-auth-user-id', 'your-org-id', 'visitor_preregistered', true, false, true),
  ('your-auth-user-id', 'your-org-id', 'visitor_checked_in', true, false, true),
  ('your-auth-user-id', 'your-org-id', 'visitor_checked_out', true, false, true),
  ('your-auth-user-id', 'your-org-id', 'visit_cancelled', true, false, true),
  ('your-auth-user-id', 'your-org-id', 'visit_rescheduled', true, false, true),
  ('your-auth-user-id', 'your-org-id', 'visit_reminder', true, false, true);
